package info.qizhi.aflower.module.pms.service.report;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberListReqDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentReqDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo.CashBillOrderReqVO;
import info.qizhi.aflower.module.pms.controller.admin.channel.vo.ChannelReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.log.OrderLogReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.order.OrderReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.price.OrderPriceReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherReqVO;
import info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo.ProtocolAgentReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.*;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import info.qizhi.aflower.module.pms.dal.dataobject.cashbillorder.CashBillOrderDO;
import info.qizhi.aflower.module.pms.dal.dataobject.channel.ChannelDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GeneralConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderLogDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderPriceDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO;
import info.qizhi.aflower.module.pms.dal.dataobject.protocolagent.ProtocolAgentDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.service.account.AccountService;
import info.qizhi.aflower.module.pms.service.cashbillorder.CashBillOrderService;
import info.qizhi.aflower.module.pms.service.channel.ChannelService;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import info.qizhi.aflower.module.pms.service.order.OrderLogService;
import info.qizhi.aflower.module.pms.service.order.OrderPriceService;
import info.qizhi.aflower.module.pms.service.order.OrderService;
import info.qizhi.aflower.module.pms.service.order.OrderTogetherService;
import info.qizhi.aflower.module.pms.service.protocolagent.ProtocolAgentService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import info.qizhi.aflower.module.system.api.user.AdminUserApi;
import info.qizhi.aflower.module.system.api.user.dto.UserSimpleRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.enums.ConsumeAccountEnum.*;
import static info.qizhi.aflower.framework.common.enums.PayAccountEnum.*;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Service
@Validated
public class AccountDetailServiceImpl implements AccountDetailService {
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private AccountService accountService;
    @Resource
    private OrderService orderService;
    @Resource
    private OrderTogetherService orderTogetherService;
    @Resource
    private ChannelService channelService;
    @Resource
    private ProtocolAgentService protocolAgentService;
    @Resource
    private GeneralConfigService generalConfigService;
    @Resource
    private CashBillOrderService cashBillOrderService;
    @Resource
    private OrderPriceService orderpriceService;
    @Resource
    private AdminUserApi userApi;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private OrderLogService orderLogService;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private MemberApi memberApi;
    @Resource
    private RoomTypeService roomTypeService;

    private static final String CASH_NAME = "现付账";
    private static final BigDecimal HUNDRED = new BigDecimal(100);
    private static final String OFFER_PRICE = "放盘价";
    private static final String HAND_PRICE = "手工价";

    @Override
    public PayAndConsumeDetailReportRespVO getPayDetailReport(PayOrConsumeDetailReqVO reqVO) {
        PayAndConsumeDetailReportRespVO payAndConsumeDetailReportRespVO = new PayAndConsumeDetailReportRespVO();
        // 获取科目为付款的所有账务
        List<AccountDO> payAccountList = accountService.getPayOrConsumeAccountList(reqVO.setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode()));
        if (CollUtil.isEmpty(payAccountList)){
            return payAndConsumeDetailReportRespVO;
        }
        // 排除所有预授权类型账务
        List<AccountDO> filteredAccountDOList = payAccountList.stream()
                .filter(account -> !Set.of(
                        PayAccountEnum.BANK_PRE_AUTH.getCode(),
                        PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()
                ).contains(account.getSubCode()))
                .toList();

        return getDetaileReport(reqVO, payAndConsumeDetailReportRespVO, filteredAccountDOList, null);
    }

    @Override
    public PayAndConsumeDetailReportRespVO getConsumeDetailReport(PayOrConsumeDetailReqVO reqVO) {
        PayAndConsumeDetailReportRespVO payAndConsumeDetailReportRespVO = new PayAndConsumeDetailReportRespVO();
        // 获取科目为消费的所有账务
        List<AccountDO> consumeAccountList = accountService.getPayOrConsumeAccountList(reqVO.setSubType(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode()));
        if (CollUtil.isEmpty(consumeAccountList)){
            return payAndConsumeDetailReportRespVO;
        }
        List<GeneralConfigDO> consumeConfigList = generalConfigService.getGeneralConfigList(new GeneralConfigReqVO().setParentCode(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getCode())
                .setGcode(reqVO.getGcode())
                .setType(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode())
                .setIsEnable(BooleanEnum.TRUE.getValue()));
        Map<String, GeneralConfigDO> consumeNameMap = CollectionUtils.convertMap(consumeConfigList, GeneralConfigDO::getCode);
        return getDetaileReport(reqVO, payAndConsumeDetailReportRespVO, consumeAccountList, consumeNameMap);
    }

    @Override
    public TransactionDetailReportRespVO getTransactionReport(TransactionDetailReqVO reqVO) {
        TransactionDetailReportRespVO transactionDetailReportRespVO=new TransactionDetailReportRespVO();
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        transactionDetailReportRespVO.setHname(merchant.getHname())
                .setLastSelectTime(LocalDateTime.now())
                .setList(new ArrayList<>());

        if(reqVO.getStartDate() != null && reqVO.getEndDate() != null){
            transactionDetailReportRespVO
                    .setStartDate(convertStringToLocalDate(reqVO.getStartDate(), reqVO.getTimeType()))
                    .setEndDate(convertStringToLocalDate(reqVO.getEndDate(), reqVO.getTimeType()));
        }else {
            transactionDetailReportRespVO
                    .setStartDate(reqVO.getBizDate())
                    .setEndDate(reqVO.getBizDate());
        }
        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        List<AccountDO> transactionReport = accountService.getTransactionReport(reqVO);
        List<String> refundAccNo = CollectionUtils.convertList(transactionReport, AccountDO::getRefundAccNo);
        transactionDetailReportRespVO.setList(buildTransactionDetailRespVOList(transactionReport, nickNameMap));
        return transactionDetailReportRespVO;
    }

    @Override
    public SubjectSummaryReportRespVO getSubjectSummaryReport(SubjectSummaryReqVO reqVO) {
        SubjectSummaryReportRespVO subjectSummaryReportRespVO = new SubjectSummaryReportRespVO();
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        subjectSummaryReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setBizDate(reqVO.getBizDate())
                .setStartDate(reqVO.getBizDate().with(TemporalAdjusters.firstDayOfMonth()))
                .setLastSelectTime(LocalDateTime.now())
                .setPaymentTodayFee(0L)
                .setPaymentMonthFee(0L)
                .setConsumptionTodayFee(0L)
                .setConsumptionMonthFee(0L);
        // 获得账务列表
        List<AccountDO> accountList = accountService.getAccountListForReport(new AccountListReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(subjectSummaryReportRespVO.getStartDate()).setEndDate(reqVO.getBizDate()));

        // 获得会员充值账务
        List<CashBillOrderDO> cashBillOrderList = cashBillOrderService.getCashBillOrderList(new CashBillOrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(subjectSummaryReportRespVO.getStartDate()).setEndDate(reqVO.getBizDate()).setSubCode(MEMBER_RECHARGE.getCode()));

        List<String> list = CollectionUtils.convertList(cashBillOrderList, CashBillOrderDO::getCashBillOrderNo);
        List<String> accountNoS = list !=null ? list : new ArrayList<>();

        // 获得非会员充值账务集合
        List<AccountDO> accountS = CollectionUtils.filterList(accountList, account -> !accountNoS.contains(account.getNo())
                && !BANK_PRE_AUTH.getCode().equals(account.getSubCode()) && !SCAN_GUN_PRE_AUTH.getCode().equals(account.getSubCode()));
        // 获得会员充值账务集合
        List<AccountDO> memberRechargeAccountList = CollectionUtils.filterList(accountList, account -> accountNoS.contains(account.getNo()));
        // 处理门店账务
        handleAccount(reqVO, accountS, subjectSummaryReportRespVO);
        // 处理会员充值账务
        handleMemberRechargeAccount(reqVO, memberRechargeAccountList, subjectSummaryReportRespVO);

        return subjectSummaryReportRespVO;
    }

    @Override
    public RedAndAdjustReportRespVO getRedAndAdjustReport(RedAndAdjustReqVO reqVO) {
        RedAndAdjustReportRespVO redAndAdjustReportRespVO =  new RedAndAdjustReportRespVO();
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        redAndAdjustReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setEndDate(reqVO.getEndDate())
                .setStartDate(reqVO.getStartDate())
                .setLastSelectTime(LocalDateTime.now());

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        Map<String, String> dictDataLabelMap = dictDataApi.getDictDataLabelMap(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode()));

        List<AccountDO> accountList = accountService.getAccountList(new AccountListReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setRecorder(reqVO.getRecorder()).setIsRev(NumberEnum.ONE.getNumber()).setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate()));

        List<RedAndAdjustRespVO> list = new ArrayList<>();
        accountList.forEach(account -> {
            RedAndAdjustRespVO redAndAdjustRespVO =BeanUtils.toBean(account, RedAndAdjustRespVO.class);
            redAndAdjustRespVO.setRecorderName(nickNameMap.getOrDefault(account.getRecorder(), account.getRecorder()));
            redAndAdjustRespVO.setSubName(dictDataLabelMap.getOrDefault(account.getSubCode(), account.getSubType()));

            String url = String.format(
                    OrderUrlEnum.URL.getCode(),
                    account.getNo(),
                    NoTypeEnum.ORDER.getCode()
            );
            redAndAdjustRespVO.setUrl(url);

            if(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(account.getSubType())){
                redAndAdjustRespVO.setPayFee(account.getFee());
                redAndAdjustRespVO.setConsumeFee(0L);
            }else {
                redAndAdjustRespVO.setConsumeFee(account.getFee());
                redAndAdjustRespVO.setPayFee(0L);
            }

            if(AccountTypeEnum.CASH.getCode().equals(account.getAccType())){
                redAndAdjustRespVO.setGuestName(CASH_NAME);
                redAndAdjustRespVO.setUrl("");
            }

            list.add(redAndAdjustRespVO);
        });

        return redAndAdjustReportRespVO.setList(list);
    }

    @Override
    public TransferAccountReportRespVO getTransferAccountReport(TransferAccountReqVO reqVO) {
        TransferAccountReportRespVO transferAccountReportRespVO =  new TransferAccountReportRespVO();
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        transferAccountReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setEndDate(reqVO.getEndDate())
                .setStartDate(reqVO.getStartDate())
                .setLastSelectTime(LocalDateTime.now());

        List<AccountDO> accountList = accountService.getAccountChangeList(reqVO.setState(NumberEnum.ONE.getNumber()));
        if(CollUtil.isEmpty(accountList)){
            return transferAccountReportRespVO.setList(new ArrayList<>());
        }

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        Map<String, String> dictDataLabelMap = dictDataApi.getDictDataLabelMap(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode()));

        // 获得转入账务
        List<AccountDO> turnInAccountDOS = CollectionUtils.filterList(accountList, account -> NumberEnum.ONE.getNumber().equals(account.getIsTurnOutIn()));
        List<String> orderNos = CollectionUtils.convertList(turnInAccountDOS, AccountDO::getNo);

        // 获得订单列表
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setOrderNos(orderNos));
        Map<String, OrderDO> orderDOMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
        // 获得转出账务
        List<AccountDO> turnOutAccountDOS = CollectionUtils.filterList(accountList, account -> NumberEnum.MINUS.getNumber().equals(account.getIsTurnOutIn()));
        Map<String, AccountDO> turnOutAccountDOMap = CollectionUtils.convertMap(turnOutAccountDOS, AccountDO::getNo);

        List<TransferAccountRespVO> list = new ArrayList<>();
        turnInAccountDOS.forEach(account -> {
            TransferAccountRespVO transferAccount = new TransferAccountRespVO();
            OrderDO order = orderDOMap.getOrDefault(account.getNo(), new OrderDO());
            transferAccount.setCheckinTime(order.getCheckinTime());
            transferAccount.setCheckoutTime(order.getCheckoutTime());
            transferAccount.setSubCode(account.getSubCode());
            transferAccount.setSubName(dictDataLabelMap.getOrDefault(account.getSubCode(), account.getSubCode()));
            transferAccount.setBizDate(account.getBizDate());
            transferAccount.setCreateTime(account.getCreateTime());
            transferAccount.setFee(account.getFee());
            transferAccount.setTurnInRNo(account.getRNo());
            transferAccount.setTurnInGuestName(account.getGuestName());
            transferAccount.setRecorder(account.getRecorder());
            transferAccount.setRecorderName(nickNameMap.getOrDefault(account.getRecorder(), account.getRecorder()));
            transferAccount.setRemark(account.getRemark());

            if(StrUtil.isNotEmpty(account.getTurnOutNo())){
                AccountDO turnOutAccount = turnOutAccountDOMap.getOrDefault(account.getTurnOutNo(), new AccountDO());
                transferAccount.setTurnOutRNo(turnOutAccount.getRNo());
                transferAccount.setTurnOutGuestName(turnOutAccount.getGuestName());
            }else {
                transferAccount.setTurnOutRNo(account.getRNo());
                transferAccount.setTurnOutGuestName(account.getGuestName());
            }

            list.add(transferAccount);
        });

        return transferAccountReportRespVO.setList(list);
    }

    @Override
    public SplitAccountReportRespVO getSplitAccountReport(TransferAccountReqVO reqVO) {
        SplitAccountReportRespVO splitAccountReportRespVO =  new SplitAccountReportRespVO();
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        splitAccountReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setEndDate(reqVO.getEndDate())
                .setStartDate(reqVO.getStartDate())
                .setLastSelectTime(LocalDateTime.now());
        // 获得拆账列表
        List<AccountDO> splitaccountList = accountService.getAccountChangeList(reqVO.setState(NumberEnum.TWO.getNumber()));
        if(CollUtil.isEmpty(splitaccountList)){
            return splitAccountReportRespVO.setList(new ArrayList<>());
        }
        Map<String, List<AccountDO>> splitaccountListMap = CollectionUtils.convertMultiMap(splitaccountList, AccountDO::getOriginalSplitAccNo);
        Set<String> accNos = CollectionUtils.convertSet(splitaccountList, AccountDO::getOriginalSplitAccNo);
        // 获得原账务
        List<AccountDO> accountList = accountService.getAccountList(new AccountListReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setAccNos(accNos.stream().toList()));
        Map<String, AccountDO> originalAccountDOMap = CollectionUtils.convertMap(accountList, AccountDO::getAccNo);
        if(CollUtil.isEmpty(splitaccountList)){
            return splitAccountReportRespVO.setList(new ArrayList<>());
        }



        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);
        Map<String, String> dictDataLabelMap = dictDataApi.getDictDataLabelMap(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode()));

        List<SplitAccountRespVO> list = new ArrayList<>();
        for (Map.Entry<String, List<AccountDO>> entry : splitaccountListMap.entrySet()) {
            String accNo = entry.getKey();
            List<AccountDO> accountDOS = entry.getValue();
            AccountDO account = accountDOS.getFirst();
            AccountDO originalAccount = originalAccountDOMap.getOrDefault(accNo, new AccountDO());

            SplitAccountRespVO transferAccount = new SplitAccountRespVO();
            transferAccount.setSubCode(account.getSubCode());
            transferAccount.setSubName(dictDataLabelMap.getOrDefault(account.getSubCode(), account.getSubCode()));
            transferAccount.setBizDate(account.getBizDate());
            transferAccount.setCreateTime(account.getCreateTime());
            transferAccount.setFee(originalAccount.getFee());
            transferAccount.setRNo(account.getRNo());
            transferAccount.setGuestName(account.getGuestName());
            transferAccount.setRecorder(originalAccount.getRecorder());
            transferAccount.setRecorderName(nickNameMap.getOrDefault(originalAccount.getRecorder(), originalAccount.getRecorder()));
            transferAccount.setRecorder(account.getRecorder());
            transferAccount.setRecorderName(nickNameMap.getOrDefault(account.getRecorder(), account.getRecorder()));
            transferAccount.setRemark(account.getRemark());

            String url = String.format(
                    OrderUrlEnum.URL.getCode(),
                    account.getNo(),
                    NoTypeEnum.ORDER.getCode()
            );
            transferAccount.setUrl(url);

            String splitFees = accountDOS.stream()
                    .map(splitAccount -> new BigDecimal(splitAccount.getFee())
                            .divide(HUNDRED, 2, RoundingMode.UP)
                            .toString())
                    .collect(Collectors.joining(","));
            transferAccount.setSplitFee(splitFees);

            list.add(transferAccount);
        }
        // 获得转入账务
        return splitAccountReportRespVO.setList(list);
    }

    @Override
    public ChangePriceReportRespVO getChangePriceReport(ChangePriceReqVO reqVO) {
        ChangePriceReportRespVO changePriceReportRespVO =  new ChangePriceReportRespVO();
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        changePriceReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setEndDate(reqVO.getEndDate().toLocalDate())
                .setStartDate(reqVO.getStartDate().toLocalDate())
                .setLastSelectTime(LocalDateTime.now());
        List<OrderLogDO> orderLogList = orderLogService.getOrderLogList(BeanUtils.toBean(reqVO, OrderLogReqVO.class).setHandle(DictDataEnum.DICT_DATA_UPDATE_PRICE.getCode()));
        if(CollUtil.isEmpty(orderLogList)){
            return changePriceReportRespVO.setList(new ArrayList<>());
        }
        Set<String> orderNoSet = CollectionUtils.convertSet(orderLogList, OrderLogDO::getOrderNo);
        List<String> orderNos = orderNoSet.stream().toList();
        // 获得价格列表
        List<OrderPriceDO> orderPriceList = orderpriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos));
        Map<String, OrderPriceDO> orderPriceDOMap = CollectionUtils.convertMap(orderPriceList, OrderPriceDO::getOrderNo);

        // 获得订单列表
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos));
        Map<String, OrderDO> orderDOMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);

        // 获得房型列表
        List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
        Map<String, RoomTypeDO> roomTypeDOMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode);

        // 获得宾客代码列表
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos));
        Map<String, OrderTogetherDO> orderTogetherMap = orderTogetherList.stream()
                .collect(Collectors.groupingBy(
                        OrderTogetherDO::getOrderNo,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> {
                                    // 首先尝试找到isMain为"1"的记录
                                    Optional<OrderTogetherDO> mainGuest = list.stream()
                                            .filter(t -> NumberEnum.ONE.getNumber().equals(t.getIsMain()))
                                            .findFirst();

                                    // 如果存在主宾客则返回，否则返回列表第一个
                                    return mainGuest.orElseGet(() -> list.isEmpty() ? null : list.get(0));
                                }
                        )
                ));

        // 获得所以中介列表
        List<ProtocolAgentRespDTO> protocolAgentList = protocolAgentApi.getProtocolAgentList(new ProtocolAgentReqDTO().setGcode(reqVO.getGcode())).getData();
        Map<String, ProtocolAgentRespDTO> agentMap = CollectionUtils.convertMap(protocolAgentList, ProtocolAgentRespDTO::getPaCode);
        // 获得所以会员列表
        List<MemberAndStoreCardRespDTO> memberList = memberApi.getMemberList(new MemberListReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())).getData();
        Map<String, MemberAndStoreCardRespDTO> memberMap = CollectionUtils.convertMap(memberList, MemberAndStoreCardRespDTO::getMcode);

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);


        List<ChangePriceRespVO> list = new ArrayList<>();
        orderLogList.forEach(orderLog -> {
            OrderTogetherDO togetherDO = orderTogetherMap.getOrDefault(orderLog.getOrderNo(), new OrderTogetherDO());
            OrderDO orderDO = orderDOMap.getOrDefault(orderLog.getOrderNo(), new OrderDO());
            ChangePriceRespVO changePriceResp = new ChangePriceRespVO();
            changePriceResp.setNo(orderLog.getOrderNo());
            changePriceResp.setRNo(togetherDO.getRNo());
            changePriceResp.setCheckinType(togetherDO.getCheckinType());
            changePriceResp.setGuestSrcType(togetherDO.getGuestSrcType());
            changePriceResp.setGuestName(togetherDO.getName());
            changePriceResp.setStatChannelCode(orderDO.getStatChannelCode());
            changePriceResp.setGuestCode(orderDO.getGuestCode());
            changePriceResp.setRtCode(orderDO.getRtCode());
            changePriceResp.setCheckinTime(orderDO.getCheckinTime());
            changePriceResp.setCheckoutTime(orderDO.getCheckoutTime());
            changePriceResp.setOriginalPriceType(orderPriceDOMap.getOrDefault(orderLog.getOrderNo(), new OrderPriceDO()).getPriceType());
            changePriceResp.setOriginalPriceTypeName(NumberEnum.ONE.getNumber().equals(changePriceResp.getOriginalPrice()) ? HAND_PRICE : OFFER_PRICE);
            changePriceResp.setNewPriceType(orderPriceDOMap.getOrDefault(orderLog.getOrderNo(), new OrderPriceDO()).getPriceType());
            changePriceResp.setNewPriceTypeName(NumberEnum.ONE.getNumber().equals(changePriceResp.getNewPrice()) ? HAND_PRICE : OFFER_PRICE);
            changePriceResp.setStatChannelCodeName(ChannelEnum.getNameByCode(changePriceResp.getStatChannelCode()));
            changePriceResp.setCheckinTypeName(CheckInTypeEnum.getNameByCode(changePriceResp.getCheckinType()));
            changePriceResp.setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(changePriceResp.getGuestSrcType()));
            changePriceResp.setRtName(roomTypeDOMap.getOrDefault(changePriceResp.getRtCode(), new RoomTypeDO()).getRtName());
            changePriceResp.setCreateTime(orderLog.getCreateTime());
            changePriceResp.setCreator(orderLog.getCreator());
            changePriceResp.setCreatorName(nickNameMap.getOrDefault(orderLog.getCreator(), orderLog.getCreator()));

            if (GuestSrcTypeEnum.MEMBER.getCode().equals(changePriceResp.getGuestSrcType())) {
                String mtName = null;

                if (changePriceResp.getGuestCode() != null && memberMap.get(changePriceResp.getGuestCode()) != null) {
                    mtName = memberMap.get(changePriceResp.getGuestCode()).getMtName();
                }
                changePriceResp.setLevelOrCompanyName(mtName);
            }
            if (GuestSrcTypeEnum.AGENT.getCode().equals(changePriceResp.getGuestSrcType())) {
                changePriceResp.setLevelOrCompanyName(agentMap.getOrDefault(changePriceResp.getGuestCode(), new ProtocolAgentRespDTO()).getPaName());
            }

            String url = String.format(
                    OrderUrlEnum.URL.getCode(),
                    orderLog.getOrderNo(),
                    NoTypeEnum.ORDER.getCode()
            );

            changePriceResp.setUrl(url);


            // 设置原房价，现房价
            setPrice(orderLog, changePriceResp);

            list.add(changePriceResp);
        });
        return changePriceReportRespVO.setList(list);
    }

    private static void setPrice(OrderLogDO orderLog, ChangePriceRespVO changePriceResp) {
        List<String> originalPriceList = new ArrayList<>();
        List<String> newPriceList = new ArrayList<>();

        // 匹配原价格格式：日期:2025-05-16 原价格:52000(单位分)
        Pattern originalPattern = Pattern.compile("日期:(\\d{4}-\\d{2}-\\d{2}) 原价格:(\\d+)");
        Matcher originalMatcher = originalPattern.matcher(orderLog.getContent());

        while (originalMatcher.find()) {
            String date = originalMatcher.group(1);
            BigDecimal priceInFen = new BigDecimal(originalMatcher.group(2));
            String priceYuan =priceInFen.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
            originalPriceList.add(date + "：" + priceYuan+ ";");
        }

        // 匹配所有现价格，按顺序提取（不含日期，按原价日期顺序对齐）
        Pattern newPattern = Pattern.compile("现价格:(\\d+)");
        Matcher newMatcher = newPattern.matcher(orderLog.getContent());

        int index = 0;
        while (newMatcher.find() && index < originalPriceList.size()) {
            String date = originalPriceList.get(index).split("：")[0];
            BigDecimal priceInFen = new BigDecimal(newMatcher.group(1));
            String priceYuan =priceInFen.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toString();
            newPriceList.add(date + "：" + priceYuan + ";");
            index++;
        }
        changePriceResp.setOriginalPrice(String.join("\n", originalPriceList));
        changePriceResp.setNewPrice(String.join("\n", newPriceList));
    }

    private void handleAccount(SubjectSummaryReqVO reqVO, List<AccountDO> accountS, SubjectSummaryReportRespVO subjectSummaryReportRespVO) {
        // 获得选中营业日的账务集合
        List<AccountDO> nowAccountDOS = CollectionUtils.filterList(accountS, account -> account.getBizDate().equals(reqVO.getBizDate()));
        Map<String, List<AccountDO>> nowAccountDOSMap = CollectionUtils.convertMultiMap(nowAccountDOS, AccountDO::getSubCode);

        Map<String, List<AccountDO>> accountListMap = CollectionUtils.convertMultiMap(accountS, AccountDO::getSubType);
        List<AccountDO> payAccounts = accountListMap.get(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode());
        List<AccountDO> consumeAccounts = accountListMap.get(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode());

        if(ObjectUtil.isNotEmpty(payAccounts)){
            subjectSummaryReportRespVO.setPaymentDetails(getSubjectSummaryRespVO(payAccounts, nowAccountDOSMap, GeneralConfigTypeEnum.PAY_ACCOUNT.getCode(), subjectSummaryReportRespVO));
        }
        if(ObjectUtil.isNotEmpty(consumeAccounts)){
            subjectSummaryReportRespVO.setConsumptionDetails(getSubjectSummaryRespVO(consumeAccounts, nowAccountDOSMap, GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode(), subjectSummaryReportRespVO));
        }
    }

    private void handleMemberRechargeAccount(SubjectSummaryReqVO reqVO, List<AccountDO> memberRechargeAccountList, SubjectSummaryReportRespVO subjectSummaryReportRespVO) {
        // 获得选中营业日的账务集合
        List<AccountDO> nowAccountDOS = CollectionUtils.filterList(memberRechargeAccountList, account -> account.getBizDate().equals(reqVO.getBizDate()));
        Map<String, List<AccountDO>> nowAccountDOSMap = CollectionUtils.convertMultiMap(nowAccountDOS, AccountDO::getSubCode);

        Map<String, List<AccountDO>> accountListMap = CollectionUtils.convertMultiMap(memberRechargeAccountList, AccountDO::getSubType);
        List<AccountDO> payAccounts = accountListMap.get(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode());
        List<AccountDO> consumeAccounts = accountListMap.get(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode());

        if(ObjectUtil.isNotEmpty(payAccounts)){
            subjectSummaryReportRespVO.setMemberRechargePaymentDetails(getSubjectSummaryRespVO(payAccounts, nowAccountDOSMap, GeneralConfigTypeEnum.PAY_ACCOUNT.getCode(), subjectSummaryReportRespVO));
        }
        if(ObjectUtil.isNotEmpty(consumeAccounts)){
            subjectSummaryReportRespVO.setMemberRechargeConsumptionDetails(getSubjectSummaryRespVO(consumeAccounts, nowAccountDOSMap, GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode(), subjectSummaryReportRespVO));
        }
    }

    private List<SubjectSummaryReportRespVO.Detail> getSubjectSummaryRespVO(List<AccountDO> accounts, Map<String, List<AccountDO>> nowAccountDOSMap,
                                                                            String subType, SubjectSummaryReportRespVO subjectSummaryReportRespVO) {
        List<SubjectSummaryReportRespVO.Detail> detailList = new ArrayList<>();

        Map<String, List<AccountDO>> subAccountsMap = CollectionUtils.convertMultiMap(accounts, AccountDO::getSubCode);
        for (Map.Entry<String, List<AccountDO>> entry : subAccountsMap.entrySet()) {
            SubjectSummaryReportRespVO.Detail detail = new SubjectSummaryReportRespVO.Detail();

            String subCode = entry.getKey();
            detail.setTodayFee(calculateTotalFee(nowAccountDOSMap.getOrDefault(subCode, new ArrayList<>())));
            detail.setMonthFee(calculateTotalFee(entry.getValue()));
            if(subType.equals(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode())){
                subjectSummaryReportRespVO.setPaymentTodayFee(detail.getTodayFee()+subjectSummaryReportRespVO.getPaymentTodayFee());
                subjectSummaryReportRespVO.setPaymentMonthFee(detail.getMonthFee() + subjectSummaryReportRespVO.getPaymentMonthFee());

                detail.setSubCode(subCode);
                detail.setSubName(PayAccountEnum.getLabelByCode(subCode));
                detail.setClassCode(PayAccountClassEnum.TWENTY.getCode()).setClassName(PayAccountClassEnum.TWENTY.getName());
                classPayAccount(subCode, detail);
            }else {
                subjectSummaryReportRespVO.setConsumptionTodayFee(detail.getTodayFee() + subjectSummaryReportRespVO.getConsumptionTodayFee());
                subjectSummaryReportRespVO.setConsumptionMonthFee(detail.getMonthFee() + subjectSummaryReportRespVO.getConsumptionMonthFee());

                detail.setSubCode(subCode);
                detail.setSubName(ConsumeAccountEnum.getLabelByCode(subCode));
                classConsumeAccount(subCode, detail);
            }
            detailList.add(detail);
        }

        return detailList;
    }

    /**
     * 计算账务集合的合计
     *
     * @param accounts
     * @return
     */
    private long calculateTotalFee(List<AccountDO> accounts) {
        return accounts.stream().mapToLong(AccountDO::getFee).sum();
    }

    private static void classPayAccount(String subCode, SubjectSummaryReportRespVO.Detail detail) {
        // 配置付款科目二级分类代码以及名称
        if (RMB_RECEIPT.getCode().equals(subCode) || CASH_REFUND.getCode().equals(subCode) || RMB_DEPOSIT.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.ONE.getCode()).setClassName(PayAccountClassEnum.ONE.getName());
        }
        if (BANK_CARD.getCode().equals(subCode) || BANK_CARD_REFUND.getCode().equals(subCode)
                || BANK_TRANSFER.getCode().equals(subCode) || BANK_TRANSFER_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.TWO.getCode()).setClassName(PayAccountClassEnum.TWO.getName());
        }
        if (SCAN_GUN_WX.getCode().equals(subCode) || SCAN_GUN_WX_REFUND.getCode().equals(subCode)
                || SCAN_GUN_ALIPAY.getCode().equals(subCode) || SCAN_GUN_ALIPAY_REFUND.getCode().equals(subCode)
                || SCAN_GUN.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.EIGHT.getCode()).setClassName(PayAccountClassEnum.EIGHT.getName());
        }
        if (COUPON.getCode().equals(subCode) || DISCOUNT_COUPON.getCode().equals(subCode)
                || VOUCHER.getCode().equals(subCode) || PRE_TICKET.getCode().equals(subCode)
                || FREE_VOUCHER.getCode().equals(subCode) || COUPON_REFUND.getCode().equals(subCode)
                || PRE_TICKET_REFUND.getCode().equals(subCode) || FREE_VOUCHER_REFUND.getCode().equals(subCode)
                || DISCOUNT_COUPON_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.SEVEN.getCode()).setClassName(PayAccountClassEnum.SEVEN.getName());
        }
        if (STORE_CARD.getCode().equals(subCode) || STORE_CARD_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.THREE.getCode()).setClassName(PayAccountClassEnum.THREE.getName());
        }
        if (CHEQUE.getCode().equals(subCode) || CHEQUE_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.FOUR.getCode()).setClassName(PayAccountClassEnum.FOUR.getName());
        }
        if (CREDIT_S_ACCOUNT.getCode().equals(subCode) || AR_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.FIVE.getCode()).setClassName(PayAccountClassEnum.FIVE.getName());
        }
        if (DRAFT.getCode().equals(subCode) || DRAFT_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.NINE.getCode()).setClassName(PayAccountClassEnum.NINE.getName());
        }
        if (HK_DOLLAR.getCode().equals(subCode) || HK_DOLLAR_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.TEN.getCode()).setClassName(PayAccountClassEnum.TEN.getName());
        }
        if (DISCOUNT.getCode().equals(subCode) || DISCOUNT_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.ELEVEN.getCode()).setClassName(PayAccountClassEnum.ELEVEN.getName());
        }
        if (CLOUD_QUICK_PAY.getCode().equals(subCode) || CLOUD_QUICK_PAY_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.TWELVE.getCode()).setClassName(PayAccountClassEnum.TWELVE.getName());
        }
        if (POINT_CHANGE.getCode().equals(subCode) || POINT_CHANGE_REFUND.getCode().equals(subCode)) {
            detail.setClassCode(PayAccountClassEnum.THIRTEEN.getCode()).setClassName(PayAccountClassEnum.THIRTEEN.getName());
        }
    }

    private static void classConsumeAccount(String subCode, SubjectSummaryReportRespVO.Detail detail) {
        // 配置付款科目二级分类代码以及名称
        if (GOODS.getCode().equals(subCode) || GOODS_TYPE.getCode().equals(subCode)) {
            detail.setClassCode(ConsumeAccountClassEnum.ONE.getCode()).setClassName(ConsumeAccountClassEnum.ONE.getName());
        }
        else if (INDEMNITY_FEE.getCode().equals(subCode)) {
            detail.setClassCode(ConsumeAccountClassEnum.TWO.getCode()).setClassName(ConsumeAccountClassEnum.TWO.getName());
        }
        else if (BK_FEE.getCode().equals(subCode) || CATERING.getCode().equals(subCode)) {
            detail.setClassCode(ConsumeAccountClassEnum.THREE.getCode()).setClassName(ConsumeAccountClassEnum.THREE.getName());
        }
        else if (MEMBER_CARD.getCode().equals(subCode) ) {
            detail.setClassCode(ConsumeAccountClassEnum.FOUR.getCode()).setClassName(ConsumeAccountClassEnum.FOUR.getName());
        }
        else if (MEMBER_RECHARGE.getCode().equals(subCode)) {
            detail.setClassCode(ConsumeAccountClassEnum.SEVEN.getCode()).setClassName(ConsumeAccountClassEnum.SEVEN.getName());
        }
        else if (ROOM_FEE.getCode().equals(subCode) || ADJUST_ROOM_FEE.getCode().equals(subCode)
                || HAND_INPUT_ROOM_FEE.getCode().equals(subCode) || ROOM_FEE_ADD.getCode().equals(subCode)
                || ADD_HALF_DAY.getCode().equals(subCode) || ADD_ALL_DAY.getCode().equals(subCode)
                || HOUR_ROOM.getCode().equals(subCode) || ROOM_FEE_DEDIT.getCode().equals(subCode)
                || COUPON_DEDUCTION.getCode().equals(subCode)) {
            detail.setClassCode(ConsumeAccountClassEnum.FIVE.getCode()).setClassName(ConsumeAccountClassEnum.FIVE.getName());
        }
        else if (MEETING_FEE.getCode().equals(subCode)) {
            detail.setClassCode(ConsumeAccountClassEnum.SIX.getCode()).setClassName(ConsumeAccountClassEnum.SIX.getName());
        }else {
            detail.setClassCode(ConsumeAccountClassEnum.NINETY_NINE.getCode()).setClassName(ConsumeAccountClassEnum.NINETY_NINE.getName());
        }
    }

    private List<TransactionDetailRespVO> buildTransactionDetailRespVOList(List<AccountDO> transactionReport,
                                                                           Map<String, String> nickNameMap) {
        List<TransactionDetailRespVO> list=new ArrayList<>();
        for (AccountDO accountDO : transactionReport) {
            TransactionDetailRespVO transaction = BeanUtils.toBean(accountDO, TransactionDetailRespVO.class);
            transaction.setSubCodeName(PayAccountEnum.getLabelByCode(accountDO.getSubCode()));
            transaction.setRecorderName(nickNameMap.getOrDefault(transaction.getRecorder(), transaction.getRecorder()));
            if(AccountStatusEnum.REDEEMED.getCode().equals(accountDO.getState())){
                transaction.setState("退款");
            } else if (accountDO.getIsRefunded().equals(NumberEnum.ONE.getNumber())) {
                transaction.setState("退款");
            }else {
                transaction.setState("已支付");
            }

            list.add(transaction);
        }
        return list;
    }


/*    private static AccountListReqVO buildAccountDetail(PayAndConsumeDetailReqVO reqVO, DictTypeEnum dictTypePayAccount) {
        return new AccountListReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setBizDate(reqVO.getBizDate())
                .setRecorder(reqVO.getRecorder())
                .setSubCodes(reqVO.getSubCode())
                .setSubType(dictTypePayAccount.getCode());
    }*/

    private PayAndConsumeDetailReportRespVO getDetaileReport(PayOrConsumeDetailReqVO reqVO, PayAndConsumeDetailReportRespVO payAndConsumeDetailReportRespVO,
                                                             List<AccountDO> accountList, Map<String, GeneralConfigDO> consumeNameMap) {
        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        payAndConsumeDetailReportRespVO.setHname(merchant.getHname())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now())
                .setList(new ArrayList<>());

        if(reqVO.getStartDate() != null && reqVO.getEndDate() != null){
            payAndConsumeDetailReportRespVO
                    .setStartDate(convertStringToLocalDate(reqVO.getStartDate(), reqVO.getTimeType()))
                    .setEndDate(convertStringToLocalDate(reqVO.getEndDate(), reqVO.getTimeType()));
        }else {
            payAndConsumeDetailReportRespVO
                    .setStartDate(reqVO.getBizDate())
                    .setEndDate(reqVO.getBizDate());
        }
        // 获取渠道列表
        List<ChannelDO> channels = channelService.getChannelList(new ChannelReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
        Map<String, String> channelMap = CollectionUtils.convertMap(channels, ChannelDO::getChannelCode, ChannelDO::getChannelName);
        //  获取所有的中介列表
        List<ProtocolAgentDO> protocolAgentList = protocolAgentService.getProtocolAgentList(new ProtocolAgentReqVO().setGcode(reqVO.getGcode()).setBelongHcode(reqVO.getHcode()).setPaType(NumberEnum.ONE.getNumber()));
        Map<String, ProtocolAgentDO> protocolAgentMap = CollectionUtils.convertMap(protocolAgentList, ProtocolAgentDO::getPaCode);

        List<String> orderNos = CollectionUtils.convertList(accountList, AccountDO::getNo).stream().distinct().toList();
        List<String> togetherOrderCodes = CollectionUtils.convertList(accountList, AccountDO::getTogetherCode).stream().distinct().toList();
        // 获取订单信息 以及 宾客信息
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNos));
        Map<String, OrderDO> orderDOMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setTogetherCodes(togetherOrderCodes));
        Map<String, OrderTogetherDO> orderTogetherDOMap = CollectionUtils.convertMap(orderTogetherList, OrderTogetherDO::getTogetherCode);

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        // 构建消费或付款明细报表信息
        List<PayAndConsumeDetailRespVO> list = payOrConsumeList(accountList, orderDOMap, orderTogetherDOMap,
                payAndConsumeDetailReportRespVO, channelMap, protocolAgentMap, consumeNameMap, nickNameMap);
        if (CollUtil.isNotEmpty(list)){
            payAndConsumeDetailReportRespVO.setList(list);
        }
        if(ObjectUtil.isNotEmpty(reqVO.getStatChannel())){
            List<PayAndConsumeDetailRespVO> collect = payAndConsumeDetailReportRespVO.getList().stream().filter(item -> reqVO.getStatChannel().equals(item.getStatChannel())).collect(Collectors.toList());
            payAndConsumeDetailReportRespVO.setList(collect);
        }
        return payAndConsumeDetailReportRespVO;
    }

    private List<PayAndConsumeDetailRespVO> payOrConsumeList(List<AccountDO> accountList, Map<String, OrderDO> orderDOMap,
                                                             Map<String, OrderTogetherDO> orderTogetherDOMap,
                                                             PayAndConsumeDetailReportRespVO payAndConsumeDetailReportRespVO,
                                                             Map<String, String> channelMap, Map<String, ProtocolAgentDO> protocolAgentMap,
                                                             Map<String, GeneralConfigDO> consumeNameMap, Map<String, String> nickNameMap) {

        return CollectionUtils.convertList(accountList, accountDO -> {
            PayAndConsumeDetailRespVO payAndConsumeDetailRespVO = new PayAndConsumeDetailRespVO();
            OrderDO order = orderDOMap.get(accountDO.getNo());
            String url = order == null ? "" : String.format(
                    OrderUrlEnum.URL.getCode(),
                    accountDO.getNo(),
                    OrderStateEnum.IN_BOOKING.getCode().equals(order.getState()) ? NoTypeEnum.BOOK.getCode() : NoTypeEnum.ORDER.getCode()
            );
            payAndConsumeDetailRespVO.setGcode(accountDO.getGcode())
                    .setHcode(accountDO.getHcode())
                    .setNo(accountDO.getNo())
                    .setUrl(url)
                    .setRNo(accountDO.getRNo())
                    .setGuestName(orderTogetherDOMap.getOrDefault(accountDO.getTogetherCode(), new OrderTogetherDO()).getName())
                    .setGuestSrcType(orderDOMap.getOrDefault(accountDO.getNo(), new OrderDO()).getGuestSrcType())
                    .setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(orderDOMap.getOrDefault(accountDO.getNo(), new OrderDO()).getGuestSrcType()))
                    .setCreateChannel(orderDOMap.getOrDefault(accountDO.getNo(), new OrderDO()).getChannelCode())
                    .setCreateChannelName(channelMap.getOrDefault(orderDOMap.getOrDefault(accountDO.getNo(), new OrderDO()).getChannelCode(), ""))
                    .setSubCode(accountDO.getSubCode())
                    .setFee(accountDO.getFee())
                    .setBizDate(accountDO.getBizDate())
                    .setShiftName(DictDataEnum.getLabelByCode(accountDO.getShiftNo()))
                    .setCreateTime(accountDO.getCreateTime())
                    .setRemark(accountDO.getRemark())
                    .setRecorder(accountDO.getRecorder())
                    .setRecorderName(nickNameMap.getOrDefault(accountDO.getRecorder(), accountDO.getRecorder()));
            if(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(accountDO.getSubType())){
                payAndConsumeDetailRespVO.setSubName(PayAccountEnum.getLabelByCode(accountDO.getSubCode()));
            }else {
                String subName = consumeNameMap.getOrDefault(accountDO.getSubCode(), new GeneralConfigDO()).getName();
                if(StrUtil.isEmpty(subName) && ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(accountDO.getSubCode())){
                    generalConfigService.createGeneralConfig(new GeneralConfigSaveReqVO()
                            .setGcode(accountDO.getGcode())
                            .setHcode(NumberEnum.ZERO.getNumber())
                            .setCode(ConsumeAccountEnum.COUPON_DEDUCTION.getCode())
                            .setName(ConsumeAccountEnum.COUPON_DEDUCTION.getLabel())
                            .setValue(NumberEnum.ONE.getNumber())
                            .setType(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())
                            .setParentCode(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getCode())
                            .setIsG(NumberEnum.ONE.getNumber()));

                    subName = ConsumeAccountEnum.COUPON_DEDUCTION.getLabel();
                }
                payAndConsumeDetailRespVO.setSubName(subName);
                if(GOODS.getCode().equals(accountDO.getSubCode())){
                    payAndConsumeDetailRespVO.setRemark(accountDO.getAccDetail());
                    if(StrUtil.isEmpty(payAndConsumeDetailRespVO.getRNo())){
                        payAndConsumeDetailRespVO.setRNo("现付账");
                    }
                }
            }
            if (Objects.equals(orderDOMap.getOrDefault(accountDO.getNo(), new OrderDO()).getGuestSrcType(), GuestSrcTypeEnum.AGENT.getCode())) {
                ProtocolAgentDO agent = protocolAgentMap.getOrDefault(orderDOMap.getOrDefault(accountDO.getNo(), new OrderDO()).getGuestCode(), new ProtocolAgentDO());
                String channelCode = agent.getChannel();
                if (StrUtil.isNotBlank(channelCode)) {
                    payAndConsumeDetailRespVO.setStatChannel(channelCode)
                            .setStatChannelName(channelMap.getOrDefault(channelCode, ""));
                }
            }
            return payAndConsumeDetailRespVO;
        });
    }

    public LocalDate convertStringToLocalDate(String dateString, String timeType) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter;
            if (timeType.equals(NumberEnum.TWO.getNumber())) {
                // 日期字符串包含时间部分
                formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
                // 解析为 LocalDateTime，然后提取 LocalDate
                return LocalDateTime.parse(dateString, formatter).toLocalDate();
            } else {
                // 只有日期部分
                formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY);
                return LocalDate.parse(dateString, formatter);
            }
        } catch (DateTimeParseException e) {
            // 处理日期格式错误的情况
            throw new IllegalArgumentException("日期格式无效。请使用'yyyy-MM-dd'或'yyyy-MM-dd HH:mm:ss'格式", e);
        }
    }

    @Override
    public PaymentRecoveryDetailReportRespVO getPaymentRecoveryDetailReport(PaymentRecoveryDetailReqVO reqVO) {
        PaymentRecoveryDetailReportRespVO reportRespVO = new PaymentRecoveryDetailReportRespVO();

        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        reportRespVO.setHname(merchant.getHname())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now())
                .setStartDate(reqVO.getBizDate())
                .setEndDate(reqVO.getBizDate())
                .setList(new ArrayList<>());



        // 获取已结账的付款账务列表 - 使用新的查询方法按payBizDate查询
        List<AccountDO> settledAccountList = accountService.getCheckoutAccountList(reqVO.setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode()));
        if (CollUtil.isEmpty(settledAccountList)) {
            return reportRespVO;
        }

        // 构建付款收回明细数据
        List<PaymentRecoveryDetailRespVO> detailList = buildPaymentRecoveryDetailList(
                settledAccountList, reqVO);

        reportRespVO.setList(detailList);
        return reportRespVO;
    }

    private List<PaymentRecoveryDetailRespVO> buildPaymentRecoveryDetailList(
            List<AccountDO> accountList, PaymentRecoveryDetailReqVO reqVO) {

        // 获取用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        List<PaymentRecoveryDetailRespVO> list= new ArrayList<>();
        accountList.forEach(account -> {
            PaymentRecoveryDetailRespVO detail = new PaymentRecoveryDetailRespVO();

            detail.setId(account.getId())
                    .setGcode(account.getGcode())
                    .setHcode(account.getHcode())
                    .setSubCode(account.getSubCode())
                    .setSubName(PayAccountEnum.getLabelByCode(account.getSubCode()))
                    .setRecoveryAmount(account.getFee())
                    .setRNo(account.getRNo())
                    .setGuestName(account.getGuestName())
                    .setPayBizDate(account.getPayBizDate())
                    .setPayTime(account.getPayTime())
                    .setPayOperator(account.getRecorder())
                    .setPayOperatorName(nickNameMap.getOrDefault(account.getRecorder(), account.getRecorder()))
                    .setShiftName(DictDataEnum.getLabelByCode(account.getShiftNo()))
                    .setRemark(account.getRemark())
                    .setOrderNo(account.getNo())
                    .setAccNo(account.getAccNo())
                    .setPayNo(account.getPayNo());

            list.add(detail);
        });
        // 构建明细数据
        return list;
    }

}
