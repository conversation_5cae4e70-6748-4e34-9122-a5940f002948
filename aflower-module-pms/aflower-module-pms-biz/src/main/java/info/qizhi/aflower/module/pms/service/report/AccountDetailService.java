package info.qizhi.aflower.module.pms.service.report;

import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.*;
import jakarta.validation.Valid;

/**
 * 账务有关报表 Service
 */

public interface AccountDetailService {
    /**
     * 获得付款明细报表
     * @param reqVO
     * @return
     */
    PayAndConsumeDetailReportRespVO getPayDetailReport(@Valid PayOrConsumeDetailReqVO reqVO);

    /**
     * 获得消费明细报表
     * @param reqVO
     * @return
     */
    PayAndConsumeDetailReportRespVO getConsumeDetailReport(@Valid PayOrConsumeDetailReqVO reqVO);

    /**
     * 获得交易记录表
     * @param reqVO
     * @return
     */
    TransactionDetailReportRespVO getTransactionReport(@Valid TransactionDetailReqVO reqVO);

    /**
     * 获得酒店经营科目汇总月报
     * @param reqVO
     * @return
     */
    SubjectSummaryReportRespVO getSubjectSummaryReport(@Valid SubjectSummaryReqVO reqVO);

    /**
     * 获得冲账调账明细报表
     * @param reqVO
     * @return
     */
    RedAndAdjustReportRespVO getRedAndAdjustReport(@Valid RedAndAdjustReqVO reqVO);

    /**
     * 获得转账报表
     * @param reqVO
     * @return
     */
    TransferAccountReportRespVO getTransferAccountReport(@Valid TransferAccountReqVO reqVO);

    /**
     * 获得拆账报表
     * @param reqVO
     * @return
     */
    SplitAccountReportRespVO getSplitAccountReport(@Valid TransferAccountReqVO reqVO);

    /**
     * 获得人工修改房价明细报表
     * @param reqVO
     * @return
     */
    ChangePriceReportRespVO getChangePriceReport(@Valid ChangePriceReqVO reqVO);

    /**
     * 获得付款(结账)收回明细报表
     * @param reqVO
     * @return
     */
    PaymentRecoveryDetailReportRespVO getPaymentRecoveryDetailReport(@Valid PaymentRecoveryDetailReqVO reqVO);

    /**
     * 获得消费(结账)收回明细报表
     * @param reqVO
     * @return
     */
    ConsumeRecoveryDetailReportRespVO getConsumeRecoveryDetailReport(@Valid ConsumeRecoveryDetailReqVO reqVO);
}
