package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 付款(结账)收回明细报表 Request VO")
@Data
public class PaymentRecoveryDetailReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "报表操作员")
    private String operator;

    @Schema(description = "结账操作员")
    private String checkoutOperator;

    @Schema(description = "科目类型")
    private String subType;

    @Schema(description = "班次")
    private List<String> shiftNos;

    @Schema(description = "付款科目代码")
    private List<String> subCodes;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    @Schema(description = "房号")
    private String roomNo;

    @Schema(description = "客人姓名")
    private String guestName;
}
