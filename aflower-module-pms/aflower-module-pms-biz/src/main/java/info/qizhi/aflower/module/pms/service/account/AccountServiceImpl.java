package info.qizhi.aflower.module.pms.service.account;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import info.qizhi.aflower.framework.common.core.KeyValue;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.date.DateUtils;
import info.qizhi.aflower.framework.common.util.number.MoneyUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.common.util.object.ObjectUtils;
import info.qizhi.aflower.framework.security.core.util.SecurityFrameworkUtils;
import info.qizhi.aflower.framework.tenant.core.context.TenantContextHolder;
import info.qizhi.aflower.module.erp.api.stock.StockApi;
import info.qizhi.aflower.module.erp.api.stock.StockOutApi;
import info.qizhi.aflower.module.erp.api.stock.dto.ErpStockOutSaveReqDTO;
import info.qizhi.aflower.module.erp.api.stock.dto.ErpStockOutUpdateNumberReqDTO;
import info.qizhi.aflower.module.erp.api.stock.dto.ErpStockOutUpdateReqDTO;
import info.qizhi.aflower.module.erp.enums.ErpPayCodeConstants;
import info.qizhi.aflower.module.marketing.api.coupon.CouponApi;
import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponReqDTO;
import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponRespDTO;
import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponUseReqDTO;
import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponUseSaveReqDTO;
import info.qizhi.aflower.module.marketing.api.couponactivity.CouponActivityApi;
import info.qizhi.aflower.module.marketing.api.couponactivity.dto.CouponActivityReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.send.SmsSendClientApi;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.SendRequestDTO;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.ConsumeSaveReqDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberListReqDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberRespDTO;
import info.qizhi.aflower.module.member.api.memberType.MemberTypeApi;
import info.qizhi.aflower.module.member.api.storecard.StoreCardApi;
import info.qizhi.aflower.module.member.api.storecard.dto.StoreCardRedReqDTO;
import info.qizhi.aflower.module.pay.api.base.PayBaseApi;
import info.qizhi.aflower.module.pay.api.base.dto.*;
import info.qizhi.aflower.module.pay.enums.PlatFormEnum;
import info.qizhi.aflower.module.pms.api.shiftTime.ShiftTimeApi;
import info.qizhi.aflower.module.pms.api.shiftTime.dto.ShiftTimeReqDTO;
import info.qizhi.aflower.module.pms.api.shiftTime.dto.ShiftTimeRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.*;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.*;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.AccOutInListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.AccountChooseReqVO;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.ArSetListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.book.BookListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigReq2VO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.groupparam.GroupParamConfigNightNumRespVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.hotelparam.HotelParamConfigReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.tax.TaxConfigReqVO;
import info.qizhi.aflower.module.pms.controller.admin.goods.vo.retail.GoodsSellRecordListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.order.OrderReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.price.OrderPriceReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday.PriceAllDayRuleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday.PriceAllDayRuleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule.HourRtFeeRule;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule.RtFeeRule;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour.PriceHourRuleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour.PriceHourRuleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo.ProtocolAgentReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.PayOrConsumeDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.PaymentRecoveryDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.TransactionDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.TransferAccountReqVO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.room.RoomRespVO;
import info.qizhi.aflower.module.pms.controller.admin.serviceintegration.vo.ServiceIntegrationPaymentRespVO;
import info.qizhi.aflower.module.pms.controller.admin.shift.vo.ShiftTimeReqVO;
import info.qizhi.aflower.module.pms.convert.account.AccountConvert;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import info.qizhi.aflower.module.pms.dal.dataobject.arset.AccOutInDO;
import info.qizhi.aflower.module.pms.dal.dataobject.arset.ArSetDO;
import info.qizhi.aflower.module.pms.dal.dataobject.booking.BookDO;
import info.qizhi.aflower.module.pms.dal.dataobject.booking.TeamDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GeneralConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GroupParamConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.HotelParamConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.TaxConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.groupparamconfig.NightNum;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.Deposit;
import info.qizhi.aflower.module.pms.dal.dataobject.goods.GoodsSellRecordDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderPriceDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO;
import info.qizhi.aflower.module.pms.dal.dataobject.protocolagent.ProtocolAgentDO;
import info.qizhi.aflower.module.pms.dal.dataobject.serviceintegration.ServiceIntegrationDO;
import info.qizhi.aflower.module.pms.dal.dataobject.shift.ShiftTimeDO;
import info.qizhi.aflower.module.pms.dal.mysql.account.AccountMapper;
import info.qizhi.aflower.module.pms.dal.mysql.order.OrderMapper;
import info.qizhi.aflower.module.pms.mq.producer.TaskProducer;
import info.qizhi.aflower.module.pms.service.account.bo.OrderAccountStatBO;
import info.qizhi.aflower.module.pms.service.account.bo.OrderBO;
import info.qizhi.aflower.module.pms.service.accset.AccSetService;
import info.qizhi.aflower.module.pms.service.arset.AccOutInService;
import info.qizhi.aflower.module.pms.service.arset.ArSetService;
import info.qizhi.aflower.module.pms.service.booking.BookService;
import info.qizhi.aflower.module.pms.service.booking.TeamService;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import info.qizhi.aflower.module.pms.service.config.GroupParamConfigService;
import info.qizhi.aflower.module.pms.service.config.HotelParamConfigService;
import info.qizhi.aflower.module.pms.service.config.TaxConfigService;
import info.qizhi.aflower.module.pms.service.goods.GoodsSellRecordService;
import info.qizhi.aflower.module.pms.service.order.OrderPriceService;
import info.qizhi.aflower.module.pms.service.order.OrderService;
import info.qizhi.aflower.module.pms.service.order.OrderTogetherService;
import info.qizhi.aflower.module.pms.service.pricerule.PriceAllDayRuleService;
import info.qizhi.aflower.module.pms.service.pricerule.PriceHourRuleService;
import info.qizhi.aflower.module.pms.service.protocolagent.ProtocolAgentService;
import info.qizhi.aflower.module.pms.service.room.RoomService;
import info.qizhi.aflower.module.pms.service.sender.BookingOrderSenderService;
import info.qizhi.aflower.module.pms.service.sender.HiiiSenderService;
import info.qizhi.aflower.module.pms.service.sender.PsbSenderService;
import info.qizhi.aflower.module.pms.service.sender.RealRoomStateSenderService;
import info.qizhi.aflower.module.pms.service.serviceintegration.ServiceIntegrationService;
import info.qizhi.aflower.module.pms.service.shift.ShiftTimeService;
import info.qizhi.aflower.module.report.api.handoverreport.HandoverReportApi;
import info.qizhi.aflower.module.report.api.handoverreport.dto.HandoverReportRespDTO;
import info.qizhi.aflower.module.report.api.zeroroomrate.ZeroRoomRateReportApi;
import info.qizhi.aflower.module.report.api.zeroroomrate.dto.ZeroRoomRateSaveReqDTO;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.dict.dto.DictDataRespDTO;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import info.qizhi.aflower.module.system.api.sms.SmsCodeApi;
import info.qizhi.aflower.module.system.api.sms.dto.code.SmsCodeUseReqDTO;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.io.StringReader;
import java.io.StringWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static info.qizhi.aflower.framework.common.enums.PayAccountEnum.*;
import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.framework.common.util.servlet.ServletUtils.getClientIP;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.*;
import static info.qizhi.aflower.module.pms.enums.LogRecordConstants.*;

/**
 * 账务 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class AccountServiceImpl implements AccountService {

    public static final long ACCOUNT_TIMEOUT_MILLIS = 10 * DateUtils.SECOND_MILLIS;

    private static final long MINUTE_TO_HOUR_CONVERSION_FACTOR = 60L;

    private static final String RECORDER = "夜审";

    @Resource
    private AccountMapper accountMapper;
    @Resource
    private OrderMapper orderMapper;
    @Resource
    private ArSetService arSetService;
    @Resource
    private TaxConfigService taxConfigService;
    @Resource
    private AccSetService accSetService;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    @Lazy
    private BookService bookService;
    @Resource
    @Lazy
    private OrderTogetherService orderTogetherService;
    @Resource
    @Lazy
    private OrderService orderService;
    @Resource
    private ShiftTimeService shiftTimeService;
    @Resource
    private GeneralConfigService generalConfigService;
    @Resource
    private TeamService teamService;
    @Resource
    private ProtocolAgentService protocolAgentService;
    @Resource
    private GoodsSellRecordService goodsSellRecordService;
    @Resource
    private RoomService roomService;
    @Resource
    private PriceAllDayRuleService priceAllDayRuleService;
    @Resource
    private PriceHourRuleService priceHourRuleService;
    @Resource
    private OrderPriceService orderPriceService;
    @Resource
    private GroupParamConfigService groupParamConfigService;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private AccOutInService accOutInService;
    @Resource
    private HotelParamConfigService hotelParamConfigService;
    @Resource
    private PayBaseApi payBaseApi;
    @Resource
    private StoreCardApi storeCardApi;
    @Resource
    private MemberApi memberApi;
    @Resource
    private MemberTypeApi memberTypeApi;
    @Resource
    private RealRoomStateSenderService realRoomStateSenderService;
    @Resource
    @Lazy
    private BookingOrderSenderService bookingOrderSenderService;
    @Resource
    private HiiiSenderService hiiiSenderService;
    @Resource
    private ServiceIntegrationService serviceIntegrationService;
    @Resource
    private SmsSendClientApi smsSendClientApi;
    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private StockOutApi stockOutApi;
    @Resource
    private StockApi stockApi;
    @Resource
    private ShiftTimeApi shiftTimeApi;
    @Resource
    private TaskProducer taskProducer;
    @Resource
    private ZeroRoomRateReportApi zeroRoomRateReportApi;

    @Resource
    private PsbSenderService psbSenderService;
    @Resource
    private CouponActivityApi couponActivityApi;
    @Resource
    private CouponApi couponApi;
    @Resource
    private HandoverReportApi handoverReportApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAccount(AccountSaveReqVO createReqVO) {
        // 处理税后金额
        Long afterTaxFee = calculateAfterTaxFee(createReqVO.getGcode(), createReqVO.getHcode(), createReqVO.getSubCode(), createReqVO.getFee());
        createReqVO.setAfterTaxFee(afterTaxFee);
        // 插入
        AccountDO account = BeanUtils.toBean(createReqVO, AccountDO.class);
        // 获取门店
        MerchantRespDTO merchant = merchantApi.getMerchant(createReqVO.getHcode()).getData();
        account.setAccNo(IdUtil.getSnowflakeNextIdStr());
        account.setCurrencyUnit(merchant.getCurrencyUnit());
        accountMapper.insert(account);
        return account.getId();
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @LogRecord(success = PMS_PAY_SUCCESS, type = PMS_ORDER_PAY_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public String pay(PayAccountSaveReqVO reqVO) {
        // 判断是否验证短信
        if (NumberEnum.TWO.getNumberInt().equals(reqVO.getVerifyMode())) {
            CommonResult<Boolean> valid = smsCodeApi.useSmsCode(new SmsCodeUseReqDTO()
                    .setMobile(reqVO.getPhone()).setScene(5).setCode(reqVO.getSmsCode()).setUsedIp(getClientIP()));
            if (valid.getCode() != 0) throw exception(SMS_CODE_ERROR, valid.getMsg());
        }
        // 验证密码
        if (NumberEnum.ONE.getNumberInt().equals(reqVO.getVerifyMode())) {
            CommonResult<Boolean> verify = memberApi.verifyMemberPassword(reqVO.getGcode(), reqVO.getMcode(), reqVO.getPwd());
            if (verify.getCode() != 0) throw exception(MEMBER_PAY_ERROR, verify.getMsg());
        }
        // 校验支付
        validPay(reqVO.getGcode(), reqVO.getHcode(), reqVO.getAccType(), reqVO.getNo());
        // 根据收款类型，设置正负数
        buildFee(reqVO);
        //校验付款方式与门店设置的付款方式是否一致
        String currencyUnit = validUnit(reqVO.getSubCode(), reqVO.getHcode(), reqVO.getGcode(), reqVO.getCurrencyUnit());
        // 将请求VO转换为账户DO对象
        AccountDO account = BeanUtils.toBean(reqVO, AccountDO.class);
        account.setCurrencyUnit(currencyUnit);
        // 获取业务日期
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        // 获取当前登录用户的班次信息
        String shiftNo = "";
        if (OrderSrcEnum.APP.getCode().equals(reqVO.getSource()) || OrderSrcEnum.AGENT.getCode().equals(reqVO.getSource())) {
            List<ShiftTimeRespDTO> shiftTimeList = shiftTimeApi.getShiftTimeList(new ShiftTimeReqDTO()
                    .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(BooleanEnum.TRUE.getValue())).getData();
            shiftNo = assignShiftCodeBasedOnTime(shiftTimeList);
        } else {
            shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        }

        // 设置账户的子类型、账号、状态、费用、业务日期、记录者和班次等信息
        account.setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())
                .setAccNo(IdUtil.getSnowflakeNextIdStr())
                .setState(StrUtil.isNotBlank(reqVO.getState()) ? reqVO.getState() : AccountStatusEnum.UNCLOSED.getCode())
                .setAfterTaxFee(reqVO.getFee())
                .setBizDate(bizDate)
                .setRecorder(SecurityFrameworkUtils.getLoginUserName())
                .setShiftNo(shiftNo);
        if (Objects.equals(reqVO.getState(), AccountStatusEnum.CLOSED.getCode())) {
            account.setPayShiftNo(shiftNo)
                    .setPayBizDate(bizDate)
                    .setPayTime(LocalDateTime.now())
                    .setIsCanRev(BooleanEnum.FALSE.getValue())
                    .setPayer(SecurityFrameworkUtils.getLoginUserName());
        }
        if (StrUtil.isNotEmpty(reqVO.getPayNo())) {
            account.setPayNo(reqVO.getPayNo());
        } else {
            account.setPayNo(account.getAccNo());
        }
        if (Objects.nonNull(reqVO.getOrderTogether())) {
            account.setRCode(reqVO.getOrderTogether().getRCode())
                    .setRNo(reqVO.getOrderTogether().getRNo())
                    .setGuestName(reqVO.getOrderTogether().getGuestName())
                    .setGuestSrcType(reqVO.getOrderTogether().getGuestSrcType());
        }
        // 根据账户类型处理不同的业务逻辑
        handleAccountType(account, reqVO);

        // 获得对接服务平台
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(reqVO.getGcode(), reqVO.getHcode(), ServiceTypeEnum.PAYMENT.getCode());
        ServiceIntegrationPaymentRespVO serviceIntegrationPaymentRespVO = BeanUtils.toBean(serviceIntegration, ServiceIntegrationPaymentRespVO.class);
        switch (PayAccountEnum.getPayAccountEnumByCode(reqVO.getSubCode())) {
            case CREDIT_S_ACCOUNT: // 如果是AR账 需要在账务表里记录外部订单号和备注里记录订单入住人
                KeyValue<String, List<String>> paType = arPay(reqVO.getGcode(), reqVO.getPayCode(), reqVO.getFee());
                account.setCreditTargetType(paType.getKey()).setAccDetail(paType.getValue().getFirst());
                if (NumberEnum.ONE.getNumber().equals(paType.getValue().getLast())) {
                    account.setIsVerify(NumberEnum.ONE.getNumber());
                }
                // 处理AR账的外部订单号和备注
                warpArAccountOutOrderNo(account, reqVO);
                break;
            case SCAN_GUN: // 扫码抢收款,处理扫码枪收款业务
                if (ObjectUtil.isEmpty(serviceIntegrationPaymentRespVO)) {
                    throw exception(SCAN_NOT_OPEN);
                }
                boolean hasScanGunCode = serviceIntegrationPaymentRespVO.getScenario().getParameters().stream()
                        .anyMatch(scenario -> SCAN_GUN.getCode().equals(scenario.getCode()));
                if (!hasScanGunCode) {
                    throw exception(SCAN_NOT_OPEN);
                }
                ScanRespDTO payScanRespDTO = scanGunPay(reqVO.getHcode(), reqVO.getPayCode(), reqVO.getFee(), reqVO.getAccDetail(), account, serviceIntegrationPaymentRespVO.getSolutionProvider());
                account.setOutOrderNo(payScanRespDTO.getOrderNo());
                account.setAccDetail("账户端交易单号" + payScanRespDTO.getAccOrderNo());
                break;
            case SCAN_GUN_PRE_AUTH: // 扫码抢-预授权
                boolean hasScanGunPreCode = serviceIntegrationPaymentRespVO.getScenario().getParameters().stream()
                        .anyMatch(scenario -> SCAN_GUN_PRE_AUTH.getCode().equals(scenario.getCode()));
                if (!hasScanGunPreCode) {
                    throw exception(SCAN_NOT_OPEN);
                }
                String orderNo2 = prePayScan(reqVO.getHcode(), reqVO.getPayCode(), reqVO.getFee(), reqVO.getAccDetail(), serviceIntegrationPaymentRespVO.getSolutionProvider());
                account.setOutOrderNo(orderNo2);
                break;
            case STORE_CARD: // 储值卡付款
                if (ObjectUtil.isNotEmpty(serviceIntegrationPaymentRespVO) && serviceIntegrationPaymentRespVO.getScenario().getParameters().stream()
                        .anyMatch(scenario -> STORE_CARD.getCode().equals(scenario.getCode()))) {
                    VipRespDTO payVipRespDTO = vipCardPay(reqVO.getHcode(), reqVO.getStoreCardNo(), reqVO.getFee(), account, serviceIntegrationPaymentRespVO.getSolutionProvider());
                    account.setOutOrderNo(payVipRespDTO.getOrderNo());
                    account.setAccDetail("账户端交易单号" + payVipRespDTO.getAccOrderNo());
                } else {
                    CommonResult<Boolean> consumeResult = memberApi.consume(buildConsumeSaveReqDTO(reqVO));
                    account.setAccDetail(reqVO.getPhone());
                    if (!consumeResult.isSuccess()) {
                        throw exception(STORECARD_PAY_ERROR, consumeResult.getMsg());
                    }
                    break;
                }
            case STORE_CARD_REFUND: // 储值卡退款
                if (ObjectUtil.isNotEmpty(serviceIntegrationPaymentRespVO) && serviceIntegrationPaymentRespVO.getScenario().getParameters().stream()
                        .anyMatch(scenario -> STORE_CARD.getCode().equals(scenario.getCode()))) {
                   /* VipRespDTO payVipRespDTO = vipCardPay(reqVO.getHcode(), reqVO.getStoreCardNo(), -reqVO.getFee(), account, serviceIntegrationPaymentRespVO.getSolutionProvider());
                    account.setOutOrderNo(payVipRespDTO.getOrderNo());
                    account.setAccDetail("账户端交易单号" + payVipRespDTO.getAccOrderNo());*/
                } else {
                    CommonResult<Boolean> consumeResult = memberApi.consume(buildConsumeSaveReqDTO(reqVO));
                    account.setAccDetail(reqVO.getPhone());
                    if (!consumeResult.isSuccess()) {
                        throw exception(STORECARD_PAY_ERROR, consumeResult.getMsg());
                    }
                    break;
                }
        }
        // 插入账户信息到数据库
        accountMapper.insert(account);
        // 储值卡发送短信
        if (STORE_CARD.getCode().equals(reqVO.getSubCode())) {
            paySend(account, reqVO);
        }
        // 记录日志
        LogRecordContext.putVariable("order", account);
        LogRecordContext.putVariable("subCode", PayAccountEnum.getLabelByCode(account.getSubCode()));
        // 返回账户的账号
        return account.getAccNo();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(List<AccountDO> accountDOS) {
        accountMapper.updateBatch(accountDOS);
    }

    /**
     * 处理AR账的外部订单号和备注
     *
     * @param account
     * @param reqVO
     */
    private void warpArAccountOutOrderNo(AccountDO account, PayAccountSaveReqVO reqVO) {
        if (!PayAccountEnum.CREDIT_S_ACCOUNT.getCode().equals(reqVO.getSubCode())) {
            return;
        }
        // 根据accType来判断是取预订单还会取订单
        if (AccountTypeEnum.BOOK.getCode().equals(reqVO.getAccType())) { // 预订单里挂AR帐
            BookDO book = bookService.getBookByBookNo(reqVO.getNo());
            if (book == null) {
                return;
            }
            account.setOutOrderNo(book.getOutOrderNo());
            account.setRemark("外部订单号：" + book.getOutOrderNo() + " 预订人：" + book.getContact() + " 电话:" + book.getPhone());
        }
        if (AccountTypeEnum.GENERAL.getCode().equals(reqVO.getAccType())) { // 订单里挂AR帐
            OrderDO order = orderService.getOrderByOrderNo(reqVO.getNo());
            if (order == null) {
                return;
            }
            List<OrderTogetherDO> orderTogetherDOS = orderTogetherService.getTogetherList(reqVO.getGcode(), reqVO.getHcode(), reqVO.getNo());
            // 获取主客
            OrderTogetherDO orderTogetherDO = orderTogetherDOS.stream().filter(orderTogetherDO1 -> NumberEnum.ONE.getNumber().equals(orderTogetherDO1.getIsMain())).findFirst().orElse(null);
            account.setOutOrderNo(order.getOutOrderNo());
            // 提前对 orderTogetherDO 进行判空处理，避免重复判断
            String togetherName = (orderTogetherDO != null) ? orderTogetherDO.getName() : "";
            if (StrUtil.isNotBlank(togetherName)) {
                account.setRemark(togetherName);
            }
        }
        if (AccountTypeEnum.GROUP.getCode().equals(reqVO.getAccType())) { // 如果是团队主单里挂AR帐
            // TODO: 这里也需要完成
        }
    }

    private String assignShiftCodeBasedOnTime(List<ShiftTimeRespDTO> shifts) {
        // 获取当前时间
        LocalTime now = LocalTime.now();

        for (ShiftTimeRespDTO shift : shifts) {
            // 解析班次的开始时间和结束时间
            LocalTime startTime = LocalTime.parse(shift.getStartTime(), DateTimeFormatter.ofPattern("HH:mm"));
            LocalTime endTime = LocalTime.parse(shift.getEndTime(), DateTimeFormatter.ofPattern("HH:mm"));

            // 判断当前时间是否在班次的开始和结束时间范围内
            if (!now.isBefore(startTime) && !now.isAfter(endTime)) {
                // 如果当前时间在范围内，赋值 shiftCode
                return shift.getShiftCode();
            }
        }

        return ShiftTypeEnum.MORNING.getCode();
    }


    // 储值卡短信
    @Async
    public void paySend(AccountDO account, PayAccountSaveReqVO reqVO) {
        // 验证
        if (!NumberEnum.ONE.getNumber().equals(reqVO.getIsSendSms())) {
            return;
        }
        Integer sign = smsSendClientApi.verifySend(8, reqVO.getHcode(), reqVO.getIsSendSms(), reqVO.getMcode()).getData();
        if (NumberEnum.ZERO.getNumberInt().equals(sign)) return;
        // 属性处理
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String operateTime = account.getCreateTime().format(formatter);
        String notFiled = "暂无";
        String oldFrontPhone = merchantApi.getMerchant(reqVO.getHcode()).getData().getFrontPhone();
        String frontPhone = (StrUtil.isEmpty(oldFrontPhone) ? notFiled : oldFrontPhone);
        List<HashMap<String, String>> send = new ArrayList<>();
        String name = "";
        if (AccountTypeEnum.BOOK.getCode().equals(reqVO.getAccType())) {
            name = account.getGuestName();
        } else {
            name = orderTogetherService.getOrderTogether(reqVO.getTogetherCode()).getName();
        }
        String finalName = name;
        // 计算余额
        MemberAndStoreCardRespDTO card = memberApi.getMemberAndStoreCardByMcode(reqVO.getGcode(), reqVO.getMcode(), reqVO.getHcode())
                .getData();
        String balance = String.valueOf(card.getStoreCards().getFirst().getBalance() / 100.0);
        String phone = card.getPhone();
        send.add(new HashMap<String, String>() {{
            put(SmsConfigParamEnum.MNAME.getCode(), finalName);
            put(SmsConfigParamEnum.OPERATE_TIME.getCode(), operateTime);
            put(SmsConfigParamEnum.CONSUME_FEE.getCode(), String.valueOf(account.getFee() / 100.0));
            put(SmsConfigParamEnum.FRONT_PHONE.getCode(), frontPhone);
            put(SmsConfigParamEnum.BALANCE.getCode(), balance);
            put(SmsConfigParamEnum.RECEIVER.getCode(), phone);
        }});
        smsSendClientApi.smsSendForMap(new SendRequestDTO().setSend(send).setSign(sign).setType(8));
    }

    private void buildFee(PayAccountSaveReqVO reqVO) {
        if (NumberEnum.MINUS.getNumber().equals(reqVO.getPayValue())) {
            reqVO.setFee(-reqVO.getFee());
        }
    }

    private VipRespDTO vipCardPay(String hcode, String storeCardNo, Long fee, AccountDO account, String platform) {

        account.setPlatForm(platform);

        CommonResult<VipRespDTO> res = payBaseApi.vip(new VipReqDTO().setVipId(storeCardNo)
                .setHcode(hcode)
                .setPrice(fee)
                .setPlatform(platform));
        // 支付成功
        if (res.getCode() == 0) {
            return res.getData();
        } else {
            throw exception(PAY_FAILURE, res.getMsg());
        }
    }

    /*private VipRespDTO vipCardPay(String hcode, String storeCardNo, Long fee, AccountDO account, String platform) {

        account.setPlatForm(platform);

        CommonResult<VipRespDTO> res = payBaseApi.vipRefund(new VipReqDTO().setVipId(storeCardNo)
                .setHcode(hcode)
                .setPrice(fee)
                .setPlatform(platform));
        // 支付成功
        if (res.getCode() == 0) {
            return res.getData();
        } else {
            throw exception(PAY_FAILURE, res.getMsg());
        }
    }*/

    private String validUnit(String subCode, String hcode, String gcode, String currencyUnit) {
        //获得付款方式配置信息
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(new GeneralConfigReq2VO().setGcode(gcode).setHcode(NumberEnum.ZERO.getNumber())
                .setCode(subCode).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode()));
        //获得门店信息
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(hcode).getData();
        if (ObjectUtil.isNotEmpty(merchantRespDTO) && StrUtil.isNotEmpty(currencyUnit)) {
            if (!currencyUnit.equals(merchantRespDTO.getCurrencyUnit())) {
                throw exception(PAY_TYPE_NOT_MATCH, CurrencyUnitEnum.getNameByCode(merchantRespDTO.getCurrencyUnit()));
            }
        } else if ((ObjectUtil.isNotEmpty(generalConfig) && StrUtil.isNotEmpty(generalConfig.getCurrencyUnit()))) {
            if (!generalConfig.getCurrencyUnit().equals(merchantRespDTO.getCurrencyUnit())) {
                throw exception(PAY_TYPE_NOT_MATCH, CurrencyUnitEnum.getNameByCode(merchantRespDTO.getCurrencyUnit()));
            }
        }
        return merchantRespDTO.getCurrencyUnit();
    }

    /**
     * 校验支付前的订单状态
     * 此方法主要针对预订单类型的账务进行校验，确保在支付前订单没有入住记录
     * 如果账务类型为预订单且存在入住、信用、或退房状态的订单，则抛出异常，表示不允许在预订单中入账
     *
     * @param gcode   全球订单号，用于标识特定的订单
     * @param hcode   酒店代码，用于指定特定的酒店
     * @param accType 账务类型，用于区分不同的账务处理方式
     * @param bookNo  预订单号，用于标识特定的预订单
     */
    private void validPay(String gcode, String hcode, String accType, String bookNo) {
        // 如果账务类型为预订单，需要校验是否有入住，入住后则不允许在预订单中入账
        if (Objects.equals(accType, AccountTypeEnum.BOOK.getCode())) {
            // 查询特定状态下与给定条件相关的订单列表
            List<OrderDO> orders = orderService.getOrderList(new OrderReqVO().setGcode(gcode)
                    .setHcode(hcode)
                    .setBookNo(bookNo)
                    .setStates(List.of(OrderStateEnum.CHECK_IN.getCode(),
                            OrderStateEnum.CREDIT.getCode(), OrderStateEnum.CHECK_OUT.getCode())));
            // 如果找到相关的订单，说明已有入住等操作，抛出异常阻止在预订单中入账
            if (CollUtil.isNotEmpty(orders)) {
                throw exception(BOOK_NOT_ACCOUNT);
            }
        }
    }

    /**
     * AR账
     *
     * @param gcode   集团代码
     * @param payCode AR账套代码
     * @param fee     金额
     * @return 返回AR账的账户类型，key: agent、protocol value: 账套名称
     */
    public KeyValue<String, List<String>> arPay(String gcode, String payCode, Long fee) {
        ArSetDO arSet = arSetService.calculateArSet(gcode, payCode, ArSetHandleTypeEnum.CREDIT, fee);
        return getPaType(arSet);
    }

    /**
     * 扫码抢预授权
     *
     * @param payCode   预授权码
     * @param fee       金额
     * @param accDetail 明细
     * @return 成功返回商户支付订单编号，失败报异常
     */
    private String prePayScan(String hcode, String payCode, Long fee, String accDetail, String platform) {
        validateAndConvertScanGunSubCode(payCode);
        String hname = shiftTimeService.getHnameByUserIdFromCache(hcode);
        if (StrUtil.isBlank(hname)) {
            throw exception(SHIFT_TIME_NOT_EXISTS2);
        }
        PreScanReqDTO preScanReqDTO = new PreScanReqDTO();
        preScanReqDTO.setBody(accDetail);
        preScanReqDTO.setPrice(fee);
        preScanReqDTO.setAuthCode(payCode);
        preScanReqDTO.setHcode(hcode);
        preScanReqDTO.setPlatform(platform);
        preScanReqDTO.setSubject(hname);
        CommonResult<PreScanRespDTO> res = payBaseApi.preScan(preScanReqDTO);
        // 支付成功
        if (res.getCode() == 0) {
            return res.getData().getOrderNo();
        } else {
            throw exception(PAY_FAILURE, res.getMsg());
        }
    }

    @Override
    public ScanRespDTO scanGunPay(String hcode, String payCode, Long price, String accDetail, AccountDO account, String platform) {
        //将accDetail置为空字符串，防止报错
        if (accDetail == null) {
            accDetail = "";
        }
        // 转换为微信支付、支付宝支付类型
        String scanGunCodeType = validateAndConvertScanGunSubCode(payCode);
        account.setSubCode(scanGunCodeType);
        account.setPlatForm(platform);
        String hname = shiftTimeService.getHnameByUserIdFromCache(hcode);
        if (StrUtil.isBlank(hname)) {
            throw exception(SHIFT_TIME_NOT_EXISTS2);
        }

        ScanReqDTO scanReqDTO = new ScanReqDTO();
        scanReqDTO.setAuthCode(payCode.trim());
        scanReqDTO.setPrice(price);
        scanReqDTO.setSubject(hname);
        scanReqDTO.setHcode(hcode);
        scanReqDTO.setPlatform(platform);
        scanReqDTO.setBody(accDetail);
        CommonResult<ScanRespDTO> res = payBaseApi.scan(scanReqDTO);
        // 支付成功
        if (res.getCode() == 0) {
            return res.getData();
        } else {
            throw exception(PAY_FAILURE, res.getMsg());
        }
    }

    @Override
    public RefundRespDTO scanGunRefund(AccountDO account, Long fee) {
        validateScanGunRefund(account);
        CommonResult<RefundRespDTO> payRes = new CommonResult<>();
        if (Objects.equals(STORE_CARD.getCode(), account.getSubCode())) {
            RefundVipReqDTO refundReqDTO = new RefundVipReqDTO() {{
                setVipId(account.getPayCode()).setHcode(account.getHcode())
                        .setPlatform(account.getPlatForm().equals(NumberEnum.ZERO.getNumber()) ? PlatFormEnum.FUIOU.getCode() : account.getPlatForm())
                        .setPayPrice(account.getFee()).setRefundPrice(fee == null ? account.getFee() : fee).setPayOrderNo(account.getOutOrderNo());
            }};
            payRes = payBaseApi.vipRefund(refundReqDTO);
        } else {
            payRes = payBaseApi.refund(new RefundReqDTO().setHcode(account.getHcode())
                    .setPlatform(account.getPlatForm().equals(NumberEnum.ZERO.getNumber()) ? PlatFormEnum.FUIOU.getCode() : account.getPlatForm())
                    .setPayOrderNo(account.getOutOrderNo())
                    .setPayPrice(account.getFee())
                    .setRefundPrice(fee == null ? account.getFee() : fee));
        }

        // 退款成功返回商户退款单号
        if (payRes.getCode() == 0) {
            return payRes.getData();
        } else {
            throw exception(PAY_FAILURE, payRes.getMsg());
        }
    }

    /**
     * 验证退款
     *
     * @param account 账务
     */
    private void validateScanGunRefund(AccountDO account) {
        // 判断账务状态
        if (!AccountStatusEnum.UNCLOSED.getCode().equals(account.getState())) {
            throw exception(ACCOUNT_STATUS_ERROR, AccountStatusEnum.getNameByCode(account.getState()));
        }
        // 科目为 scan_gun_alipay 或 scan_gun_wx
        if (!Objects.equals(STORE_CARD.getCode(), account.getSubCode())) {
            if (!List.of(PayAccountEnum.SCAN_GUN_ALIPAY.getCode(), PayAccountEnum.SCAN_GUN_WX.getCode()).contains(account.getSubCode())) {
                throw exception(ACCOUNT_SUB_CODE_ERROR);
            }
        }
        // 只支持付款科目
        if (!Objects.equals(account.getSubType(), DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())) {
            throw exception(ACCOUNT_SUB_TYPE_ERROR);
        }
        // 只支持60天内的付款
        if (!LocalDateTimeUtil.isIn(account.getCreateTime(), LocalDateTime.now().minusDays(60), LocalDateTime.now())) {
            throw exception(ACCOUNT_CREATE_TIME_ERROR, 60);
        }
        // 金额必须大于0
        if (account.getFee() <= 0) {
            throw exception(ACCOUNT_FEE_ERROR);
        }
    }

    /**
     * 将支付码转换为 微信码还是支付宝的码
     *
     * @param payCode 付款码
     * @return 微信码还是支付宝的码
     */
    private String validateAndConvertScanGunSubCode(String payCode) {
        if (StrUtil.isBlank(payCode)) {
            throw exception(SCAN_GUN_PAY_CODE_NOT_EXISTS);
        }
        String payCodeTrim = payCode.trim();
        // 取payCode的前两位
        String prefix = payCodeTrim.substring(0, 2);
        // 验证是否为数字
        if (!NumberUtil.isNumber(prefix)) {
            throw exception(SCAN_GUN_PAY_CODE_ERROR);
        }
        // 微信支付码
        if (WxAliPayPrefixEnum.WX.getPrefixes().contains(prefix)) {
            if (payCodeTrim.length() != 18) {
                throw exception(SCAN_GUN_PAY_CODE_ERROR);
            }
            return PayAccountEnum.SCAN_GUN_WX.getCode();
        }
        if (WxAliPayPrefixEnum.ALIPAY.getPrefixes().contains(prefix)) {
            // payCode长度为16到24位
            if (payCodeTrim.length() < 16 || payCodeTrim.length() > 24) {
                throw exception(SCAN_GUN_PAY_CODE_ERROR);
            }
            return PayAccountEnum.SCAN_GUN_ALIPAY.getCode();
        }
        return null;
    }

    /**
     * 根据账户类型处理不同的业务逻辑
     *
     * @param account 账户DO对象
     * @param reqVO   请求VO对象
     */
    private void handleAccountType(AccountDO account, PayAccountSaveReqVO reqVO) {
        // 根据账户类型进行不同的业务处理
        switch (AccountTypeEnum.getAccountTypeEnumByCode(reqVO.getAccType())) {
            case BOOK:
                processBook(account, reqVO);
                break;
            case GENERAL:
                processGeneralOrder(account, reqVO);
                break;
            case GROUP:
                processTeam(account, reqVO);
                break;
            case CASH:
                processCash(account, reqVO);
                break;
            default:
                // 如果账户类型不存在，抛出异常
                throw exception(ACCOUNT_TYPE_NOT_EXISTS);
        }
    }

    /**
     * 根据账户类型处理不同的业务逻辑
     *
     * @param accountList 账户DO对象
     * @param reqVO       请求VO对象
     */
    private void handleAccountTypeList(List<AccountDO> accountList, BatchPayAccountSaveReqVO reqVO) {
        // 根据账户类型进行不同的业务处理
        switch (AccountTypeEnum.getAccountTypeEnumByCode(reqVO.getAccType())) {
            case BOOK:
                processBook(accountList, reqVO);
                break;
            case GENERAL:
                processGeneralOrder(accountList, reqVO);
                break;
            case GROUP:
                processTeam(accountList, reqVO);
                break;
            case CASH:
                processCash(accountList, reqVO);
                break;
            default:
                // 如果账户类型不存在，抛出异常
                throw exception(ACCOUNT_TYPE_NOT_EXISTS);
        }
    }

    /**
     * 处理预订单类型的账户
     *
     * @param account 账户DO对象
     * @param reqVO   请求VO对象
     */
    private void processBook(AccountDO account, PayAccountSaveReqVO reqVO) {
        // 根据订单号获取预订单信息
        BookDO book = bookService.getBookByBookNo(reqVO.getNo());
        // 如果预订单不存在，抛出异常
        if (Objects.isNull(book)) {
            throw exception(BOOK_NOT_EXISTS);
        }
        // 设置账户的客人姓名和来源类型
        account.setGuestName(book.getContact())
                .setGuestSrcType(book.getGuestSrcType());
    }

    /**
     * 处理预订单类型的账户
     *
     * @param accountList 账户DO对象
     * @param reqVO       请求VO对象
     */
    private void processBook(List<AccountDO> accountList, BatchPayAccountSaveReqVO reqVO) {
        // 根据订单号获取预订单信息
        BookDO book = bookService.getBookByBookNo(reqVO.getNo());
        // 如果预订单不存在，抛出异常
        if (Objects.isNull(book)) {
            throw exception(BOOK_NOT_EXISTS);
        }
        // 设置账户的客人姓名和来源类型
        accountList.forEach(account -> account.setGuestName(book.getContact())
                .setGuestSrcType(book.getGuestSrcType()));
    }

    /**
     * 处理普通订单类型的账户
     *
     * @param account 账户DO对象
     * @param reqVO   请求VO对象
     */
    private void processGeneralOrder(AccountDO account, PayAccountSaveReqVO reqVO) {
        // 检查宾客代码是否存在
        if (StrUtil.isBlank(reqVO.getTogetherCode())) {
            throw exception(TOGETHER_CODE_NOT_EXISTS);
        }
        OrderTogetherDO orderTogether = orderTogetherService.getOrderTogether(reqVO.getTogetherCode());
        // 校验订单信息并获取订单DO对象
        OrderDO order = validateOrder(orderTogether);
        // 设置账户的相关信息
        account.setGuestName(orderTogether.getName())
                .setGuestSrcType(order.getGuestSrcType())
                .setRCode(order.getRCode())
                .setRNo(order.getRNo());
    }

    /**
     * 处理普通订单类型的账户
     *
     * @param accountList 账户DO对象
     * @param reqVO       请求VO对象
     */
    private void processGeneralOrder(List<AccountDO> accountList, BatchPayAccountSaveReqVO reqVO) {
        // 检查宾客代码是否存在
        if (StrUtil.isBlank(reqVO.getTogetherCode())) {
            throw exception(TOGETHER_CODE_NOT_EXISTS);
        }
        OrderTogetherDO orderTogether = orderTogetherService.getOrderTogether(reqVO.getTogetherCode());
        // 校验订单信息并获取订单DO对象
        OrderDO order = validateOrder(orderTogether);
        // 设置账户的相关信息
        accountList.forEach(account -> account.setGuestName(orderTogether.getName())
                .setGuestSrcType(order.getGuestSrcType())
                .setRCode(order.getRCode())
                .setRNo(order.getRNo()));
    }

    /**
     * 处理团队订单类型的账户
     *
     * @param account 账户DO对象
     * @param reqVO   请求VO对象
     */
    private void processTeam(AccountDO account, PayAccountSaveReqVO reqVO) {
        // 根据团队编号获取团队信息
        TeamDO team = teamService.getTeam(reqVO.getNo());
        // 如果团队不存在，抛出异常
        if (Objects.isNull(team)) {
            throw exception(TEAM_NOT_EXISTS);
        }
        // 设置账户的团队名称和来源类型
        account.setGuestName(team.getTeamName())
                .setRNo(AccountTypeEnum.GROUP.getName())
                .setGuestSrcType(team.getGuestSrcType());
    }

    /**
     * 处理团队订单类型的账户
     *
     * @param accountList 账户DO对象
     * @param reqVO       请求VO对象
     */
    private void processTeam(List<AccountDO> accountList, BatchPayAccountSaveReqVO reqVO) {
        // 根据团队编号获取团队信息
        TeamDO team = teamService.getTeam(reqVO.getNo());
        // 如果团队不存在，抛出异常
        if (Objects.isNull(team)) {
            throw exception(TEAM_NOT_EXISTS);
        }
        // 设置账户的团队名称和来源类型
        accountList.forEach(account -> account.setGuestName(team.getTeamName())
                .setRNo(AccountTypeEnum.GROUP.getName())
                .setGuestSrcType(team.getGuestSrcType()));

    }

    /**
     * 处理现付账订单类型的账户
     *
     * @param account 账户DO对象
     * @param reqVO   入参
     */
    private void processCash(AccountDO account, PayAccountSaveReqVO reqVO) {
        account.setAccType(AccountTypeEnum.CASH.getCode());
    }

    /**
     * 处理现付账订单类型的账户
     *
     * @param accountDOList 账户DO对象
     * @param reqVO         入参
     */
    private void processCash(List<AccountDO> accountDOList, BatchPayAccountSaveReqVO reqVO) {
        accountDOList.forEach(account -> account.setAccType(AccountTypeEnum.CASH.getCode()));

    }

    /**
     * 校验订单信息并返回订单DO对象
     *
     * @param orderTogether 团单信息DO对象
     * @return 订单DO对象
     */
    private OrderDO validateOrder(OrderTogetherDO orderTogether) {
        // 根据订单编号获取订单信息
        OrderDO order = orderService.getOrderByOrderNo(orderTogether.getOrderNo());
        // 如果订单不存在，抛出异常
        if (Objects.isNull(order)) {
            throw exception(ORDER_NOT_EXISTS);
        }
        return order;
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @LogRecord(success = PMS_ORDER_CONSUME_SUCCESS, type = PMS_ORDER_CONSUME_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#reqVO.no}}",
            extra = PMS_ORDER_property)
    public String consume(ConsumeAccountSaveReqVO reqVO) {
        // 验证
        OrderBO orderBO = validateConsumeAccount(reqVO);
        // 判断消费科目，赔偿、小商品需要维护物品/商品信息
        AccountDO consumeAccount = BeanUtils.toBean(reqVO, AccountDO.class);
        // 获得房费科目
        List<DictDataRespDTO> dictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT_ROOM_FEE.getCode()).getData();
        boolean isRoomFee = dictData.stream().anyMatch(dictDataRespDTO -> dictDataRespDTO.getCode().equals(reqVO.getConsume().getSubCode()));
        // 对房费相关账务增加间夜数
        if (isRoomFee && (StrUtil.isEmpty(reqVO.getIsAdjust()) || reqVO.getIsAdjust().equals(BooleanEnum.FALSE.getValue()))) {
            GroupParamConfigNightNumRespVO nightNum = getNightNum(reqVO.getGcode());
            buildNightNum(nightNum, consumeAccount, reqVO.getConsume().getSubCode(), orderBO.getCheckinType());
        }
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        //获得门店信息
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(reqVO.getHcode()).getData();
        AccountConvert.INSTANCE.consumeAccountConvert(reqVO, consumeAccount, shiftNo, bizDate);
        consumeAccount.setAfterTaxFee(calculateAfterTaxFee(reqVO.getGcode(), reqVO.getHcode(), reqVO.getConsume().getSubCode(), reqVO.getConsume().getFee()));
        consumeAccount.setAccDetail(buildBuyContent(reqVO));
        consumeAccount.setCurrencyUnit(merchantRespDTO.getCurrencyUnit());
        // 付款
        if (reqVO.getPay() != null) {
            String payNo = IdUtil.getSnowflakeNextIdStr();
            consumeAccount.setState(AccountStatusEnum.CLOSED.getCode())
                    .setIsCanRev(BooleanEnum.FALSE.getValue())
                    .setPayNo(payNo);
            if (consumeAccount.getPayBizDate() == null) {
                consumeAccount.setPayBizDate(bizDate);
                consumeAccount.setPayShiftNo(shiftNo);
                consumeAccount.setPayTime(LocalDateTime.now());
            }
            // 调用付款接口
            pay(buildPayAccountSaveReqVO(reqVO, orderBO, payNo));
        }
        consumeAccount.setRCode(orderBO.getRCode())
                .setRNo(orderBO.getRNo())
                .setGuestName(orderBO.getGuestName())
                .setGuestSrcType(orderBO.getGuestSrcType());
        if (reqVO.getPay() != null && reqVO.getPay().getSubCode() != null) {
            if (CREDIT_S_ACCOUNT.getCode().equals(reqVO.getPay().getSubCode())) {
                //ArSetDO arSet = arSetMapper.selectByArSetCode(reqVO.getPay().getPayCode(), gcode);
                ArSetDO arSet = arSetService.getArSet(reqVO.getPay().getPayCode(), reqVO.getGcode());
                if (Objects.equals(arSet.getCreditAccType(), NumberEnum.ONE.getNumber())) {
                    consumeAccount.setIsVerify(NumberEnum.ONE.getNumber());
                }
            }
        }


        accountMapper.insert(consumeAccount);
        // 记录赔偿/商品明细
        recordGoodsDetail(reqVO, consumeAccount);
        // 商品出库
        itemStockOut(reqVO, consumeAccount);
        // 零房费固化
        if (isRoomFee) {
            createZeroRoom(consumeAccount, orderBO);
        }

        //记录日志
        LogRecordContext.putVariable("order", reqVO);
        LogRecordContext.putVariable("account", consumeAccount);
        LogRecordContext.putVariable("subCode", ConsumeAccountEnum.getLabelByCode(consumeAccount.getSubCode()));

        return consumeAccount.getAccNo();
    }

    private void createZeroRoom(AccountDO consumeAccount, OrderBO orderBO) {
        if (consumeAccount.getFee() <= 0) {
            LocalDate today = LocalDate.now();
            List<OrderPriceDO> prices = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(consumeAccount.getGcode())
                    .setHcode(consumeAccount.getHcode()).setOrderNos(List.of(consumeAccount.getNo())));

            Long price = prices.stream()
                    .filter(orderPrice -> orderPrice.getPriceDate().equals(today))  // 过滤今天的价格
                    .findFirst()  // 获取今天的第一个价格（如果存在的话）
                    .orElseGet(() -> prices.stream()  // 如果没有今天的价格
                            .filter(orderPrice -> orderPrice.getPriceDate().equals(today.minusDays(1)))  // 过滤昨天的价格
                            .findFirst()  // 获取昨天的第一个价格（如果存在的话）
                            .orElseGet(() -> prices.isEmpty() ?  // 如果今天和昨天都没有价格
                                    new OrderPriceDO().setPrice(0L) :  // 如果列表为空，返回默认价格 0
                                    prices.getLast()  // 获取列表的最后一个价格
                            )
                    ).getVipPrice();

            ZeroRoomRateSaveReqDTO zeroRoomRateSaveReqDTO = new ZeroRoomRateSaveReqDTO();
            zeroRoomRateSaveReqDTO.setGcode(consumeAccount.getGcode());
            zeroRoomRateSaveReqDTO.setHcode(consumeAccount.getHcode());
            zeroRoomRateSaveReqDTO.setRNo(consumeAccount.getRNo());
            zeroRoomRateSaveReqDTO.setNo(consumeAccount.getNo());
            zeroRoomRateSaveReqDTO.setGuestName(consumeAccount.getGuestName());
            zeroRoomRateSaveReqDTO.setGuestSrcType(consumeAccount.getGuestSrcType());
            zeroRoomRateSaveReqDTO.setCheckinType(orderBO.getCheckinType());
            zeroRoomRateSaveReqDTO.setGuestCode(orderBO.getGuestCode());
            zeroRoomRateSaveReqDTO.setCheckinTime(orderBO.getCheckinTime());
            zeroRoomRateSaveReqDTO.setCheckoutTime(orderBO.getCheckoutTime());
            zeroRoomRateSaveReqDTO.setBizDate(consumeAccount.getBizDate());
            zeroRoomRateSaveReqDTO.setPrice(price);
            zeroRoomRateSaveReqDTO.setRoomFee(consumeAccount.getFee());
            zeroRoomRateSaveReqDTO.setRemark(consumeAccount.getRemark());
            zeroRoomRateReportApi.createZeroRoomRate(zeroRoomRateSaveReqDTO);
        }
    }


    /**
     * 获取集团夜审间夜数的配置
     *
     * @param gcode 集团代码
     * @return
     */
    private GroupParamConfigNightNumRespVO getNightNum(String gcode) {
        GroupParamConfigDO groupParamConfig = groupParamConfigService.getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_NIGHT_NUM.getParamType());
        return BeanUtils.toBean(groupParamConfig, GroupParamConfigNightNumRespVO.class);
    }


    private void itemStockOut(ConsumeAccountSaveReqVO reqVO, AccountDO consumeAccount) {
        if (ConsumeAccountEnum.GOODS.getCode().equals(reqVO.getConsume().getSubCode())) {
            // 获得仓库配置
            String warehouseConfig = generalConfigService.getWarehouseConfig(reqVO.getGcode(), reqVO.getHcode());
            // 判断是否开启配置
            if (NumberEnum.ONE.getNumber().equals(warehouseConfig)) {
                List<ErpStockOutSaveReqDTO.Item> itemList = getItemList(reqVO);
                CommonResult<Long> res = stockOutApi.createStockOut(ErpStockOutSaveReqDTO.builder()
                        .gcode(reqVO.getGcode())
                        .hcode(reqVO.getHcode())
                        .orderNo(consumeAccount.getNo())
                        .accountNo(consumeAccount.getAccNo())
                        .type(ErpPayCodeConstants.YES.getStatus())
                        .items(itemList).build());
                if (res.getCode() != 0) {
                    throw exception(FAILURE_REASON, res.getMsg());
                }
            }
        }

    }

    private void buildNightNum(GroupParamConfigNightNumRespVO nightNum, AccountDO consumeAccount, String subCode, String checkinType) {
        switch (ConsumeAccountEnum.getConsumeAccountEnumByCode(subCode)) {
            case ADD_ALL_DAY: // 加收全天
                consumeAccount.setNightNum(new BigDecimal(nightNum.getValue().getAddAllDay()));
                break;
            case ADD_HALF_DAY: //加收半天
                consumeAccount.setNightNum(new BigDecimal(nightNum.getValue().getAddHalfDay()));
                break;
            case HOUR_ROOM: //钟点房
                consumeAccount.setNightNum(new BigDecimal(nightNum.getValue().getHour()));
                break;
            case ROOM_FEE: // 系统自动计费 小时房采用小时房间夜数设置 全天房采用全天房间夜数设置
                if (CheckInTypeEnum.HOUR_ROOM.getCode().equals(checkinType)) {
                    consumeAccount.setNightNum(new BigDecimal(nightNum.getValue().getHour()));
                } else {
                    consumeAccount.setNightNum(new BigDecimal(nightNum.getValue().getAllDay()));
                }
                break;
            default:   // 手工房价
                consumeAccount.setNightNum(new BigDecimal(nightNum.getValue().getHand()));
        }
    }


    /**
     * 消费时如果有付款，那么封装付款信息
     *
     * @param reqVO 入参
     * @return 付款信息
     */
    private PayAccountSaveReqVO buildPayAccountSaveReqVO(ConsumeAccountSaveReqVO reqVO, OrderBO orderBO, String payNo) {
        PayAccountSaveReqVO payAccountSaveReqVO = new PayAccountSaveReqVO();
        payAccountSaveReqVO.setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setNo(reqVO.getNo())
                .setTogetherCode(reqVO.getTogetherCode())
                .setAccType(reqVO.getAccType())
                .setFee(reqVO.getPay().getFee())
                .setSubCode(reqVO.getPay().getSubCode())
                .setPayCode(reqVO.getPay().getPayCode())
                .setBankType(reqVO.getPay().getBankType())
                .setBankCardNo(reqVO.getPay().getBankCardNo())
                .setValidDate(reqVO.getPay().getValidDate())
                .setMcode(reqVO.getPay().getMcode())
                .setPhone(reqVO.getPay().getPhone())
                .setStoreCardNo(reqVO.getPay().getPayCode())
                .setPwd(reqVO.getPay().getPwd())
                .setSmsCode(reqVO.getSmsCode())
                .setIsSendSms(reqVO.getIsSendSms())
                .setVerifyMode(reqVO.getVerifyMode())
                .setRemark(reqVO.getRemark());
        if (reqVO.getConsume() != null) {
            payAccountSaveReqVO.setAccDetail(reqVO.getConsume().getAccDetail())
                    .setPayNo(payNo)
                    .setState(AccountStatusEnum.CLOSED.getCode());
            PayAccountSaveReqVO.OrderTogether orderTogether = BeanUtils.toBean(orderBO, PayAccountSaveReqVO.OrderTogether.class);
            payAccountSaveReqVO.setOrderTogether(orderTogether);
        }
        return payAccountSaveReqVO;
    }

    private ConsumeSaveReqDTO buildConsumeSaveReqDTO(PayAccountSaveReqVO reqVO) {
        ConsumeSaveReqDTO consumeSaveReqDTO = new ConsumeSaveReqDTO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setOrderNo(reqVO.getNo())
                .setMcode(reqVO.getMcode())
                .setPhone(reqVO.getPhone())
                .setOrderType(CouponOrderTypeEnum.ROOM.getCode())
                .setFee(reqVO.getFee())
                .setScene(ConsumeSceneEnum.ROOM.getCode())
                .setPayMethod(reqVO.getSubCode())
                .setStoreCardNo(reqVO.getStoreCardNo())
                .setPwd(reqVO.getPwd())
                .setConsumeType(Objects.equals(reqVO.getAccType(), AccountTypeEnum.CASH.getCode()) ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());

        return consumeSaveReqDTO;
    }

    private String buildBuyContent(ConsumeAccountSaveReqVO createReqVO) {
        String content = "";
        if (ObjectUtil.isNotEmpty(createReqVO.getConsume()) && ObjectUtil.isNotEmpty(createReqVO.getConsume().getAccDetail())) {
            content = createReqVO.getConsume().getAccDetail();
            return content;
        }
        if (CollUtil.isNotEmpty(createReqVO.getGoodsDetails())) {
            // 返回内容为商品名称：价格*数量
            content = createReqVO.getGoodsDetails().stream().map(goodsDetail -> goodsDetail.getGoodsName() + ":"
                    + MoneyUtils.fenToYuanStr(goodsDetail.getPrice().intValue()) + "*" + goodsDetail.getNum()).collect(Collectors.joining(","));
        }
        if (CollUtil.isNotEmpty(createReqVO.getIndemnityGoodsDetails())) {
            content = createReqVO.getIndemnityGoodsDetails().stream().map(indemnityGoodsDetail -> indemnityGoodsDetail.getIndemnityName() + ":"
                    + MoneyUtils.fenToYuanStr(indemnityGoodsDetail.getPrice().intValue()) + "*" + indemnityGoodsDetail.getNum()).collect(Collectors.joining(","));
        }

        return content;
    }

    /**
     * 处理商品信息，与仓库进行关联
     *
     * @param createReqVO
     * @return
     */
    private List<ErpStockOutSaveReqDTO.Item> getItemList(ConsumeAccountSaveReqVO createReqVO) {
        List<ErpStockOutSaveReqDTO.Item> itemList = new ArrayList<>();
        Long stockId;
        CommonResult<Long> res = stockApi.selectDefaultStock(createReqVO.getHcode());
        if (res.getCode() == 0) {
            stockId = res.getData();
        } else {
            throw exception(FAILURE_REASON, res.getMsg());
        }
        // 处理商品详情列表
        if (CollUtil.isNotEmpty(createReqVO.getGoodsDetails())) {
            itemList.addAll(createReqVO.getGoodsDetails().stream().map(goodsDetail -> ErpStockOutSaveReqDTO.Item.builder()
                    .goodsCode(Long.valueOf(goodsDetail.getGoodsCode())) // 商品ID
                    .warehouseId(stockId) // 设置默认仓库
                    .productPrice(goodsDetail.getPrice()) // 商品价格
                    .count(new BigDecimal(goodsDetail.getNum())) // 商品数量
                    .build()).toList());
        }
        return itemList;
    }

    /**
     * 计算税后金额
     *
     * @param gcode   集团代码
     * @param hcode   门店代码
     * @param subCode 科目代码
     * @param fee     原始金额
     * @return 税后金额
     */
    private Long calculateAfterTaxFee(String gcode, String hcode, String subCode, Long fee) {
        return taxConfigService.calculateAfterTaxFee(gcode, hcode, subCode, fee);
    }

    /**
     * 计算税后金额
     *
     * @param taxConfigDO 税费规则
     * @param fee         金额
     * @return 根据规则计算后的税后金额
     */
    private Long calculateAfterTaxFee(TaxConfigDO taxConfigDO, Long fee) {
        return taxConfigService.calculateAfterTaxFee(taxConfigDO.getTaxRate(), fee);
    }

    /**
     * 验证消费账务
     *
     * @param reqVO 入参
     */
    private OrderBO validateConsumeAccount(ConsumeAccountSaveReqVO reqVO) {
        OrderBO orderBO = new OrderBO();
        // 如果是赔偿或者小商品的消费，要验证商品总金额与付款金额相等
        if (ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(reqVO.getConsume().getSubCode())) {
            if (CollUtil.isEmpty(reqVO.getIndemnityGoodsDetails())) {
                throw exception(GOODS_DETAILS_NOT_EXISTS);
            }
            if (reqVO.getIndemnityGoodsDetails().stream().mapToLong(detail -> detail.getPrice() * detail.getNum()).sum() != reqVO.getConsume().getFee()) {
                throw exception(GOODS_DETAILS_TOTAL_PRICE_NOT_EQUAL_FEE);
            }
        }
        if (ConsumeAccountEnum.GOODS.getCode().equals(reqVO.getConsume().getSubCode())) {
            if (CollUtil.isEmpty(reqVO.getGoodsDetails())) {
                throw exception(GOODS_DETAILS_NOT_EXISTS);
            }
            if (reqVO.getGoodsDetails().stream().mapToLong(detail -> detail.getPrice() * detail.getNum()).sum() != reqVO.getConsume().getFee()) {
                throw exception(GOODS_DETAILS_TOTAL_PRICE_NOT_EQUAL_FEE);
            }
        }
        if (reqVO.getPay() != null) {
            // 验证消费金额是否与付款金额相等
            if (!Objects.equals(reqVO.getConsume().getFee(), reqVO.getPay().getFee())) {
                throw exception(CONSUME_FEE_NOT_EQUAL_PAY_FEE);
            }
        }
        // 如果是订单，要验证订单状态。 如果是预订单，要验证预订单状态，如果是团队订单，要验证团队订单状态 TODO
        switch (AccountTypeEnum.getAccountTypeEnumByCode(reqVO.getAccType())) {
            case GENERAL: // 普通账务
                OrderDO order = orderService.getOrderByOrderNo(reqVO.getNo());
                OrderTogetherDO orderTogether = orderTogetherService.getOrderTogether(reqVO.getTogetherCode());
                if (Objects.isNull(order) || Objects.isNull(orderTogether)) {
                    throw exception(ORDER_NOT_EXISTS);
                }
                if (!Objects.equals(orderTogether.getState(), OrderStateEnum.CHECK_IN.getCode()) && !Objects.equals(orderTogether.getState(), OrderStateEnum.CREDIT.getCode())) {
                    throw exception(ORDER_STATUS_NOT_ALLOW_CONSUME, orderTogether.getName());
                }
                orderBO.setGuestName(orderTogether.getName()).setRNo(orderTogether.getRNo())
                        .setRCode(orderTogether.getRCode()).setGuestSrcType(order.getGuestSrcType())
                        .setCheckinType(orderTogether.getCheckinType()).setGuestCode(order.getGuestCode())
                        .setCheckinTime(order.getCheckinTime()).setCheckoutTime(order.getCheckoutTime());
                break;
            case BOOK: // 预订单账务
                BookDO book = bookService.getBookByBookNo(reqVO.getNo());
                if (Objects.isNull(book)) {
                    throw exception(BOOK_NOT_EXISTS);
                }
                if (!Objects.equals(book.getState(), OrderStateEnum.IN_BOOKING.getCode())) {
                    throw exception(BOOK_STATUS_NOT_ALLOW_CONSUME);
                }
                validPay(reqVO.getGcode(), reqVO.getHcode(), reqVO.getAccType(), reqVO.getNo());
                orderBO.setGuestSrcType(book.getGuestSrcType()).setGuestName(book.getGuestName()).setGuestCode(book.getGuestCode());
                break;
            case GROUP: // 团队主账
                TeamDO team = teamService.getTeam(reqVO.getNo());
                if (Objects.isNull(team)) {
                    throw exception(TEAM_NOT_EXISTS);
                }
                if (!List.of(OrderStateEnum.IN_BOOKING.getCode(), OrderStateEnum.CHECK_IN.getCode()).contains(team.getState())) {
                    throw exception(TEAM_STATUS_NOT_ALLOW_CONSUME);
                }
                orderBO.setGuestName(team.getTeamName()).setGuestSrcType(team.getGuestSrcType())
                        .setCheckinTime(team.getCheckinTime());
                break;
            case CASH: // 现付账
                break;
            default:
                throw exception(ACCOUNT_TYPE_NOT_SUPPORT);
        }
        return orderBO;
    }

    /**
     * 消费 赔偿、商品明细
     *
     * @param reqVO
     */
    private void recordGoodsDetail(ConsumeAccountSaveReqVO reqVO, AccountDO consumeAccount) {
        List<GoodsSellRecordDO> createGoodsSellRecordList = CollUtil.newArrayList();
        // 赔偿需要记录赔偿物品
        if (ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(reqVO.getConsume().getSubCode())) {
            reqVO.getIndemnityGoodsDetails().forEach(indemnityGoodsDetail -> {
                createGoodsSellRecordList.add(GoodsSellRecordDO.builder()
                        .gcode(reqVO.getGcode())
                        .hcode(reqVO.getHcode())
                        .recordCode(IdUtil.getSnowflakeNextIdStr())
                        .orderNo(reqVO.getNo())
                        .num(indemnityGoodsDetail.getNum())
                        .price(indemnityGoodsDetail.getPrice())
                        .totalPrice(indemnityGoodsDetail.getPrice() * indemnityGoodsDetail.getNum())
                        .goodsCode(indemnityGoodsDetail.getIndemnityCode())
                        .goodsName(indemnityGoodsDetail.getIndemnityName())
                        .accType(Objects.equals(reqVO.getAccType(), AccountTypeEnum.CASH.getCode()) ? NumberEnum.ONE.getNumber() : NumberEnum.ZERO.getNumber())
                        .recordType(NumberEnum.ONE.getNumber())
                        .accNo(consumeAccount.getAccNo())
                        .shiftNo(consumeAccount.getShiftNo())
                        .bizDate(consumeAccount.getBizDate())
                        .shiftNo(consumeAccount.getShiftNo())
                        .categoryId(indemnityGoodsDetail.getThingCode())
                        .categoryName(indemnityGoodsDetail.getThingName())
                        .build());
            });
        }
        if (ConsumeAccountEnum.GOODS.getCode().equals(reqVO.getConsume().getSubCode())) {
            reqVO.getGoodsDetails().forEach(goodsDetail -> {
                createGoodsSellRecordList.add(GoodsSellRecordDO.builder()
                        .gcode(reqVO.getGcode())
                        .hcode(reqVO.getHcode())
                        .recordCode(IdUtil.getSnowflakeNextIdStr())
                        .orderNo(reqVO.getNo())
                        .num(goodsDetail.getNum())
                        .price(goodsDetail.getPrice())
                        .totalPrice(goodsDetail.getPrice() * goodsDetail.getNum())
                        .goodsCode(goodsDetail.getGoodsCode())
                        .goodsName(goodsDetail.getGoodsName())
                        .accType(Objects.equals(reqVO.getAccType(), AccountTypeEnum.CASH.getCode()) ? NumberEnum.ONE.getNumber() : NumberEnum.ZERO.getNumber())
                        .recordType(NumberEnum.ZERO.getNumber())
                        .accNo(consumeAccount.getAccNo())
                        .shiftNo(consumeAccount.getShiftNo())
                        .bizDate(consumeAccount.getBizDate())
                        .shiftNo(consumeAccount.getShiftNo())
                        .categoryId(goodsDetail.getThingCode())
                        .categoryName(goodsDetail.getCategoryName())
                        .build());
            });
        }
        if (CollUtil.isEmpty(createGoodsSellRecordList)) {
            return;
        }
        goodsSellRecordService.createGoodsSellRecords(createGoodsSellRecordList);
    }

    /**
     * 如果是AR账，根据账号代码获取挂账对象类型
     *
     * @param arSet AR账套
     * @return
     */
    private KeyValue<String, List<String>> getPaType(ArSetDO arSet) {
        List<String> list = new ArrayList<>();
        if (Objects.isNull(arSet)) {
            throw exception(AR_SET_NOT_EXISTS);
        }
        ProtocolAgentDO protocolAgent = protocolAgentService.getProtocolAgent(arSet.getUnitCode());
        if (Objects.isNull(protocolAgent)) {
            throw exception(PROTOCOL_AGENT_NOT_EXISTS);
        }
        list.add(arSet.getArSetName());
        list.add(arSet.getCreditAccType());
        return NumberEnum.ZERO.getNumber().equals(protocolAgent.getPaType()) ?
                new KeyValue<>(CreditTargetTypeEnum.PROTOCOL.getCode(), list) :
                new KeyValue<>(CreditTargetTypeEnum.AGENT.getCode(), list);
    }

    @Override
    public List<CloseAccountRespVO> getCloseAccountList(CloseAccountRepVO r) {
        List<CloseAccountRespVO> resp = CollUtil.newArrayList();
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(r.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(r.getHcode());
        resp = accountMapper.getCloseAccountSumByTogetherCode(r.getGcode(), r.getHcode(), r.getTogetherCode(), shiftNo, bizDate);
        resp = filterCloseAccountList(resp);
        resp.forEach(closeAccountRespVO -> {
            closeAccountRespVO.setPayShiftName(ShiftTypeEnum.getNameByCode(closeAccountRespVO.getPayShiftNo()));
        });
        return resp;
    }

    private List<CloseAccountRespVO> filterCloseAccountList(List<CloseAccountRespVO> resp) {
        // 按 payNo 分组
        Map<String, List<CloseAccountRespVO>> groupedByPayNo = resp.stream()
                .collect(Collectors.groupingBy(CloseAccountRespVO::getPayNo));

        // 对每组进行筛选，仅保留 fee > 0 的记录
        return groupedByPayNo.values().stream()
                .flatMap(group -> group.stream()
                        .filter(item -> item.getFee() >= 0)
                        .limit(1)) // 确保只保留一个元素
                .collect(Collectors.toList());
    }

    @Override
    public List<CloseAccountPayNoRespVO> getAccountByPayNo(String gcode, String hcode, String payNo) {
        List<AccountDO> accountDOList = accountMapper.selectListByPayNo(gcode, hcode, payNo);
        List<CloseAccountPayNoRespVO> closeAccountPayNoRespVOS = BeanUtils.toBean(accountDOList, CloseAccountPayNoRespVO.class);
        List<DictDataRespDTO> subList = dictDataApi.getDictDataListByDicTypes(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())).getData();
        Map<String, String> subMap = CollectionUtils.convertMap(subList, DictDataRespDTO::getCode, DictDataRespDTO::getLabel);
        closeAccountPayNoRespVOS.forEach(account -> {
            account.setSubName(subMap.getOrDefault(account.getSubCode(), ""));
        });
        return closeAccountPayNoRespVOS;
    }

    @Override
    @Lock4j(keys = {"#r.gcode", "#r.hcode", "#r.payNo"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_ORDER_UNDO_PAY_SUCCESS, type = PMS_ORDER_UNDO_PAY_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public void undoPay(CloseAccountPayNoRepVO r) {
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(r.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(r.getHcode());
        // 1. 验证账务是否在当前营业日
        List<AccountDO> accountList = validateUndoPay(r, bizDate);
        // 2. 修改账务状态为未结
        List<AccountDO> undoAccountList = CollectionUtil.map(accountList, account -> AccountDO.builder()
                .id(account.getId())
                .state(AccountStatusEnum.UNCLOSED.getCode())
                .payNo(null)
                .payTime(null)
                .payShiftNo(null)
                .payer(null)
                .handleShiftNo(shiftNo)
                .build(), true);
        // 2.1 批量修改
        accountMapper.updateBatchById(undoAccountList);
        // 记录日志
        LogRecordContext.putVariable("payNo", r.getPayNo());
        LogRecordContext.putVariable("order", accountList.getFirst());
    }

    /**
     * 验证撤销的账务是否在当前营业日和当前班次
     *
     * @param r       入参
     * @param bizDate 营业日
     * @return 账务列表
     */
    private List<AccountDO> validateUndoPay(CloseAccountPayNoRepVO r, LocalDate bizDate) {
        List<AccountDO> accountDOList = accountMapper.selectListByPayNo(r.getGcode(), r.getHcode(), r.getPayNo());
        if (CollUtil.isEmpty(accountDOList)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        AccountDO account = accountDOList.getFirst();
        if (!account.getBizDate().isEqual(bizDate)) {
            throw exception(ACCOUNT_NOT_CURRENT_SHIFT);
        }
        boolean isMergeAccount = accountDOList.stream().anyMatch(accountDO -> MERGE_ACCOUNT.getCode().equals(accountDO.getSubCode()));
        if (isMergeAccount) {
            throw exception(ACCOUNT_MERGE_NOT_SUPPORT);
        }
        return accountDOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_ORDER_ACCOUNT_OPERATION_SUCCESS, type = PMS_ORDER_SPLIT_ACCOUNT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public void splitAccount(SplitAccountReqVO reqVO) {
        // 1 验证
        AccountDO account = validateSplitAccount(reqVO);
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        // 结帐号
        String payNo = IdUtil.getSnowflakeNextIdStr();
        LocalDateTime now = LocalDateTime.now();
        // 2 拆账
        // 2.1 修改原账务状态，并新增一条负的账务
        AccountDO oldAccount = AccountConvert.INSTANCE.splitOldAccountConvert(account, shiftNo, bizDate, payNo, now);
        AccountDO newAccount = BeanUtils.toBean(account, AccountDO.class);
        AccountConvert.INSTANCE.splitNewAccountConvert(newAccount, account, shiftNo, bizDate, payNo, now);
        // 2.2 新增拆分后的账务
        List<AccountDO> newAccounts = CollUtil.newArrayList();
        newAccounts.add(newAccount);
        if (DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(newAccount.getSubType())) {
            // 根据拆账的比例计算税后金额
            calculateSplitTaxFee(account.getAfterTaxFee(), account.getFee(), reqVO);
        }
        reqVO.getAccountList().forEach(splitAcc -> {
            AccountDO newSplitAccount = BeanUtils.toBean(account, AccountDO.class);
            AccountConvert.INSTANCE.splitNewAccountConvert(newSplitAccount, splitAcc, account, shiftNo, bizDate);
            newAccounts.add(newSplitAccount);
        });
        accountMapper.updateById(oldAccount);
        accountMapper.insertBatch(newAccounts);

        // 记录日志
        accountLogHandle(List.of(oldAccount), newAccounts);
    }

    /**
     * 计算拆账后等比例的税后金额
     *
     * @param afterTaxFee 原税后金额
     * @param fee         原金额
     * @param reqVO
     */
    private void calculateSplitTaxFee(Long afterTaxFee, Long fee, SplitAccountReqVO reqVO) {
        List<SplitAccountReqVO.Account> accountList = reqVO.getAccountList();
        BigDecimal totalAfterTaxFee = new BigDecimal(afterTaxFee);
        BigDecimal totalFee = new BigDecimal(fee);
        BigDecimal allocatedAfterTaxFee = BigDecimal.ZERO;

        for (int i = 0; i < accountList.size(); i++) {
            BigDecimal accountFee = new BigDecimal(accountList.get(i).getFee());
            // 计算比例，保留10位小数以确保精度
            BigDecimal proportion = accountFee.divide(totalFee, 10, RoundingMode.HALF_UP);
            BigDecimal currentAfterTaxFee;

            if (i == accountList.size() - 1) {
                // 最后一个账户的税后金额是剩余的税后金额
                currentAfterTaxFee = totalAfterTaxFee.subtract(allocatedAfterTaxFee);
            } else {
                currentAfterTaxFee = totalAfterTaxFee.multiply(proportion);
                allocatedAfterTaxFee = allocatedAfterTaxFee.add(currentAfterTaxFee);
            }
            accountList.get(i).setAfterTaxFee(currentAfterTaxFee.longValue()); // 直接截取整数部分
        }
    }

    /**
     * 验证拆分账务
     *
     * @param reqVO
     * @return
     */
    private AccountDO validateSplitAccount(SplitAccountReqVO reqVO) {
        AccountDO account = validateAccount(reqVO.getAccNo());
        if (CollUtil.isEmpty(reqVO.getAccountList())) {
            throw exception(ACCOUNT_SEPARATE_ERROR_NO_PAY);
        }
        // 验证拆分后的账务之和与原账是否相等
        if (reqVO.getAccountList().stream().mapToLong(SplitAccountReqVO.Account::getFee).sum() != account.getFee()) {
            throw exception(ACCOUNT_SEPARATE_ERROR);
        }
        // 已结账务不能拆分
        if (!AccountStatusEnum.UNCLOSED.getCode().equals(account.getState())) {
            throw exception(ACCOUNT_SEPARATE_ERROR_VERIFY);
        }
        // 部分科目不允许拆分
        if (DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(account.getSubType())) {
            if (ObjectUtils.equalsAny(account.getSubCode(), ConsumeAccountEnum.MEMBER_CARD.getCode(), ConsumeAccountEnum.BK_FEE.getCode())) {
                throw exception(ACCOUNT_SEPARATE_ERROR_MEMBER_CARD);
            }
        }
        // 付款科目只支持支持现金和银行卡支付
        if (DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(account.getSubType())) {
            if (!ObjectUtils.equalsAny(account.getSubCode(), PayAccountEnum.RMB_RECEIPT.getCode(), PayAccountEnum.BANK_CARD.getCode())) {
                throw exception(ACCOUNT_SEPARATE_ERROR_PAY);
            }
        }
        return account;
    }


    /**
     * 前端传过来的值
     * 账务
     * 接待： 所有房间账务  no: 订单号列表, noType: orderList, togetherCode: 第一个订单的第一个客单代码, state: ''
     * 团队接待：所有账务，包括房间和团队的账务  no: teamCode, noType: teamReception, togetherCode: teamCode, state: ''
     * 团队主单：团队主单账务 no: teamCode, noType: teamMain, togetherCode: teamCode, state: ''
     * 房间：房间下账务 no: orderNo, noType: room
     * 客人：客人账务 no: orderNo, noType: order, togetherCode: togetherCode, state: 客单状态
     * 预订单: 预订单单账务 no: bookNo, noType: book or team, togetherCode: bookNo, state: 预订单状态
     */
    @Override
    public List<TogetherAccRespVO> getRecordAccountList(String gcode, String hcode, String noType, List<String> no) {
        List<TogetherAccRespVO> togetherAccRespVOList = new ArrayList<>();

        switch (NoTypeEnum.getEnumByCode(noType)) {
            case NoTypeEnum.BOOK:
            case NoTypeEnum.TEAM:
                handleBookOrTeam(togetherAccRespVOList, gcode, hcode, no);
                break;
            case NoTypeEnum.TEAM_MAIN:
                handleTeamMain(togetherAccRespVOList, no);
                break;
            case NoTypeEnum.ORDER_LIST:
                handleOrderList(togetherAccRespVOList, gcode, hcode, no);
                break;
            case NoTypeEnum.TEAM_RECEPTION:
                handleTeamReception(togetherAccRespVOList, gcode, hcode, no);
                break;
            case NoTypeEnum.ORDER:
                handleOrder(togetherAccRespVOList, no);
                break;
            default:
                throw exception(NOTYPE_NO_SUPPORT);
        }

        return togetherAccRespVOList;
    }

    private void handleBookOrTeam(List<TogetherAccRespVO> list, String gcode, String hcode, List<String> no) {
        List<BookDO> books = bookService.getBookList(new BookListReqVO().setGcode(gcode).setHcode(hcode).setBookNos(no));
        if (CollUtil.isEmpty(books)) {
            throw exception(BOOK_NOT_EXISTS);
        }
        books.forEach(book -> list.add(createTogetherAccRespVO(book.getBookNo(), book.getBookNo(), "", book.getContact(), AccountTypeEnum.BOOK.getCode())));
    }

    private void handleTeamMain(List<TogetherAccRespVO> list, List<String> no) {
        TeamDO team = teamService.getTeam(no.getFirst());
        if (team == null) {
            throw exception(TEAM_NOT_EXISTS);
        }
        list.add(createTogetherAccRespVO(team.getTeamCode(), team.getTeamCode(), NoTypeEnum.TEAM_MAIN.getName(), team.getTeamName(), AccountTypeEnum.GROUP.getCode()));
    }

    private void handleOrderList(List<TogetherAccRespVO> list, String gcode, String hcode, List<String> no) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode)
                .setHcode(hcode).setBindCodes(no).setStates(List.of(OrderStateEnum.CHECK_IN.getCode())));
        List<String> orderNos = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
        List<OrderTogetherDO> orderTogetherDOS = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode)
                .setHcode(hcode).setOrderNos(orderNos));
        if (CollUtil.isEmpty(orderTogetherDOS)) {
            throw exception(ORDER_NOT_EXISTS);
        }
        orderTogetherDOS.forEach(orderTogetherDO -> list.add(createTogetherAccRespVO(
                orderTogetherDO.getOrderNo(), orderTogetherDO.getTogetherCode(), orderTogetherDO.getRNo(), orderTogetherDO.getName(), AccountTypeEnum.GENERAL.getCode())));
    }

    private void handleTeamReception(List<TogetherAccRespVO> list, String gcode, String hcode, List<String> no) {
        TeamDO teamDO = teamService.getTeam(no.getFirst());
        if (teamDO == null) {
            throw exception(TEAM_NOT_EXISTS);
        }
        list.add(createTogetherAccRespVO(no.getFirst(), no.getFirst(), NoTypeEnum.TEAM_MAIN.getName(), teamDO.getTeamName(), AccountTypeEnum.GROUP.getCode()));

        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode)
                .setHcode(hcode).setTeamCode(no.getFirst()).setStates(List.of(OrderStateEnum.CHECK_IN.getCode())));
        List<String> orderNos = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
        List<OrderTogetherDO> orderTogetherDOS = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode)
                .setHcode(hcode).setOrderNos(orderNos));
        if (CollUtil.isEmpty(orderTogetherDOS)) {
            throw exception(ORDER_NOT_EXISTS);
        }
        orderTogetherDOS.forEach(orderTogetherDO -> list.add(createTogetherAccRespVO(
                orderTogetherDO.getOrderNo(), orderTogetherDO.getTogetherCode(), orderTogetherDO.getRNo(), orderTogetherDO.getName(), AccountTypeEnum.GENERAL.getCode())));
    }

    private void handleOrder(List<TogetherAccRespVO> list, List<String> no) {
        OrderTogetherDO orderTogetherDO = orderTogetherService.getOrderTogether(no.getFirst());
        if (orderTogetherDO == null) {
            throw exception(ORDER_NOT_EXISTS);
        }
        list.add(createTogetherAccRespVO(
                orderTogetherDO.getOrderNo(), orderTogetherDO.getTogetherCode(), orderTogetherDO.getRNo(), orderTogetherDO.getName(), AccountTypeEnum.GENERAL.getCode()));
    }

    private TogetherAccRespVO createTogetherAccRespVO(String no, String togetherCode, String rNo, String name, String accType) {
        return new TogetherAccRespVO().setNo(no).setTogetherCode(togetherCode).setRNo(rNo).setName(name).setAccType(accType);
    }


    @Deprecated
    @Override
    public List<TogetherAccRespVO> getRecordAccountList(String gcode, String hcode) {
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode).setHcode(hcode).setState(OrderStateEnum.CHECK_IN.getCode()));
        List<TeamDO> teamList = teamService.getTeamList(new TeamReqVO().setGcode(gcode).setHcode(hcode).setState(OrderStateEnum.CHECK_IN.getCode()));
        List<TogetherAccRespVO> togetherAccRespVOList = new ArrayList<>();
        orderTogetherList.forEach(orderTogether -> {
            togetherAccRespVOList.add(new TogetherAccRespVO().setTogetherCode(orderTogether.getTogetherCode()).setRNo(orderTogether.getRNo()).setName(orderTogether.getName()));
        });
        teamList.forEach(team -> {
            togetherAccRespVOList.add(new TogetherAccRespVO().setTogetherCode(team.getTeamCode()).setRNo(AccountTypeEnum.GROUP.getName()).setName(team.getTeamName()));
        });
        return togetherAccRespVOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_ORDER_ACCOUNT_OPERATION_SUCCESS, type = PMS_ORDER_TRANSFER_ACCOUNT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public void transferAccount(TransferOutAccountReqVO reqVO) {
        // 验证
        String guestSrcType = getGuestSrcType(reqVO);
        List<AccountDO> accountList = validateTransferAccount(reqVO);
        // 转出
        // 转出账务修改状态，并新增一条账务来平衡
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        String payNo = IdUtil.getSnowflakeNextIdStr();
        LocalDateTime now = LocalDateTime.now();
        // 非团队需要根据房号获取房间信息
        String rCode = getRcode(reqVO);
        // 1 添加平衡账务
        List<AccountDO> newAccountList = BeanUtils.toBean(accountList, AccountDO.class);
        newAccountList.forEach(newAccount -> AccountConvert.INSTANCE.newAccountConvert(reqVO, newAccount, shiftNo, bizDate, payNo, now));

        // 2 转入账
        List<AccountDO> turnInAccountList = BeanUtils.toBean(accountList, AccountDO.class);
        turnInAccountList.forEach(turnInAccount -> AccountConvert.INSTANCE.turnInAccountConvert(reqVO, turnInAccount, shiftNo, bizDate, rCode, guestSrcType));

        //3  转出账务需要更改
        List<AccountDO> updateAccountList = CollUtil.newArrayList();
        accountList.forEach(account -> updateAccountList.add(AccountConvert.INSTANCE.updateAccountConvert(reqVO, account, shiftNo, bizDate, payNo, now)));
        List<AccountDO> accountDOS = (List<AccountDO>) CollUtil.addAll(newAccountList, turnInAccountList);
        accountMapper.insertBatch(accountDOS);
        accountMapper.updateBatch(updateAccountList);
        // 记录日志
        accountLogHandle(updateAccountList, accountDOS);
    }

    private String getGuestSrcType(TransferOutAccountReqVO reqVO) {
        String guestSrcType;
        // 如果是转入团队主单，验证团队是否在住
        if (BooleanEnum.TRUE.getValue().equals(reqVO.getIsTeam())) {
            TeamDO team = teamService.getTeam(reqVO.getTogetherCode());
            if (!OrderStateEnum.CHECK_IN.getCode().equals(team.getState())) {
                throw exception(ACCOUNT_TRANSFER_ERROR_TEAM_STATE);
            }
            guestSrcType = team.getGuestSrcType();
        } else {
            if (StrUtil.isBlank(reqVO.getRNo())) {
                throw exception(ACCOUNT_TRANSFER_ERROR_RNO);
            }
            // 如果是从预订单账务转，那么不需要判断是否存在客单，因为这种情况只会出现在预订单第一个人入住时
            if (!Objects.equals(reqVO.getIsBookAccount(), BooleanEnum.TRUE.getValue())) {
                // 验证转入的账号是否存在,只能转入在住宾客
                OrderTogetherDO orderTogether = orderTogetherService.getOrderTogether(reqVO.getTogetherCode());
                if (!OrderStateEnum.CHECK_IN.getCode().equals(orderTogether.getState())) {
                    throw exception(ACCOUNT_TRANSFER_ERROR_TOGETHER_STATE);
                }
            }
            OrderDO order = orderService.getOrderByOrderNo(reqVO.getNo());
            guestSrcType = order.getGuestSrcType();
        }
        return guestSrcType;
    }

    private String getRcode(TransferOutAccountReqVO reqVO) {
        // 非团队需要根据房号获取房间信息
        String rCode;
        if (BooleanEnum.FALSE.getValue().equals(reqVO.getIsTeam())) {
            RoomRespVO room = roomService.getRoomByRNo(reqVO.getHcode(), reqVO.getRNo());
            if (ObjectUtil.isNull(room)) {
                throw exception(ROOM_NOT_EXIST);
            }
            rCode = room.getRCode();
        } else {
            rCode = null;
        }
        return rCode;
    }

    private List<AccountDO> validateTransferAccount(TransferOutAccountReqVO reqVO) {
        // 验证转的账务是否存在，且要符合转出的条件 1.未结账务 2.未冲红 3.非AR账
        List<AccountDO> accountList = accountMapper.selectList(AccountDO::getAccNo, reqVO.getAccNoList());
        if (CollUtil.isEmpty(accountList) || accountList.size() != reqVO.getAccNoList().size()) {
            throw exception(ACCOUNT_TRANSFER_ERROR_ACCOUNT);
        }
        if (accountList.stream().anyMatch(account -> !AccountStatusEnum.UNCLOSED.getCode().equals(account.getState()))) {
            throw exception(ACCOUNT_TRANSFER_ERROR_ACCOUNT2);
        }
        if (accountList.stream().anyMatch(account -> CREDIT_S_ACCOUNT.getCode().equals(account.getSubCode()))) {
            throw exception(ACCOUNT_TRANSFER_ERROR_ACCOUNT3);
        }
        // 不能将账务转给自己
        if (accountList.stream().anyMatch(account -> account.getTogetherCode().equals(reqVO.getTogetherCode()))) {
            throw exception(ACCOUNT_TRANSFER_ERROR_ACCOUNT4);
        }
        // 挂账账务不能转出
        if (accountList.stream().anyMatch(account -> !BooleanEnum.FALSE.getValue().equals(account.getCreditTargetType()))) {
            throw exception(ACCOUNT_TRANSFER_ERROR_ACCOUNT5);
        }
        // 优惠券账务不能转出
        if (accountList.stream().anyMatch(account -> COUPON.getCode().equals(account.getSubCode()))) {
            throw exception(COUPON_ACCOUNT_NOT_TRANSFER);
        }
        return accountList;
    }

    @Override
    public void validateMoreRedScanGanAccount(AccountSelectedReqVO r) {
        LocalDate bizDate = generalConfigService.getBizDate(r.getHcode());
        List<AccountDO> accountList = validateRedAccount(r.getAccNos(), bizDate);
        // 判断账务列表中是否存在多条扫码付的付款账务
        if (CollectionUtils.filterList(accountList, a -> List.of(PayAccountEnum.SCAN_GUN_ALIPAY.getCode(), PayAccountEnum.SCAN_GUN_WX.getCode()).contains(a.getSubCode())).size() > 1) {
            throw exception(ACCOUNT_RED_ERROR_ACCOUNT);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#r.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_ORDER_ACCOUNT_OPERATION_SUCCESS, type = PMS_ORDER_RED_ACCOUNT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public void redAccount(RedAccountReqVO r) {
        LocalDate bizDate = generalConfigService.getBizDate(r.getHcode());
        List<AccountDO> accountList = validateRedAccount(r.getAccNoList(), bizDate);
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(r.getHcode());
        String payNo = IdUtil.getSnowflakeNextIdStr();
        LocalDateTime now = LocalDateTime.now();
        // 修改原账务状态为已冲红
        List<AccountDO> oldAccounts = CollUtil.newArrayList();
        // 增加新的平衡账务
        List<AccountDO> newAccountList = CollUtil.newArrayList();
        accountList.forEach(account -> {
            String accNo = IdUtil.getSnowflakeNextIdStr();
            oldAccounts.add(AccountConvert.INSTANCE.oldRedAccountConvert(r, account, shiftNo, bizDate, payNo, now, accNo));
            AccountDO newAccount = BeanUtils.toBean(account, AccountDO.class);
            newAccount.setRemark(r.getRemark());
            AccountConvert.INSTANCE.newRedAccountConvert(r, newAccount, account, shiftNo, bizDate, payNo, now, accNo);
            newAccount.setIsVerify(NumberEnum.TWO.getNumber());
            // 如果是扫码枪付款且外部订单号不为空，冲调前需要先调退款接口
            if (List.of(PayAccountEnum.SCAN_GUN_ALIPAY.getCode(), PayAccountEnum.SCAN_GUN_WX.getCode()).contains(account.getSubCode())
                    && StrUtil.isNotEmpty(account.getOutOrderNo())) {
                String refundNo = "";
                LocalDateTime successTime = LocalDateTime.now();
                if (BooleanEnum.TRUE.getValue().equals(account.getIsPreAuthAffirm())) {
                    PreFinishCancelRespDTO preFinishCancelRespDTO = scanGunFinishCancel(account);
                    refundNo = preFinishCancelRespDTO.getFinishCancelNo();
                    successTime = preFinishCancelRespDTO.getSuccessTime();
                } else {
                    RefundRespDTO refundRespDTO = scanGunRefund(account, null);
                    refundNo = refundRespDTO.getRefundNo();
                    successTime = refundRespDTO.getSuccessTime();
                }

                newAccount.setOutOrderNo(refundNo).setRefundAccNo(account.getAccNo())
                        .setCreateTime(successTime).setUpdateTime(successTime);

            }
            // AR账套处理
            if (Objects.equals(CREDIT_S_ACCOUNT.getCode(), account.getSubCode())) {
                arSetService.calculateArSet(account.getGcode(), account.getPayCode(), ArSetHandleTypeEnum.RED, account.getFee());
            }
            // 会员卡支付冲调需要更改会员卡余额
            if (Objects.equals(STORE_CARD.getCode(), account.getSubCode()) || Objects.equals(STORE_CARD_REFUND.getCode(), account.getSubCode())) {
                if (!account.getPlatForm().equals(NumberEnum.ZERO.getNumber())) {
                    RefundRespDTO refundRespDTO = scanGunRefund(account, null);
                    String refundNo = refundRespDTO.getRefundNo();
                    LocalDateTime successTime = refundRespDTO.getSuccessTime();
                    newAccount.setOutOrderNo(refundNo).setCreateTime(successTime).setUpdateTime(successTime);
                } else {
                    storeCardApi.redStoreCard(new StoreCardRedReqDTO()
                            .setHcode(r.getHcode())
                            .setStoreCardNo(account.getPayCode())
                            .setConsumeFee(account.getFee())
                            .setRechargeFee(0L)
                            .setGiveFee(0L)
                            .setGivePoint(0L)
                            .setOrderNo(account.getNo())
                            .setAccType(account.getAccType()));
                }
            }
            if (Objects.equals(ConsumeAccountEnum.GOODS.getCode(), account.getSubCode()) || Objects.equals(ConsumeAccountEnum.INDEMNITY_FEE.getCode(), account.getSubCode())) {
                redGoods(account, bizDate, shiftNo);
            }
            newAccountList.add(newAccount);
        });
        accountMapper.updateBatch(oldAccounts);
        accountMapper.insertBatch(newAccountList);
        // 商品冲调
        itemRed(accountList);
        // 记录日志
        accountLogHandle(oldAccounts, newAccountList);
    }

    private void itemRed(List<AccountDO> accountList) {
        List<String> accountNos = new ArrayList<>();
        boolean flag = true;
        String warehouseConfig = NumberEnum.ZERO.getNumber();
        for (AccountDO account : accountList) {
            if (ConsumeAccountEnum.GOODS.getCode().equals(account.getSubCode())) {
                if (flag) {
                    // 获得仓库配置
                    warehouseConfig = generalConfigService.getWarehouseConfig(account.getGcode(), account.getHcode());
                    flag = false;
                }
                if (NumberEnum.ONE.getNumber().equals(warehouseConfig)) {
                    accountNos.add(account.getAccNo());
                }
            }
        }
        if (CollUtil.isNotEmpty(accountNos)) {
            CommonResult<Boolean> res = stockOutApi.updateBrewing(new ErpStockOutUpdateReqDTO().setAccountNo(accountNos)
                    .setOrderNo(accountList.getFirst().getNo()).setHcode(accountList.getFirst().getHcode()));
            if (res.getCode() != 0) {
                throw exception(FAILURE_REASON, res.getMsg());
            }
        }
    }

    private void redGoods(AccountDO account, LocalDate bizDate, String shiftNo) {
        // 小商品/赔偿
        if (ConsumeAccountEnum.GOODS.getCode().equals(account.getSubCode()) || ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(account.getSubCode())) {
            // 获取商品销售/物品赔偿记录
            List<GoodsSellRecordDO> goodsSellRecordList = goodsSellRecordService.getGoodsSellRecordList(new GoodsSellRecordListReqVO().setGcode(account.getGcode())
                    .setHcode(account.getHcode())
                    .setAccNo(account.getAccNo())
                    .setAccType(NumberEnum.ZERO.getNumber())
                    .setOffset(NumberEnum.ZERO.getNumber()));
            goodsSellRecordList.forEach(goodsSellRecordDO -> {
                goodsSellRecordDO.setOffset(NumberEnum.ONE.getNumber())
                        .setId(null)
                        .setNum(goodsSellRecordDO.getNum() * -1)
                        .setTotalPrice(goodsSellRecordDO.getTotalPrice() * -1)
                        .setShiftNo(shiftNo)
                        .setBizDate(bizDate)
                        .setRevRecordCode(goodsSellRecordDO.getRevRecordCode());
            });
            goodsSellRecordService.createGoodsSellRecords(goodsSellRecordList);
        }
    }

    private void returnGoods(AccountDO account, LocalDate bizDate, String shiftNo, List<ConsumeRefundGoodReqVO.Goods> goodsList,
                             AccountDO newAccount, List<ErpStockOutUpdateNumberReqDTO.Item> itemList) {
        Long fee = 0L;
        String context = "";
        // 小商品/赔偿
        if (ConsumeAccountEnum.GOODS.getCode().equals(account.getSubCode()) || ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(account.getSubCode())) {
            // 获取商品销售/物品赔偿记录
            List<GoodsSellRecordDO> goodsSellRecordList = goodsSellRecordService.getGoodsSellRecordList(new GoodsSellRecordListReqVO().setGcode(account.getGcode())
                    .setHcode(account.getHcode())
                    .setAccNo(account.getAccNo())
                    .setAccType(NumberEnum.ZERO.getNumber())
                    .setOffset(NumberEnum.ZERO.getNumber()));

            List<GoodsSellRecordDO> sellRecordDOs = CollectionUtils.filterList(goodsSellRecordList, goodsSellRecordDO -> goodsSellRecordDO.getTotalPrice() > 0);
            Map<String, List<GoodsSellRecordDO>> goodsSellRecordDOMap = CollectionUtils.convertMultiMap(goodsSellRecordList, GoodsSellRecordDO::getGoodsCode);


            List<GoodsSellRecordDO> list = new ArrayList<>();
            for (ConsumeRefundGoodReqVO.Goods goods : goodsList) {
                List<GoodsSellRecordDO> goodsSellRecordDList = goodsSellRecordDOMap.getOrDefault(goods.getGoodsCode(), new ArrayList<>());
                long sum = goodsSellRecordDList.stream().mapToLong(GoodsSellRecordDO::getNum).sum();
                if (goods.getNum() > sum) {
                    throw exception(RETURN_ACCOUNT_NUM_ERROR);
                }
                GoodsSellRecordDO goodsSellRecord = BeanUtils.toBean(goodsSellRecordDList.getFirst(), GoodsSellRecordDO.class);
                context += goodsSellRecord.getGoodsName() + ":" + MoneyUtils.fenToYuanStr(goodsSellRecord.getPrice().intValue())
                        + "*" + -goods.getNum() + ",";

                goodsSellRecord.setId(null)
                        .setNum(-goods.getNum())
                        .setTotalPrice(-(goods.getNum() * goodsSellRecord.getPrice()))
                        .setShiftNo(shiftNo)
                        .setBizDate(bizDate);

                list.add(goodsSellRecord);
                // 记录库存
                itemList.add(new ErpStockOutUpdateNumberReqDTO.Item()
                        .setProductId(Long.parseLong(goods.getGoodsCode())).setCount(goods.getNum()));
            }

            goodsSellRecordService.createGoodsSellRecords(list);
            fee = list.stream().mapToLong(GoodsSellRecordDO::getTotalPrice)
                    .sum();
        }

        context = StringUtils.removeEnd(context, ",");
        newAccount.setFee(fee);
        newAccount.setAfterTaxFee(calculateAfterTaxFee(account.getGcode(), account.getHcode(), account.getSubCode(), fee));
        newAccount.setAccDetail(context);
    }

    private PreFinishCancelRespDTO scanGunFinishCancel(AccountDO account) {
        validateScanGunRefund(account);
        CommonResult<PreFinishCancelRespDTO> preFinishCancel = payBaseApi.preFinishCancel(new PreFinishCancelReqDTO().setFinishOrderNo(account.getPreAuthFinishOrderNo())
                .setPlatform(PlatFormEnum.FUIOU.getCode()).setHcode(account.getHcode()).setRefundPrice(account.getFee()));
        // 退款成功返回退款单号
        if (preFinishCancel.getCode() == 0) {
            return preFinishCancel.getData();
        } else {
            throw exception(PAY_FAILURE, preFinishCancel.getMsg());
        }
    }

    private void accountLogHandle(List<AccountDO> oldAccounts, List<AccountDO> newAccountList) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(PMS_ORDER_ACCOUNT_OPERATION_SUCCESS_TEMPLATE), "template");
        StringWriter writer = new StringWriter();
        Map<String, Object> context = new HashMap<>();
        context.put("oldAccounts", oldAccounts);
        context.put("newAccountList", newAccountList);
        String content = mustache.execute(writer, context).toString();
        LogRecordContext.putVariable("order", newAccountList.getFirst());
        LogRecordContext.putVariable("content", content);
    }

    /**
     * 验证冲账
     *
     * @param accNoList 账务号列表
     * @param bizDate   营业日期
     * @return 账务列表
     */
    private List<AccountDO> validateRedAccount(List<String> accNoList, LocalDate bizDate) {
        List<AccountDO> accountList = accountMapper.selectList(AccountDO::getAccNo, accNoList);
        if (CollUtil.isEmpty(accountList) || accountList.size() != accNoList.size()) {
            throw exception(ACCOUNT_RED_ERROR_ACCOUNT);
        }
        // 如果账务的营业日和当前营业日不同，不能冲红
        accountList.forEach(account -> {
            // 如果账务已经核销，则不能冲账
            if (NumberEnum.ONE.getNumber().equals(account.getIsVerify())) {
                throw exception(ACCOUNT_RED_ERROR_VERIFY);
            }
            // 如果账务状态不是未结且不是现付账账务则不能冲调
            if (!Objects.equals(account.getAccType(), AccountTypeEnum.CASH.getCode())) {
                if (!AccountStatusEnum.UNCLOSED.getCode().equals(account.getState())) {
                    throw exception(ACCOUNT_RED_ERROR_ACCOUNT2);
                }
            }
            // 如果非预订单账务营业日和当前营业日不同，不能冲账
            if (!account.getBizDate().isEqual(bizDate) && !AccountTypeEnum.BOOK.getCode().equals(account.getAccType())) {
                throw exception(ACCOUNT_RED_ERROR_BIZ_DATE);
            }
            if (ConsumeAccountEnum.BK_FEE.getCode().equals(account.getSubCode())) {
                throw exception(ACCOUNT_RED_ERROR_BK_FEE);
            }
            if (account.getIsRefunded().equals(NumberEnum.ONE.getNumber())) {
                throw exception(ACCOUNT_REFUND_NOT_RED);
            }
            // 如果是退货商品，则不能冲账
            if (AccountTagEnum.REFUND_GOODS.getTitle().equals(account.getTags())) {
                throw exception(RETURN_ACCOUNT_NOT_RED);
            }
        });
        return accountList;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode", "#reqVO.no"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_ORDER_PART_CLOSE_ACCOUNT_SUCCESS, type = PMS_ORDER_PART_CLOSE_ACCOUNT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#reqVO.no}}",
            extra = PMS_ORDER_property)
    public void partCloseAccount(PartCloseAccountReqVO reqVO) {
        // 验证并返回未结账务列表
        List<AccountDO> accountList = validatePartCloseAccount(reqVO);
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        String payNo = IdUtil.getSnowflakeNextIdStr();
        FinishCloseAccountReqVO confirm = new FinishCloseAccountReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setAccNoList(reqVO.getAccNoList());
        StatCheckOutAccountRespVO statAccount = statCloseAccount(confirm);
        // 2.1 获取付款科目列表
        Map<String, GeneralConfigDO> paySubMap = getPayAccountMap(reqVO.getGcode());
        String payMode = paySubMap.get(reqVO.getSubCode()).getValue();
        List<String> accNoList = CollUtil.newArrayList();
        // 2.2 如果选中的subCode(收款、退款)与payMode不一样，或者付款金额与退房统计的金额不一致，那么就直接入账流程
        if (!Objects.equals(statAccount.getPayMode(), payMode) || !Objects.equals(statAccount.getFee(), reqVO.getFee())) {
            // 付款
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT3);
        }

        // 1. 如果付款金额等于账务明细总额，则修改账务明细状态为已结
        List<AccountDO> updateAccountList = CollUtil.newArrayList();
        AccountDO account = BeanUtils.toBean(reqVO, AccountDO.class);
        buildAccount(reqVO, payMode, accountList, bizDate, shiftNo, payNo, updateAccountList, account);

        // 获取开通的支付方式
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(reqVO.getGcode(), reqVO.getHcode(), ServiceTypeEnum.PAYMENT.getCode());
        ServiceIntegrationPaymentRespVO serviceIntegrationPaymentRespVO = BeanUtils.toBean(serviceIntegration, ServiceIntegrationPaymentRespVO.class);
        // 2. 根据付款方式做相应的操作
        switch (PayAccountEnum.getPayAccountEnumByCode(reqVO.getSubCode())) {
            // AR账 验证账套的期限和额度
            case CREDIT_S_ACCOUNT:
                ProtocolAgentDO protocolAgent = validateArSet(reqVO, reqVO.getFee(), bizDate);
                String paType = protocolAgent.getPaType();
                account.setCreditTargetType(NumberEnum.ZERO.getNumber().equals(paType) ? GuestSrcTypeEnum.PROTOCOL.getCode() : GuestSrcTypeEnum.AGENT.getCode());
                break;
            // 扫码付-扫码枪收款 调用线上支付接口
            case SCAN_GUN:
                if (ObjectUtil.isEmpty(serviceIntegrationPaymentRespVO)) {
                    throw exception(SCAN_NOT_OPEN);
                }
                boolean hasScanGunCode = serviceIntegrationPaymentRespVO.getScenario().getParameters().stream()
                        .anyMatch(scenario -> SCAN_GUN.getCode().equals(scenario.getCode()));
                if (!hasScanGunCode) {
                    throw exception(SCAN_NOT_OPEN);
                }
                // 扫码抢收款,处理扫码枪收款业务
                ScanRespDTO payScanRespDTO = scanGunPay(reqVO.getHcode(), reqVO.getPayCode(), reqVO.getFee(), reqVO.getAccDetail(), account, serviceIntegrationPaymentRespVO.getSolutionProvider());
                account.setOutOrderNo(payScanRespDTO.getOrderNo());
                account.setAccDetail("账户端交易单号" + payScanRespDTO.getAccOrderNo());
                break;
            // 扫码付-预授权 调用线上预授权接口
            case SCAN_GUN_PRE_AUTH:
                String orderNox = prePayScan(reqVO.getHcode(), reqVO.getPayCode(), reqVO.getFee(), reqVO.getAccDetail(), serviceIntegrationPaymentRespVO.getSolutionProvider());
                account.setOutOrderNo(orderNox);
                break;
            // 储值卡付款需要修改储值卡余额
            case STORE_CARD: // 储值卡付款
                if (ObjectUtil.isNotEmpty(serviceIntegrationPaymentRespVO) && serviceIntegrationPaymentRespVO.getScenario().getParameters().stream()
                        .anyMatch(scenario -> STORE_CARD.getCode().equals(scenario.getCode()))) {
                    VipRespDTO payVipRespDTO = vipCardPay(reqVO.getHcode(), reqVO.getStoreCardNo(), reqVO.getFee(), account, serviceIntegrationPaymentRespVO.getSolutionProvider());
                    account.setOutOrderNo(payVipRespDTO.getOrderNo());
                    account.setAccDetail("账户端交易单号" + payVipRespDTO.getAccOrderNo());
                } else {
                    CommonResult<Boolean> consumeResult = memberApi.consume(buildConsumeSaveReqDTO(reqVO));
                    if (!consumeResult.isSuccess()) {
                        throw exception(STORECARD_PAY_ERROR, consumeResult.getMsg());
                    }
                }
            default:
                break;
        }
        accountMapper.updateBatch(updateAccountList);
        accountMapper.insert(account);
        // 记录日志
        partCloseAccountLogHandle(accountList, reqVO);
    }

    private void partCloseAccountLogHandle(List<AccountDO> accountList, PartCloseAccountReqVO reqVO) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(PMS_ORDER_PART_CLOSE_ACCOUNT_SUCCESS_TEMPLATE), "template");
        StringWriter writer = new StringWriter();
        Map<String, Object> context = new HashMap<>();
        context.put("accounts", accountList);
        String content = mustache.execute(writer, context).toString();
        LogRecordContext.putVariable("order", reqVO);
        LogRecordContext.putVariable("content", content);
    }

    private ConsumeSaveReqDTO buildConsumeSaveReqDTO(PartCloseAccountReqVO reqVO) {
        return new ConsumeSaveReqDTO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setMcode(reqVO.getMcode())
                .setPhone(reqVO.getPhone())
                .setOrderType(CouponOrderTypeEnum.ROOM.getCode())
                .setFee(reqVO.getFee())
                .setScene(ConsumeSceneEnum.ROOM.getCode())
                .setPayMethod(reqVO.getSubCode())
                .setStoreCardNo(reqVO.getStoreCardNo())
                .setPwd(reqVO.getPwd())
                .setConsumeType(Objects.equals(reqVO.getAccType(), AccountTypeEnum.CASH.getCode()) ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
    }


    @Override
    public PartCloseAccountRespVO getPartCloseAccount(AccountSelectPartCloseReqVO reqVO) {
        PartCloseAccountRespVO resp = new PartCloseAccountRespVO();
        List<AccountDO> accountList = accountMapper.selectList2(new AccountReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setAccNoList(reqVO.getAccNoList()));
        if (CollUtil.isEmpty(accountList)) {
            return resp;
        }
        long consumeFee = 0L;
        long payFee = 0L;
        long balance = 0L;
        Set<PartCloseAccountRespVO.RecordAccountNo> recordAccountNos = CollUtil.newHashSet();
        List<PartCloseAccountRespVO.Account> accounts = CollUtil.newArrayList();
        Set<String> togetherCodes = CollUtil.newHashSet();
        // 获得科目字典
        List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getDictDataListByDicTypes(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())).getData();
        Map<String, String> dictMap = CollectionUtils.convertMap(dictDataRespDTOS, DictDataRespDTO::getCode, DictDataRespDTO::getLabel);
        for (AccountDO account : accountList) {
            togetherCodes.add(account.getTogetherCode());
            if (Objects.equals(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), account.getSubType())) {
                consumeFee += account.getFee();
            } else {
                payFee += account.getFee();
            }
            PartCloseAccountRespVO.Account x = new PartCloseAccountRespVO.Account().setSubName(dictMap.getOrDefault(account.getSubCode(), ""))
                    .setAccNo(account.getAccNo())
                    .setSubType(account.getSubType())
                    .setFee(account.getFee())
                    .setFee(account.getFee())
                    .setRNo(account.getRNo())
                    .setName(account.getGuestName());
            accounts.add(x);
            recordAccountNos.add(new PartCloseAccountRespVO.RecordAccountNo().setNo(account.getNo())
                    .setRNo(account.getRNo())
                    .setName(account.getGuestName())
                    .setTogetherCode(account.getTogetherCode())
                    .setAccType(account.getAccType()));
        }
        resp.setAccounts(accounts)
                .setRecordAccountNos(recordAccountNos.stream().toList())
                .setTogetherCodes(togetherCodes.stream().toList());
        // 计算余额：余额=付款金额-消费金额
        balance = payFee - consumeFee;
        resp.setBalance(balance);
        if (balance <= 0) {
            resp.setSubCode(PayAccountEnum.RMB_RECEIPT.getCode());
        } else {
            resp.setSubCode(PayAccountEnum.CASH_REFUND.getCode());
        }
        return resp;
    }

    private ProtocolAgentDO validateArSet(PartCloseAccountReqVO r, Long needPayFee, LocalDate bizDate) {
        // 查询账套信息
        ArSetDO arSet = arSetService.getArSet(r.getPayCode(), r.getGcode());
        if (ObjectUtil.isNull(arSet)) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT4);
        }
        if (Objects.equals(arSet.getIsEnable(), BooleanEnum.FALSE.getValue())) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT5);
        }
        // 验证当前日期是否在有效日期之间
        if (Objects.equals(NumberEnum.ONE.getNumber(), arSet.getCreditValidType())) {
            if (!LocalDateTimeUtil.isIn(bizDate.atStartOfDay(), arSet.getCreditStartDate().atStartOfDay(),
                    arSet.getCreditEndDate().plusDays(1).atStartOfDay())) {
                throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT6);
            }
        }
        if (arSet.getBalance() < needPayFee) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT7);
        }
        ProtocolAgentDO protocolAgent = protocolAgentService.getProtocolAgent(arSet.getUnitCode());
        if (Objects.equals(BooleanEnum.FALSE.getValue(), protocolAgent.getIsEnable())) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT8);
        }
        if (Objects.equals(BooleanEnum.FALSE.getValue(), protocolAgent.getIsCredit())) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT9);
        }
        return protocolAgent;
    }

    /**
     * 部分结账，封装账务
     *
     * @param reqVO             入参
     * @param accountList       账务列表
     * @param bizDate           营业日
     * @param shiftNo           班次
     * @param payNo             结账号
     * @param updateAccountList 需要修改的账务列表
     * @param account           账务
     */
    private void buildAccount(PartCloseAccountReqVO reqVO, String payMode, List<AccountDO> accountList, LocalDate bizDate, String shiftNo, String payNo, List<AccountDO> updateAccountList, AccountDO account) {
        // 3. 修改账务明细状态为已结
        LocalDateTime now = LocalDateTime.now();
        accountList.forEach(acc -> {
            updateAccountList.add(AccountDO.builder()
                    .id(acc.getId())
                    .state(AccountStatusEnum.CLOSED.getCode())
                    .payNo(payNo)
                    .payShiftNo(shiftNo)
                    .payBizDate(bizDate)
                    .payTime(now)
                    .payer(SecurityFrameworkUtils.getLoginUserName())
                    .handleShiftNo(shiftNo)
                    .build());
        });
        String guestSrcType = null;
        String rCode = null;
        String rNo = null;
        String guestName = null;
        // 4. 添加一笔账务
        switch (AccountTypeEnum.getAccountTypeEnumByCode(reqVO.getAccType())) {
            case GENERAL:
                OrderTogetherDO orderTogether = orderTogetherService.getOrderTogether(reqVO.getTogetherCode());
                guestName = orderTogether.getName();
                OrderDO order = orderService.getOrderByOrderNo(orderTogether.getOrderNo());
                guestSrcType = order.getGuestSrcType();
                rCode = orderTogether.getRCode();
                rNo = orderTogether.getRNo();
                break;
            case GROUP:
                TeamDO team = teamService.getTeam(reqVO.getTogetherCode());
                guestName = team.getTeamName();
                guestSrcType = team.getGuestSrcType();
                break;
            case BOOK:
                BookDO book = bookService.getBookByBookNo(reqVO.getNo());
                guestName = book.getGuestName();
                guestSrcType = book.getGuestSrcType();
                break;
            default:
                throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT);
        }

        if (NumberEnum.MINUS.getNumber().equals(payMode)) {
            reqVO.setFee(reqVO.getFee() * -1);
        }
        account.setAccNo(IdUtil.getSnowflakeNextIdStr())
                .setRCode(rCode).setRNo(rNo)
                .setFee(reqVO.getFee())
                .setGuestName(guestName)
                .setGuestSrcType(guestSrcType)
                .setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())
                .setAfterTaxFee(reqVO.getFee())
                .setShiftNo(shiftNo)
                .setRecorder(SecurityFrameworkUtils.getLoginUserName())
                .setBizDate(bizDate)
                .setState(AccountStatusEnum.CLOSED.getCode())
                .setIsCanRev(BooleanEnum.FALSE.getValue())
                .setPayNo(payNo)
                .setPayShiftNo(shiftNo)
                .setPayBizDate(bizDate)
                .setPayTime(now)
                .setPayer(SecurityFrameworkUtils.getLoginUserName())
                .setCurrencyUnit(accountList.getFirst().getCurrencyUnit());
    }

    /**
     * 部分结账验证
     *
     * @param reqVO 入参
     * @return 未结账务列表
     */
    private List<AccountDO> validatePartCloseAccount(PartCloseAccountReqVO reqVO) {
        // 验证
        List<AccountDO> accountList = accountMapper.selectList2(new AccountReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setAccNoList(reqVO.getAccNoList())
                .setState(AccountStatusEnum.UNCLOSED.getCode()));
        if (CollUtil.isEmpty(accountList)) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT);
        }
        Set<String> accNoList = CollUtil.newHashSet();
        Set<String> togetherCodes = CollUtil.newHashSet();
        accountList.forEach(acc -> {
            accNoList.add(acc.getAccNo());
            togetherCodes.add(acc.getTogetherCode());
        });
        if (!accNoList.containsAll(reqVO.getAccNoList())) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT2);
        }

        AccountStatByTogetherCodesRespVO statAccount = accountMapper.statTogetherAccountByTogetherCodes(new AccountStatByTogetherCodesReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setTogetherCodes(togetherCodes.stream().toList()));
        // 如果是退款，要判断退款金额不能大于余额
        long balance = statAccount.getPaymentTotal() - statAccount.getConsumeTotal();
        Map<String, GeneralConfigDO> payMap = getPayAccountMap(reqVO.getGcode());
        GeneralConfigDO payAccount = payMap.get(reqVO.getSubCode());
        if (Objects.equals(NumberEnum.MINUS.getNumber(), payAccount.getValue()) && balance < reqVO.getFee()) {
            throw exception(ACCOUNT_PART_CLOSE_ERROR_ACCOUNT10, balance / 100);
        }
        return accountList;
    }

    @Override
    public void updateRemark(AccountRemarkSaveReqVO reqVO) {
        AccountDO account = accountMapper.selectOne(AccountDO::getAccNo, reqVO.getAccNo());
        if (Objects.isNull(account)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        if (!Objects.equals(account.getState(), AccountStatusEnum.UNCLOSED.getCode())) {
            throw exception(ACCOUNT_STATUS_NOT_UNCLOSED2);
        }
        accountMapper.updateById(AccountDO.builder().id(account.getId()).remark(reqVO.getRemark()).build());
    }

    private AccountDO validateAccount(String accNo) {
        AccountDO account = accountMapper.selectOne(AccountDO::getAccNo, accNo);
        if (Objects.isNull(account)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        return account;
    }

    @Override
    public OrderCheckOutGuestRespVO getCheckOutGuestList(String no, String noType) {
        return getCheckOutGuestList(no, noType, null);
    }


    @Override
    public OrderCheckOutGuestRespVO getCheckOutGuestList(String no, String noType, String all) {
        OrderCheckOutGuestRespVO resp = new OrderCheckOutGuestRespVO();
        resp.setOrderTogethers(new ArrayList<>());
        BookDO book = new BookDO();
        // 如果是普通预订单或团队预订单，则获取预订单信息
        if (NoTypeEnum.BOOK.getCode().equals(noType) || NoTypeEnum.TEAM.getCode().equals(noType)) {
            book = bookService.getBookByBookNo(no);
            if (book == null) {
                throw exception(BOOK_NOT_EXISTS);
            }
            return bulidOrderCheckOutGuestRespVO(resp, book);
        }
        // 如果是团队主单，那么就获取团队主单信息
        if (NoTypeEnum.TEAM_MAIN.getCode().equals(noType)) {
            TeamDO team = teamService.getTeam(no);
            if (team == null) {
                throw exception(TEAM_NOT_EXISTS);
            }
            return buildTeamOrderCheckOutGuestRespVO(resp, team);
        }
        // 如果是团队接待，获取团队下的所有客单, 将团队主单也放进去
        if (NoTypeEnum.TEAM_RECEPTION.getCode().equals(noType)) {
            TeamDO team = teamService.getTeam(no);
            if (team == null) {
                throw exception(TEAM_NOT_EXISTS);
            }
            return buildTeamReceptionCheckOutGuestRespVO(resp, team);
        }
        // 默认走客单
        OrderTogetherDO orderTogether = orderTogetherService.getOrderTogether(no);

        if (Objects.isNull(orderTogether)) {
            throw exception(ORDER_NOT_EXISTS);
        }
        OrderDO order = orderService.getOrderByOrderNo(orderTogether.getOrderNo());
        resp.setBindCode(order.getBindCode());
        resp.setOrderType(order.getOrderType());
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(order.getGcode())
                .setHcode(order.getHcode())
                .setBindCode(order.getBindCode()));
        List<String> orderNos = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
        List<OrderTogetherDO> orderTogethers = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(order.getGcode())
                .setHcode(order.getHcode())
                .setStates(List.of(OrderStateEnum.CHECK_IN.getCode(), OrderStateEnum.CREDIT.getCode()))
                .setOrderNos(orderNos));
        List<OrderCheckOutGuestRespVO.OrderTogether> ots = orderTogethers.stream().map(orderTogetherDO -> {
            OrderCheckOutGuestRespVO.OrderTogether ot = new OrderCheckOutGuestRespVO.OrderTogether().setName(orderTogetherDO.getName())
                    .setRNo(orderTogetherDO.getRNo())
                    .setRCode(orderTogetherDO.getRCode())
                    .setTogetherCode(orderTogetherDO.getTogetherCode())
                    .setIsMain(orderTogetherDO.getIsMain())
                    .setIsTeam(BooleanEnum.FALSE.getValue())
                    .setNo(orderTogetherDO.getOrderNo())
                    .setAccType(AccountTypeEnum.GENERAL.getCode())
                    .setState(orderTogetherDO.getState())
                    .setCheckinType(order.getCheckinType())
                    .setChannelCode(order.getChannelCode())
                    .setRtCode(order.getRtCode())
                    .setGuestSrcType(orderTogetherDO.getGuestSrcType())
                    .setOrderNo(orderTogetherDO.getOrderNo())
                    .setGuestCode(order.getGuestCode())
                    .setPhone(orderTogetherDO.getPhone());
            if (Objects.equals(orderTogetherDO.getRCode(), orderTogether.getRCode())) {
                ot.setChecked(BooleanEnum.TRUE.getValue());
            } else {
                ot.setChecked(BooleanEnum.FALSE.getValue());
            }
            return ot;
        }).toList();
        // 如果不是全部，则过滤出主单
        if (StrUtil.isBlank(all) || BooleanEnum.FALSE.getValue().equals(all)) {
            //  如果是主单才能入帐，则过滤出主单
            if (hotelParamConfigService.isMainOrderBilling(order.getGcode(), order.getHcode())) {
                ots = ots.stream()
                        .filter(ot -> ot != null && BooleanEnum.TRUE.getValue().equals(ot.getIsMain()))
                        .toList();
            }
        }
        // 如果是团队，则需要添加团队主账
        if (Objects.equals(order.getOrderType(), OrderTypeEnum.GROUP.getCode())) {

            TeamDO team = teamService.getTeam(order.getTeamCode());
            if (Objects.equals(team.getAccState(), AccountStatusEnum.UNCLOSED.getCode())) {
                // 将不可变集合转换为可变集合
                List<OrderCheckOutGuestRespVO.OrderTogether> mutableOts = new LinkedList<>(ots);
                // 添加团队主账到列表头部
                mutableOts.addFirst(new OrderCheckOutGuestRespVO.OrderTogether()
                        .setNo(order.getTeamCode()) // 将团队代码赋值给订单号，在账务表中，团队代码作为团队主账的单号
                        .setRNo(NoTypeEnum.TEAM_MAIN.getName())
                        .setName(team.getTeamName())
                        .setTogetherCode(order.getTeamCode())
                        .setIsMain(BooleanEnum.FALSE.getValue())
                        .setState(team.getState())
                        .setAccType(AccountTypeEnum.GROUP.getCode())
                        .setCheckinType(order.getCheckinType())
                        .setChannelCode(order.getChannelCode())
                        .setRtCode(order.getRtCode())
                        .setGuestSrcType(team.getGuestSrcType())
                        .setIsTeam(BooleanEnum.TRUE.getValue()));
                // 使用可变集合替换原始不可变集合
                ots = mutableOts;
            }
        }
        resp.setOrderTogethers(ots);
        return resp;
    }

    private OrderCheckOutGuestRespVO bulidOrderCheckOutGuestRespVO(OrderCheckOutGuestRespVO resp, BookDO book) {
        resp.setBindCode(book.getBookNo());
        resp.setOrderType(book.getBookType());

        resp.setOrderTogethers(List.of(new OrderCheckOutGuestRespVO.OrderTogether()
                .setNo(book.getBookNo())
                .setName(book.getContact())
                .setIsTeam(BooleanEnum.FALSE.getValue())
                .setTogetherCode(book.getBookNo())
                .setAccType(AccountTypeEnum.BOOK.getCode())
                .setState(book.getState())));
        return resp;
    }

    private OrderCheckOutGuestRespVO buildTeamReceptionCheckOutGuestRespVO(OrderCheckOutGuestRespVO resp, TeamDO team) {
        resp.setBindCode(team.getTeamCode());
        resp.setOrderType(OrderTypeEnum.GROUP.getCode());
        List<OrderDO> orderDOS = orderService.getOrderList(new OrderReqVO().setGcode(team.getGcode())
                .setHcode(team.getHcode())
                .setTeamCode(team.getTeamCode()));
        List<String> orderNos = CollectionUtils.convertList(orderDOS, OrderDO::getOrderNo);
        List<OrderTogetherDO> orderTogethers = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(team.getGcode())
                .setHcode(team.getHcode())
                .setStates(List.of(OrderStateEnum.CHECK_IN.getCode(), OrderStateEnum.CREDIT.getCode()))
                .setOrderNos(orderNos));
        // 确保 orderTogethers 不为 null
        if (orderTogethers == null) {
            orderTogethers = Collections.emptyList();
        }
        List<OrderCheckOutGuestRespVO.OrderTogether> ots = orderTogethers.stream().map(orderTogetherDO -> {
            OrderCheckOutGuestRespVO.OrderTogether ot = new OrderCheckOutGuestRespVO.OrderTogether().setName(orderTogetherDO.getName())
                    .setRNo(orderTogetherDO.getRNo())
                    .setRCode(orderTogetherDO.getRCode())
                    .setTogetherCode(orderTogetherDO.getTogetherCode())
                    .setIsMain(orderTogetherDO.getIsMain())
                    .setIsTeam(BooleanEnum.FALSE.getValue())
                    .setNo(orderTogetherDO.getOrderNo())
                    .setChecked(BooleanEnum.TRUE.getValue())
                    .setAccType(AccountTypeEnum.GENERAL.getCode())
                    .setState(orderTogetherDO.getState());
            return ot;
        }).collect(Collectors.toList()); // 使用 Collectors.toList() 创建可变列表
        // 如果团队在住状态，需要将其主单放第一个
        // 将团队主单放第一个
        if (Objects.equals(team.getAccState(), AccountStatusEnum.UNCLOSED.getCode())) {
            OrderCheckOutGuestRespVO.OrderTogether teamMain = new OrderCheckOutGuestRespVO.OrderTogether()
                    .setRNo(NoTypeEnum.TEAM_MAIN.getName())
                    .setNo(team.getTeamCode())
                    .setName(team.getTeamName())
                    .setIsMain(BooleanEnum.FALSE.getValue())
                    .setIsTeam(BooleanEnum.TRUE.getValue())
                    .setTogetherCode(team.getTeamCode())
                    .setChecked(BooleanEnum.TRUE.getValue())
                    .setAccType(AccountTypeEnum.GROUP.getCode())
                    .setState(team.getState());
            ots.addFirst(teamMain);
        }

        resp.setOrderTogethers(ots);
        return resp;
    }

    private OrderCheckOutGuestRespVO buildTeamOrderCheckOutGuestRespVO(OrderCheckOutGuestRespVO resp, TeamDO team) {
        resp.setBindCode(team.getTeamCode());
        resp.setOrderType(OrderTypeEnum.GROUP.getCode());
        if (Objects.equals(team.getAccState(), AccountStatusEnum.UNCLOSED.getCode())) {
            resp.setOrderTogethers(List.of(new OrderCheckOutGuestRespVO.OrderTogether()
                    .setNo(team.getTeamCode())
                    .setRNo(NoTypeEnum.TEAM_MAIN.getName())
                    .setName(team.getTeamName())
                    .setIsTeam(BooleanEnum.TRUE.getValue())
                    .setTogetherCode(team.getTeamCode())
                    .setAccType(AccountTypeEnum.GROUP.getCode())
                    .setState(team.getState())));
        }
        return resp;
    }

    @Override
    public List<ConfirmCheckOutPreAuthRespVO> getConfirmPreAuthList(ConfirmPreAuthListReqVO reqVO) {
        List<ConfirmCheckOutPreAuthRespVO> confirmList = CollUtil.newArrayList();
        if (CollUtil.isEmpty(reqVO.getTogetherCodeList())) {
            return confirmList;
        }
        // 查询结账预授权账务
        List<AccountDO> accountList = accountMapper.selectPreAuthAccountList(reqVO);
        if (CollUtil.isEmpty(accountList)) {
            return confirmList;
        }
        // 过滤出预授权账务
        List<AccountDO> preAuthList = CollectionUtils.filterList(accountList, account -> account.getSubCode().equals(PayAccountEnum.BANK_PRE_AUTH.getCode())
                || account.getSubCode().equals(PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()));
        // 预授权确认收款账务
        List<AccountDO> preAuthConfirmList = CollectionUtils.filterList(accountList, account -> NumberEnum.ONE.getNumber().equals(account.getIsPreAuthAffirm()));
        Map<String, AccountDO> preAuthConfirmMap = CollectionUtils.convertMap(preAuthConfirmList, AccountDO::getPreAuthAccNo);
        preAuthList.forEach(pre -> {
            ConfirmCheckOutPreAuthRespVO confirmCheckOutPreAuthRespVO = new ConfirmCheckOutPreAuthRespVO().setTogetherCode(pre.getTogetherCode())
                    .setAccNo(pre.getAccNo())
                    .setState(pre.getState())
                    .setSubCode(pre.getSubCode())
                    .setPreAuth(pre.getFee())
                    .setFee(0L)
                    .setSubName(PayAccountEnum.getLabelByCode(pre.getSubCode()));
            AccountDO confirm = preAuthConfirmMap.get(pre.getAccNo());
            if (Objects.nonNull(confirm)) {
                confirmCheckOutPreAuthRespVO.setFee(confirm.getFee());
            }
            confirmList.add(confirmCheckOutPreAuthRespVO);
        });
        return confirmList;
    }

// TODO: 补丁
//    public void confirmRoomFee(ConfirmRoomFeeReqVO reqVO) {
//        confirmRoomFee2(reqVO);
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_CONFIRM_ROOM_FEE_SUCCESS, type = PMS_ORDER_CONFIRM_ROOM_FEE_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#no}}",
            extra = PMS_ORDER_property, condition = "#accountList != null && !#accountList.isEmpty()")
    public void confirmRoomFee(ConfirmRoomFeeReqVO reqVO) {
        // 验证
        List<OrderTogetherDO> orderTogetherList = validateConfirmRoomFee(reqVO);
        List<String> orderNoList = CollectionUtils.convertList(orderTogetherList, OrderTogetherDO::getOrderNo);
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNoList));
        Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
        List<TaxConfigDO> taxConfigs = taxConfigService.getTaxConfigs(new TaxConfigReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
        Map<String, TaxConfigDO> taxConfigMap = CollectionUtils.convertMap(taxConfigs, TaxConfigDO::getSubCode);
        // 挂账订单、团队主账不再加收
        List<AccountDO> accountList = CollUtil.newArrayList();
        Map<String, OrderTogetherDO> orderTogetherMap = CollectionUtils.convertMap(orderTogetherList, OrderTogetherDO::getTogetherCode);
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        // 获取间夜数配置
        GroupParamConfigDO groupParamConfigDO = groupParamConfigService.getGroupParamConfig(reqVO.getGcode(), ParamConfigTypeEnum.PARAM_TYPE_NIGHT_NUM.getParamType());
        NightNum nightNumConfig = BeanUtils.toBean(groupParamConfigDO.getValue(), NightNum.class);
        //获得门店信息
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(reqVO.getHcode()).getData();
        reqVO.getOrderTogethers().forEach(t -> {
            OrderTogetherDO o = orderTogetherMap.get(t.getTogetherCode());
            if (t.getFee() > 0 && o != null) {
                AccountDO account = AccountDO.builder()
                        .gcode(reqVO.getGcode())
                        .hcode(reqVO.getHcode())
                        .no(o.getOrderNo())
                        .accNo(IdUtil.getSnowflakeNextIdStr())
                        .accType(AccountTypeEnum.GENERAL.getCode())
                        .togetherCode(o.getTogetherCode())
                        .rCode(o.getRCode())
                        .rNo(o.getRNo())
                        .guestName(o.getName())
                        .guestSrcType(orderMap.getOrDefault(o.getOrderNo(), new OrderDO()).getGuestSrcType())
                        .subCode(reqVO.getSubCode())
                        .subType(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())
                        .fee(t.getFee())
                        .shiftNo(shiftNo)
                        .recorder(SecurityFrameworkUtils.getLoginUserName())
                        .bizDate(bizDate)
                        .state(AccountStatusEnum.UNCLOSED.getCode())
                        .currencyUnit(merchantRespDTO.getCurrencyUnit())
                        .build();
                if (CheckInTypeEnum.HOUR_ROOM.getCode().equals(o.getCheckinType())) {
                    account.setSubCode(ConsumeAccountEnum.HOUR_ROOM.getCode());
                }
                // 计算税后金额
                if (Objects.nonNull(taxConfigMap.get(reqVO.getSubCode()))) {
                    account.setAfterTaxFee(calculateAfterTaxFee(taxConfigMap.get(reqVO.getSubCode()), t.getFee()));
                } else {
                    account.setAfterTaxFee(t.getFee());
                }
                // 设置间夜数
                account.setNightNum(calculateNightNum(nightNumConfig, reqVO.getSubCode(), o));
                accountList.add(account);
            }
        });
        accountMapper.insertBatch(accountList);
        // 记录日志
        //if (CollUtil.isNotEmpty(accountList)) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(PMS_CONFIRM_ROOM_FEE_SUCCESS_TEMPLATE), "template");
        StringWriter writer = new StringWriter();
        Map<String, Object> context = new HashMap<>();
        context.put("accountList", accountList);
        context.put("subCodeName", ConsumeAccountEnum.getLabelByCode(reqVO.getSubCode()));
        String content = mustache.execute(writer, context).toString();
        LogRecordContext.putVariable("order", reqVO);
        LogRecordContext.putVariable("no", accountList.isEmpty() ? "" : accountList.getFirst().getNo());
        LogRecordContext.putVariable("content", content);
        //}
    }

    /**
     * 计算间夜数
     *
     * @param nightNumConfig  间夜数配置
     * @param subCode         科目代码
     * @param orderTogetherDO 客单
     * @return 间夜数
     */
    private BigDecimal calculateNightNum(NightNum nightNumConfig, String subCode, OrderTogetherDO orderTogetherDO) {
        BigDecimal nightNum = BigDecimal.ZERO;
        switch (ConsumeAccountEnum.getConsumeAccountEnumByCode(subCode)) {
            case ROOM_FEE: // 系统自动计费 小时房采用小时房间夜数设置 全天房采用全天房间夜数设置
                if (Objects.equals(orderTogetherDO.getCheckinType(), CheckInTypeEnum.HOUR_ROOM.getCode())) {
                    nightNum = new BigDecimal(nightNumConfig.getHour());
                } else {
                    nightNum = new BigDecimal(nightNumConfig.getAllDay());
                }
                break;
            case ADD_HALF_DAY: // 加收半天 与全天房 小时房无关
                nightNum = new BigDecimal(nightNumConfig.getAddHalfDay());
                break;
            case ADD_ALL_DAY: // 加收全天 与全天房 小时房无关
                nightNum = new BigDecimal(nightNumConfig.getAddAllDay());
                break;
            case HAND_INPUT_ROOM_FEE: // 手工加收房费 与全天房 小时房无关
                nightNum = new BigDecimal(nightNumConfig.getHand());
                break;
        }

        return nightNum;
    }

    @Override
    public StatCheckOutAccountRespVO statCheckOutAccount(FinishCheckOutAccountReqVO reqVO) {
        List<String> togetherCodes = CollectionUtils.convertList(reqVO.getOrderTogethers(), FinishCheckOutAccountReqVO.OrderTogether::getTogetherCode);
        if (CollUtil.isEmpty(togetherCodes)) {
            throw exception(ORDER_TOGETHER_NO_EXIST);
        }
        return statAccount(reqVO.getGcode(), reqVO.getHcode(), togetherCodes, null, reqVO.getSubCode(), reqVO.getDiscountFee());
    }

    private StatCheckOutAccountRespVO statAccount(String gcode, String hcode, List<String> togetherCodes, List<String> accNoList, String subCode, Long discountFee) {
        StatCheckOutAccountRespVO accountStatResp = new StatCheckOutAccountRespVO();
        //获得门店信息
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(hcode).getData();
        // 统计账务
        AccountStatByTogetherCodesRespVO accountState = accountMapper.statTogetherAccountByTogetherCodes(new AccountStatByTogetherCodesReqVO().setGcode(gcode)
                .setHcode(hcode)
                .setTogetherCodes(togetherCodes)
                .setAccNoList(accNoList));
        if (Objects.isNull(accountState)) {
            accountState = new AccountStatByTogetherCodesRespVO().setConsumeTotal(0L).setPaymentTotal(0L);
            accountStatResp.setIsCloseAccount(BooleanEnum.TRUE.getValue());
        }
        if (discountFee != null) {
            // 优惠金额
            accountState.setPaymentTotal(accountState.getPaymentTotal() + discountFee);
        }
        if (accountState.getConsumeTotal() == null) {
            accountState.setConsumeTotal(0L);
        }
        if (accountState.getPaymentTotal() == null) {
            accountState.setPaymentTotal(0L);
        }

        if (StrUtil.isNotEmpty(subCode) && CREDIT_S_ACCOUNT.getCode().equals(subCode)
                && !BooleanEnum.TRUE.getValue().equals(accountStatResp.getIsCloseAccount())) {
            accountStatResp.setSubCode(CREDIT_S_ACCOUNT.getCode()).setPayMode(NumberEnum.ONE.getNumber()).setIsCloseAccount(BooleanEnum.FALSE.getValue());
            List<AccountDO> accountDOS = accountMapper.selectList(new AccountListReqVO().setGcode(gcode).setHcode(hcode)
                    .setTogetherCodes(togetherCodes).setAccNos(accNoList).setState(AccountStatusEnum.UNCLOSED.getCode()).setSubCodes(List.of(CREDIT_S_ACCOUNT.getCode())));
            long payPrice = accountDOS.stream().mapToLong(AccountDO::getFee).sum();
            long fee = accountState.getConsumeTotal() - payPrice;
            accountStatResp.setFee(fee);
            if (fee != 0) {
                accountStatResp.setIsAR(false);
                return accountStatResp;
            }
        }

        // 获取团队主单
//        FinishCheckOutAccountReqVO.OrderTogether teamAccount = CollectionUtils.findFirst(reqVO.getOrderTogethers(), o -> BooleanEnum.TRUE.getValue().equals(o.getIsTeam()));
//        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode())
//                                                                                                                    .setHcode(reqVO.getHcode())
//                                                                                                                    .setTogetherCodes(togetherCodes));
        List<GeneralConfigDO> generalConfigList = generalConfigService.getGeneralConfigList(new GeneralConfigReqVO().setGcode(gcode).setHcode(NumberEnum.ZERO.getNumber())
                .setCurrencyUnit(merchantRespDTO.getCurrencyUnit()).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode()));
        //获取收款方式
        String receiptCode = generalConfigList.stream()
                .filter(config -> config.getCode().endsWith(CashTypeEnum.RECEIPT.getCode()) && NumberEnum.ONE.getNumber().equals(config.getValue()))
                .map(GeneralConfigDO::getCode)
                .collect(Collectors.toList()).getFirst();
        //获取退款方式
        String refundCode = generalConfigList.stream()
                .filter(config -> config.getCode().endsWith(CashTypeEnum.REFUND.getCode()) && NumberEnum.MINUS.getNumber().equals(config.getValue()))
                .map(GeneralConfigDO::getCode)
                .collect(Collectors.toList()).getFirst();

        if (accountState.getConsumeTotal().longValue() > accountState.getPaymentTotal().longValue()) {
            accountStatResp.setSubCode(receiptCode).setPayMode(NumberEnum.ONE.getNumber()).setIsCloseAccount(BooleanEnum.FALSE.getValue());
            accountStatResp.setFee(accountState.getConsumeTotal() - accountState.getPaymentTotal());
        } else if (accountState.getConsumeTotal().longValue() == accountState.getPaymentTotal().longValue()) {
            accountStatResp.setSubCode(receiptCode).setPayMode(NumberEnum.ONE.getNumber()).setFee(0L).setIsCloseAccount(BooleanEnum.TRUE.getValue());
        } else {
            accountStatResp.setSubCode(refundCode).setPayMode(NumberEnum.MINUS.getNumber()).setIsCloseAccount(BooleanEnum.FALSE.getValue());
            accountStatResp.setFee(accountState.getPaymentTotal() - accountState.getConsumeTotal());
        }
//        List<StatCheckOutAccountRespVO.OrderTogether> orderTogethers = CollUtil.newArrayList();
//        orderTogetherList.forEach(t -> {
//            StatCheckOutAccountRespVO.OrderTogether o = BeanUtils.toBean(t, StatCheckOutAccountRespVO.OrderTogether.class);
//            o.setIsTeam(BooleanEnum.FALSE.getValue());
//            orderTogethers.add(o);
//        });
//        if (Objects.nonNull(teamAccount)) {
//            TeamDO team = teamService.getTeam(teamAccount.getTogetherCode());
//            orderTogethers.add(new StatCheckOutAccountRespVO.OrderTogether().setNo(team.getTeamCode())
//                                                                            .setTogetherCode(team.getTeamCode())
//                                                                            .setRNo(AccountTypeEnum.GROUP.getName())
//                                                                            .setName(team.getTeamName())
//                                                                            .setIsTeam(BooleanEnum.TRUE.getValue())
//                                                                            .setIsMain(BooleanEnum.FALSE.getValue()));
//        }
//
//        accountStatResp.setOrderTogethers(orderTogethers);
        // 不是AR账务或者没有AR账可挂
        accountStatResp.setIsAR(true);
        return accountStatResp;
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_PAY_CHECKOUT_SUCCESS, type = PMS_ORDER_PAY_CHECKOUT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#no}}",
            extra = PMS_ORDER_property)
    public void payCheckOut(PayCheckOutAccountReqVO reqVO) {
        // 校验AR账务付款金额
        if (PayAccountEnum.CREDIT_S_ACCOUNT.getCode().equals(reqVO.getPay().getSubCode())) {
            validateARAccountFee(reqVO);
        }
        //验证收付款货币单位是否一致
        validatePayCheckOutAccount(reqVO);
        // 1. 验证并返回入账账号
        PayCheckOutAccountReqVO.OrderTogether recordAccountNo = validatePayCheckOut(reqVO);
        // 2 判断付款金额是否与统计的金额相等，如果相等走退房结账流程，否则走入账流程(入账完成后再次进行统计，再次进入结账功能，直到付款金额与统计金额相等为止，然后走退房结账流程)
        FinishCheckOutAccountReqVO confirm = new FinishCheckOutAccountReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode());

        List<FinishCheckOutAccountReqVO.OrderTogether> orderTogetherList = CollUtil.newArrayList();
        reqVO.getOrderTogethers().forEach(t -> orderTogetherList.add(new FinishCheckOutAccountReqVO.OrderTogether().setTogetherCode(t.getTogetherCode())
                .setNo(t.getNo())
                .setIsTeam(t.getIsTeam())
                .setIsMain(t.getIsMain())));
        confirm.setOrderTogethers(orderTogetherList);
        StatCheckOutAccountRespVO statAccount = statCheckOutAccount(confirm);
        // 2.1 获取付款科目列表
        Map<String, GeneralConfigDO> paySubMap = getPayAccountMap(reqVO.getGcode());
        String payMode = paySubMap.get(reqVO.getPay().getSubCode()).getValue();
        List<String> accNoList = CollUtil.newArrayList();
        long payFee = 0;
        if (CollUtil.isNotEmpty(reqVO.getPayInfoList())) {
            long sumFee = reqVO.getPayInfoList().stream()
                    .map(PayCheckOutAccountReqVO.PayInfo::getFee)
                    .mapToLong(Long::longValue)
                    .sum();

            payFee = reqVO.getPay().getFee() + sumFee;
        } else {
            payFee = reqVO.getPay().getFee();
        }
        // 2.2 如果选中的subCode(收款、退款)与payMode不一样，或者付款金额与退房统计的金额不一致，那么就直接入账流程
        if (!Objects.equals(statAccount.getPayMode(), payMode) || !Objects.equals(statAccount.getFee(), payFee)) {
            // 付款
            String accNo = pay(reqVO, payMode, AccountStatusEnum.UNCLOSED.getCode(), recordAccountNo);
            accNoList.add(accNo);
            if (CollUtil.isNotEmpty(reqVO.getPayInfoList())) {
                OrderDO order = orderService.getOrderByOrderNo(reqVO.getOrderTogethers().getFirst().getNo());
                List<String> accNos = CollectionUtils.convertList(reqVO.getPayInfoList(), PayCheckOutAccountReqVO.PayInfo::getAccNo);
                LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
                String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
                useCoupon(reqVO, order, bizDate, shiftNo, AccountStatusEnum.UNCLOSED.getCode());
            }
        } else {
            // 2.3 走退房结账流程
            log.info("[payCheckOut][开始执行退房结账流程] 订单列表={}",
                    reqVO.getOrderTogethers().stream().map(o -> o.getNo()).collect(Collectors.toList()));
            checkOutAccountOrderRoom(reqVO, recordAccountNo, payMode);
            log.info("[payCheckOut][退房结账流程完成]");
        }
        // 发送短信
        payCheckOutSend(reqVO);
    }

    private void payCheckOutSend(PayCheckOutAccountReqVO reqVO) {
        // 只允许会员发送，且允许发送短信
        if (!NumberEnum.ONE.getNumber().equals(reqVO.getIsSendSms()) || StrUtil.isBlank(reqVO.getPay().getMcode())) {
            return;
        }
        CommonResult<MemberRespDTO> dto = memberApi.getMemberByMcode(reqVO.getGcode(), reqVO.getPay().getMcode());
        // 获取会员详情失败，不发
        if (!NumberEnum.ZERO.getNumberInt().equals(dto.getCode())) return;
        // 不存在签名以及会员自定义发送等检测，不发送
        Integer sign = smsSendClientApi.verifySend(15, reqVO.getHcode(), reqVO.getIsSendSms(), reqVO.getPay().getMcode()).getData();
        if (NumberEnum.ZERO.getNumberInt().equals(sign)) return;
        List<HashMap<String, String>> send = new ArrayList<>();
        // 组装
        send.add(new HashMap<>() {{
            put(SmsConfigParamEnum.MNAME.getCode(), dto.getData().getName());
            put(SmsConfigParamEnum.RECEIVER.getCode(), dto.getData().getPhone());
        }});
        //发送，一条信息发送给一个主客
        smsSendClientApi.smsSendForMap(new SendRequestDTO().setSend(send).setSign(sign).setType(15));
    }

    private void validatePayCheckOutAccount(PayCheckOutAccountReqVO reqVO) {
        List<String> orderTogetherList = CollUtil.newArrayList();
        reqVO.getOrderTogethers().forEach(t -> orderTogetherList.add(t.getTogetherCode()));
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(new GeneralConfigReq2VO().setGcode(reqVO.getGcode()).setHcode(NumberEnum.ZERO.getNumber())
                .setCode(reqVO.getPay().getSubCode()).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode()));
        if (generalConfig != null && generalConfig.getCurrencyUnit() != null) {
            List<AccountDO> accountDOS = accountMapper.selectList(new AccountListReqVO().setGcode(reqVO.getGcode())
                    .setHcode(reqVO.getHcode()).setTogetherCodes(orderTogetherList));

            String expectedCurrencyUnit = generalConfig.getCurrencyUnit();

            for (AccountDO account : accountDOS) {
                if (!expectedCurrencyUnit.equals(account.getCurrencyUnit())) {
                    throw exception(MONEY_NOT_EQUAL);
                }
            }
        }
    }

    private void payCheckOutLogHandle(String no, PayCheckOutAccountReqVO reqVO) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(PMS_PAY_CHECKOUT_SUCCESS_TEMPLATE), "template");
        StringWriter writer = new StringWriter();
        Map<String, Object> context = new HashMap<>();
        context.put("orderTogethers", reqVO.getOrderTogethers());
        String content = mustache.execute(writer, context).toString();
        LogRecordContext.putVariable("order", reqVO);
        LogRecordContext.putVariable("no", no);
        LogRecordContext.putVariable("content", content);
    }

    @Override
    public List<AccountStatByBizDateRespVO> statAccountByBizDate(String gcode, String hcode, LocalDate bizDate) {
        return accountMapper.statAccountByBizDate(gcode, hcode, bizDate, AccountTypeEnum.CASH.getCode());
    }

    /**
     * 集团下付款科目列表
     *
     * @param gcode 集团代码
     * @return Map: key: 科目代码 value: 科目对象
     */
    private Map<String, GeneralConfigDO> getPayAccountMap(String gcode) {
        List<GeneralConfigDO> paySubList = generalConfigService.getGeneralConfigList(new GeneralConfigReqVO().setGcode(gcode).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode()));
        return CollectionUtils.convertMap(paySubList, GeneralConfigDO::getCode);
    }

    /**
     * 如果选中的subCode(收款、退款)与payMode不一样，或者付款金额与退房统计的金额不一致，那么就直接入账流程
     *
     * @param r               入参
     * @param payMode         支付模式(-1:退款 1:收款)
     * @param recordAccountNo 入账账号
     */
    private String pay(PayCheckOutAccountReqVO r, String payMode, String state, PayCheckOutAccountReqVO.OrderTogether recordAccountNo) {
        // 转换为 PayAccountSaveReqVO
        PayAccountSaveReqVO payAccount = AccountConvert.INSTANCE.payAccountConvert(r);
        if (NumberEnum.MINUS.getNumber().equals(payMode)) {
            payAccount.setFee(r.getPay().getFee() * -1);
        }
        if (BooleanEnum.TRUE.getValue().equals(recordAccountNo.getIsTeam())) {
            payAccount.setAccType(AccountTypeEnum.GROUP.getCode());
        } else {
            payAccount.setAccType(AccountTypeEnum.GENERAL.getCode());
        }
        payAccount.setTogetherCode(recordAccountNo.getTogetherCode())
                .setNo(recordAccountNo.getNo())
                .setState(state)
                .setVerifyMode(r.getVerifyMode())
                .setSmsCode(r.getSmsCode())
                .setIsSendSms(r.getIsSendSms());
        // 付款操作
        return pay(payAccount);
    }

    /**
     * 退房结账流程<br>
     * 1. 处理账务数据，在入账账号插入收款账务，同时添加合并结账做平衡,在其他客单账务插入合并结账做平衡.<br>
     * 1.1 如果退房时，选中了团队主单，那么账务平衡到团队主单里，否则平衡账务插入到选中的房间主客上。<br>
     * 1.2 如果选择入账的非主客单，那么付款账务需要插入到主客单上。<br>
     * 2. 处理客单和订单，订单下所有客单都退房，订单状态改为退房，如果存在挂账，则订单状态还是挂账；订单下多个客单，需要处理主客人；<br>
     * 3. 处理房间状态，订单下所有客单都退房或挂账，房间状态改为空脏<br>
     *
     * @param reqVO           入参
     * @param recordAccountNo 入账账号
     * @param payMode         支付模式（-1：退款 1：收款）
     */
    private void checkOutAccountOrderRoom(PayCheckOutAccountReqVO reqVO, PayCheckOutAccountReqVO.OrderTogether recordAccountNo, String payMode) {
        log.info("[checkOutAccountOrderRoom][开始退房结账处理] 支付模式={}, 入账账号={}", payMode, recordAccountNo.getTogetherCode());

        // 获取团队主账
        PayCheckOutAccountReqVO.OrderTogether teamOrderTogether = CollectionUtils.findFirst(reqVO.getOrderTogethers(), o -> Objects.equals(o.getIsTeam(), BooleanEnum.TRUE.getValue()));
        log.info("[checkOutAccountOrderRoom][团队主账信息] 是否有团队主账={}", teamOrderTogether != null);

        // 获取客单列表
        List<PayCheckOutAccountReqVO.OrderTogether> orderTogetherList = CollectionUtils.filterList(reqVO.getOrderTogethers(), o -> Objects.equals(o.getIsTeam(), BooleanEnum.FALSE.getValue()));
        List<String> togetherCodes = CollectionUtils.convertList(orderTogetherList, PayCheckOutAccountReqVO.OrderTogether::getTogetherCode);
        Set<String> orderNoSet = CollectionUtils.convertSet(orderTogetherList, PayCheckOutAccountReqVO.OrderTogether::getNo);
        log.info("[checkOutAccountOrderRoom][客单信息] 客单数量={}, 订单数量={}", togetherCodes.size(), orderNoSet.size());

        List<OrderTogetherDO> guests = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(togetherCodes)) {
            // 如果开启了主单入账模式，那么退房时，需要将房间下所有客人都退房
            boolean isMainOrderBilling = hotelParamConfigService.isMainOrderBilling(reqVO.getGcode(), reqVO.getHcode());
            if (isMainOrderBilling) {
                guests = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNoSet.stream().toList()));
            } else {
                guests = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setTogetherCodes(togetherCodes));
            }
            guests = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setTogetherCodes(togetherCodes));
        }
        // 1 验证团队主单和客单是否有已经退房的，并返回团队
        TeamDO team = validateGuests(guests, teamOrderTogether);

        // 付款
        PayCheckOutAccountReqVO.OrderTogether findOrderTogether = findMainOrTeamOrderTogether(reqVO, recordAccountNo);
        String payNo = pay(reqVO, payMode, AccountStatusEnum.CLOSED.getCode(), findOrderTogether);

        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        LocalDateTime now = LocalDateTime.now();
        // 需要关闭的账单号
        List<String> needCloseAccountNoList = buildTogetherCodeList(togetherCodes, Objects.isNull(team) ? "" : team.getTeamCode());
        List<AccountDO> accountList = CollUtil.newArrayList();
        // 获取未结账务
        if (CollUtil.isNotEmpty(needCloseAccountNoList)) {
            accountList = accountMapper.selectOpenAccountByTogetherCodes(new AccountReqVO().setGcode(reqVO.getGcode())
                    .setHcode(reqVO.getHcode())
                    .setTogetherCodeList(needCloseAccountNoList));
        }
        // 退房的订单
        List<OrderDO> orderList = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(orderNoSet)) {
            orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNoSet.stream().toList()));
        }
        Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
        // 2. 遍历账单，将所有未结账务设置为已结并返回需要修改的账单列表
        List<AccountDO> updateAccountList = buildUpdateAccounts(accountList, shiftNo, bizDate, payNo, now);

        // 3. 遍历账单，将账单的账务进行分组，并对每个账单进行计算获得付款金额，进行平账，平账类型为合并结账；对入账账号插入付款账务同时生成对应的合并账务
        List<AccountDO> insertAccountList = buildMergeAccounts(reqVO, shiftNo, bizDate, payNo, now, accountList, findOrderTogether);

        // 4. 如果房间下还有在住客人，且当前退房客人是主客人，那么需要修改在住客人为主客，并修改当前客人为退房,同时处理房间状态
        log.info("[checkOutAccountOrderRoom][开始处理退房订单和房间状态] 订单数量={}, 客人数量={}", orderNoSet.size(), guests.size());
        Boolean flag = checkOutOrderAndRoom(reqVO.getGcode(), reqVO.getHcode(), orderNoSet, guests, orderMap, shiftNo, now);
        log.info("[checkOutAccountOrderRoom][退房订单和房间状态处理完成] 是否有订单状态变更={}", flag);
        if (CollUtil.isNotEmpty(updateAccountList)) {
            accountMapper.updateBatch(updateAccountList);
        }
        if (CollUtil.isNotEmpty(insertAccountList)) {
            accountMapper.insertBatch(insertAccountList);
        }

        // 5. 修改团队状态为完成和账务状态为已结
        updateTeam(team, shiftNo, now);
        // 6. 获取账务统计信息
        /*OrderAccountStatBO orderAccountStatBO = statOrderAccountStatByNo(orderNoSet.stream().toList(), NumberEnum.ZERO.getNumber());
        if(!orderAccountStatBO.getCouponAmount().equals(orderAccountStatBO.getPayAmount())){
            throw exception(ACCOUNT_FEE_NOT_EQUAL);
        }*/
        // 会员退房赠券
        freeTicket(orderList);
        // 使用优惠券
        if (CollUtil.isNotEmpty(reqVO.getPayInfoList())) {
            useCoupon(reqVO, orderList.getFirst(), bizDate, shiftNo, null);
        }
        // 同步房态信息到前端
        List<String> rCodes = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(togetherCodes)) {
            orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode())
                            .setHcode(reqVO.getHcode())
                            .setTogetherCodes(togetherCodes))
                    .forEach(o -> rCodes.add(o.getRCode()));
            realRoomStateSenderService.sendRoomStatusMessage(reqVO.getGcode(), reqVO.getHcode(), rCodes);
        }
        // 发送订单信息
        if (flag) {
            bookingOrderSenderService.sendBookingOrderInfo(reqVO.getGcode(), reqVO.getHcode(), null, orderList.stream().map(OrderDO::getOrderNo).toList(), BookingOrderActionTypeEnum.CHECK_OUT.getCode(), null, null);
        }
        //记录日志
        payCheckOutLogHandle(recordAccountNo.getNo(), reqVO);
        if (NumberEnum.ONE.getNumber().equals(reqVO.getIsAutoTask())) {
            // 创建房扫任务
            taskProducer.sendRoomCleanTaskMessage(reqVO.getGcode(), reqVO.getHcode(), rCodes, RoomCleanTaskEnum.CHECK_OUT.getCode());
        }

        // 推送到PSB
        psbSenderService.sendPSBMessage(reqVO.getGcode(), reqVO.getHcode(), BookingOrderActionTypeEnum.CHECK_OUT.getCode(), guests);
        // 推送到第三方
        hiiiSenderService.sendCheckOutAccountOrderRoomInfo(guests, now, flag);
    }

    private void useCoupon(PayCheckOutAccountReqVO reqVO, OrderDO orderDO, LocalDate bizDate, String shiftNo, String state) {
        CouponUseReqDTO useReqDTO = new CouponUseReqDTO();
        useReqDTO.setGcode(reqVO.getGcode());
        useReqDTO.setHcode(reqVO.getHcode());
        useReqDTO.setOrderNo(orderDO.getOrderNo());
        useReqDTO.setBizDate(bizDate);
        useReqDTO.setShiftNo(shiftNo);
        useReqDTO.setRNo(orderDO.getRNo());
        useReqDTO.setRtCode(orderDO.getRtCode());
        useReqDTO.setCheckinType(orderDO.getCheckinType());
        useReqDTO.setState(state);
        useReqDTO.setCouponCode(BeanUtils.toBean(reqVO.getPayInfoList(), CouponUseSaveReqDTO.class));
        CommonResult<Boolean> res = couponApi.useCoupon(useReqDTO);
        if (res.getCode() != 0) {
            throw exception(FAILURE_REASON, res.getMsg());
        }
       /* BatchPayAccountSaveReqVO batchPayAccountSaveReqVO = new BatchPayAccountSaveReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setTogetherCode(findOrderTogether.getTogetherCode()).setNo(findOrderTogether.getNo())
                .setPayInfoList(BeanUtils.toBean(reqVO.getPayInfoList(), BatchPayAccountSaveReqVO.PayInfo.class));
        if (BooleanEnum.TRUE.getValue().equals(recordAccountNo.getIsTeam())) {
            batchPayAccountSaveReqVO.setAccType(AccountTypeEnum.GROUP.getCode());
        } else {
            batchPayAccountSaveReqVO.setAccType(AccountTypeEnum.GENERAL.getCode());
        }
        batchPay(batchPayAccountSaveReqVO);*/
    }

    private void freeTicket(List<OrderDO> orderList) {
        List<OrderDO> memberOrderList = CollectionUtils.filterList(orderList, o -> GuestSrcTypeEnum.MEMBER.getCode().equals(o.getGuestSrcType()));
        if (CollUtil.isEmpty(memberOrderList)) {
            return;
        }
        List<String> mcodes = CollectionUtils.convertList(memberOrderList, OrderDO::getGuestCode);
        List<MemberAndStoreCardRespDTO> memberAndStoreCardRespDTOS = memberApi.getMemberList(new MemberListReqDTO().setGcode(orderList.getFirst().getGcode())
                .setMcodes(mcodes)).getData();
        Map<String, MemberAndStoreCardRespDTO> memberMap = CollectionUtils.convertMap(memberAndStoreCardRespDTOS, MemberAndStoreCardRespDTO::getMcode);
        memberOrderList.forEach(order -> {
            //会员退房赠券
            CouponActivityReqDTO couponActivityReqDTO = new CouponActivityReqDTO();
            MemberAndStoreCardRespDTO member = memberMap.getOrDefault(order.getGuestCode(), new MemberAndStoreCardRespDTO());
            couponActivityReqDTO.setActivityCode(CouponActivityTypeEnum.CHECKOUT.getCode());
            couponActivityReqDTO.setGcode(order.getGcode());
            couponActivityReqDTO.setHcode(order.getHcode());
            couponActivityReqDTO.setName(member.getName());
            couponActivityReqDTO.setPhone(member.getPhone());
            couponActivityReqDTO.setSourceMtCode(member.getMtCode());
            couponActivityReqDTO.setTargetMtCode(member.getMtCode());
            //couponActivityReqDTO.setOrderNo(order.getOrderNo());

            couponActivityReqDTO.setOrderType(CouponOrderTypeEnum.ROOM.getCode());

            couponActivityApi.freeTicket(couponActivityReqDTO);
        });

    }

    /**
     * 构建 togetherCodeList，包含 togetherCodes 和 teamCode
     *
     * @param togetherCodes 客单代码集合
     * @param teamCode      团队代码
     * @return 拼接后的 togetherCodeList
     */
    private List<String> buildTogetherCodeList(List<String> togetherCodes, String teamCode) {
        if (StrUtil.isBlank(teamCode)) {
            return new ArrayList<>(togetherCodes);
        }
        return Stream.concat(togetherCodes.stream(), Stream.of(teamCode))
                .collect(Collectors.toList());
    }

    private void validateARAccountFee(PayCheckOutAccountReqVO reqVO) {
        List<FinishCheckOutAccountReqVO.OrderTogether> orderTogethers = BeanUtils.toBean(reqVO.getOrderTogethers(), FinishCheckOutAccountReqVO.OrderTogether.class);
        StatCheckOutAccountRespVO statCheckOutAccount = statCheckOutAccount(new FinishCheckOutAccountReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setSubCode(reqVO.getPay().getSubCode()).setOrderTogethers(orderTogethers).setOrderType(reqVO.getOrderType()));
        if (reqVO.getPay().getFee() > statCheckOutAccount.getFee()) {
            throw exception(CONSUME_ACCOUNT_NOT_EQUAL);
        }
    }

    /**
     * 返回入账账号<br>
     * 1. 如果是团队订单，查找选中的客单中是否包括团队主单，包括直接返回团队主单<br>
     * 2. 如果非团队订单,如果入账客单不为主客，需要找当前房间主客，如果当前房间主客不退房，那么选择下间房住客入账，如果都是非主客，将收款入驻到选中的客单上<br>
     * 3. 入账优先级：主单>主客>同来宾客单
     *
     * @param reqVO         入参
     * @param selectedOrder 选中的入账客单
     * @return 入账客单
     */
    private PayCheckOutAccountReqVO.OrderTogether findMainOrTeamOrderTogether(PayCheckOutAccountReqVO reqVO, PayCheckOutAccountReqVO.OrderTogether selectedOrder) {
        // 查找团队主单
        PayCheckOutAccountReqVO.OrderTogether mainOrTeamOrder = CollectionUtils.findFirst(reqVO.getOrderTogethers(), o -> Objects.equals(o.getIsTeam(), BooleanEnum.TRUE.getValue()));
        if (mainOrTeamOrder != null) {
            return mainOrTeamOrder;
        }
        // 如果选中的是主客单，直接返回选中客单
        if (Objects.equals(selectedOrder.getIsMain(), BooleanEnum.TRUE.getValue())) {
            return selectedOrder;
        }
        // 尝试找到与选中客单关联的主客单
        mainOrTeamOrder = CollectionUtils.findFirst(reqVO.getOrderTogethers(), o -> Objects.equals(o.getIsMain(), BooleanEnum.TRUE.getValue()) && Objects.equals(o.getNo(), selectedOrder.getNo()));
        if (mainOrTeamOrder != null) {
            return mainOrTeamOrder;
        }
        // 最后尝试找到任意一个主客单
        mainOrTeamOrder = CollectionUtils.findFirst(reqVO.getOrderTogethers(), o -> Objects.equals(o.getIsMain(), BooleanEnum.TRUE.getValue()));
        return mainOrTeamOrder != null ? mainOrTeamOrder : selectedOrder;
    }

    /**
     * 更新团队状态
     *
     * @param team    团队
     * @param shiftNo 班次号
     * @param now     系统时间
     */
    private void updateTeam(TeamDO team, String shiftNo, LocalDateTime now) {
        if (team == null) {
            return;
        }
        teamService.updateTeam(new TeamDO().setId(team.getId())
                .setState(OrderStateEnum.OVER.getCode())
                .setAccState(AccountStatusEnum.CLOSED.getCode())
                .setPayShiftNo(shiftNo)
                .setPayTime(now)
                .setPayOperator(SecurityFrameworkUtils.getLoginUserName()));
    }

    /**
     * 验证客单是否有已经退房的
     *
     * @param guests            客单列表
     * @param teamOrderTogether 团队主单
     * @return 团队
     */
    private TeamDO validateGuests(List<OrderTogetherDO> guests, PayCheckOutAccountReqVO.OrderTogether teamOrderTogether) {
        StringBuilder closeRoomsBuilder = new StringBuilder();
        TeamDO team = null;
        if (CollUtil.isEmpty(guests) && Objects.isNull(teamOrderTogether)) {
            throw exception(ACCOUNT_ORDER_CLOSED2);
        }
        if (Objects.nonNull(teamOrderTogether)) {
            team = teamService.getTeam(teamOrderTogether.getTogetherCode());
            if (Objects.nonNull(team) && Objects.equals(AccountStatusEnum.CLOSED.getCode(), team.getAccState())) {
                String x = String.join("-", AccountTypeEnum.GROUP.getName(), team.getTeamName());
                closeRoomsBuilder.append(x);
                throw exception(ACCOUNT_TEAM_CLOSED, x); // 单独抛出团队账务已结账的异常
            }
        }
        if (CollUtil.isNotEmpty(guests)) {
            for (OrderTogetherDO o : guests) {
                if (Objects.equals(OrderStateEnum.CHECK_OUT.getCode(), o.getState())) {
                    String y = String.join("-", o.getRNo(), o.getName());
                    closeRoomsBuilder.append(",").append(y);
                }
            }
        }
        String closeRooms = closeRoomsBuilder.toString();
        if (StrUtil.isNotBlank(closeRooms)) {
            throw exception(ACCOUNT_ORDER_CLOSED, closeRooms);
        }
        return team;
    }

    /**
     * 封装需要更新账务的账务列表
     *
     * @param accountList 账务列表
     * @param shiftNo     班次号
     * @param bizDate     营业日期
     * @param payNo       结账号
     * @param now         系统时间
     * @return 账务列表
     */
    private List<AccountDO> buildUpdateAccounts(List<AccountDO> accountList, String shiftNo, LocalDate bizDate, String payNo, LocalDateTime now) {
        if (CollUtil.isEmpty(accountList)) {
            return CollUtil.newArrayList();
        }
        List<AccountDO> updateAccountList = CollUtil.newArrayList();
        accountList.forEach(account -> {
            updateAccountList.add(AccountDO.builder()
                    .id(account.getId())
                    .state(AccountStatusEnum.CLOSED.getCode())
                    .isCanRev(BooleanEnum.FALSE.getValue())
                    .payNo(payNo)
                    .payShiftNo(shiftNo)
                    .payBizDate(bizDate)
                    .payTime(now)
                    .payer(SecurityFrameworkUtils.getLoginUserName())
                    .handleShiftNo(shiftNo)
                    .build());
        });
        return updateAccountList;
    }

    /**
     * 封装合并结账科目的账务列表
     *
     * @param reqVO             入参
     * @param shiftNo           班次号
     * @param bizDate           营业日期
     * @param payNo             结账号
     * @param now               系统时间
     * @param accountList       账务列表
     * @param findOrderTogether 入账客单
     * @return 账务列表
     */
    private List<AccountDO> buildMergeAccounts(PayCheckOutAccountReqVO reqVO, String shiftNo, LocalDate bizDate, String payNo, LocalDateTime now,
                                               List<AccountDO> accountList, PayCheckOutAccountReqVO.OrderTogether findOrderTogether) {
        List<AccountDO> insertAccountList = CollUtil.newArrayList();
        if (CollUtil.isEmpty(accountList)) {
            return insertAccountList;
        }

        // 按照客单号进行分组
        Map<String, List<AccountDO>> accountGroupMap = CollectionUtils.convertMultiMap(accountList, AccountDO::getTogetherCode);

        // 过滤出非入账客单列表
        List<PayCheckOutAccountReqVO.OrderTogether> otherOrderTogethers = CollectionUtils.filterList(reqVO.getOrderTogethers(), o -> !Objects.equals(o.getTogetherCode(), findOrderTogether.getTogetherCode()));

        // 循环非入账客单列表，生成平衡客单账务的合并结账,同时生成在入账单中需要插入的合并结账账务作为入账客单的平衡账务,这个账务金额与非入账客单中的金额相反
        if (CollUtil.isNotEmpty(otherOrderTogethers)) {
            for (PayCheckOutAccountReqVO.OrderTogether o : otherOrderTogethers) {
                // 在其他账号下添加合并结账来平衡账务,状态为已结
                AccountDO mergedAccount = buildMergeAccount(accountGroupMap.get(o.getTogetherCode()));
                if (mergedAccount != null) {
                    mergedAccount.setShiftNo(shiftNo)
                            .setRecorder(SecurityFrameworkUtils.getLoginUserName())
                            .setBizDate(bizDate)
                            .setPayNo(payNo)
                            .setPayShiftNo(shiftNo)
                            .setPayBizDate(bizDate)
                            .setPayTime(now)
                            .setPayNo(payNo)
                            .setPayer(SecurityFrameworkUtils.getLoginUserName());
                    insertAccountList.add(mergedAccount);
                    // 生成入账客单的平衡账务
                    AccountDO balanceAccount = BeanUtils.toBean(mergedAccount, AccountDO.class);
                    balanceAccount.setNo(findOrderTogether.getNo())
                            .setTogetherCode(findOrderTogether.getTogetherCode())
                            .setAccNo(IdUtil.getSnowflakeNextIdStr())
                            .setSubCode(PayAccountEnum.MERGE_ACCOUNT.getCode())
                            .setPayNo(payNo)
                            .setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode()).setFee(mergedAccount.getFee() * -1)
                            .setAccDetail(PayAccountEnum.MERGE_ACCOUNT.getLabel());
                    insertAccountList.add(balanceAccount);
                }
            }
        }

        return insertAccountList;
    }

    /**
     * 联房进行退房或挂账时需要设置新的联房主单
     *
     * @param gcode      集团代码
     * @param hcode      门店代码
     * @param guests     客单列表
     * @param orderMap   订单列表
     * @param handleType 处理类型 0: 挂账 1：退房
     * @return 返回新的主单
     */
    private OrderDO handleMergeMainOrder(String gcode, String hcode, List<OrderTogetherDO> guests, Map<String, OrderDO> orderMap, String handleType) {
        if (CollUtil.isEmpty(guests) || CollUtil.isEmpty(orderMap)) {
            return null;
        }
        OrderDO newMainOrder = null;
        OrderDO firstOrder = orderMap.get(guests.getFirst().getOrderNo());

        // 1 如果是联房订单，则把所有的其他订单也查询出来，如果退房订单里存在主单，那么要重新设置未退房订单其中一个为主单
        if (Objects.equals(OrderTypeEnum.JOIN.getCode(), firstOrder.getOrderType())) {
            // 查询旧主单
            OrderDO oldMainOrder = null;
            for (Map.Entry<String, OrderDO> entry : orderMap.entrySet()) {
                if (Objects.equals(entry.getValue().getIsMain(), BooleanEnum.TRUE.getValue())) {
                    oldMainOrder = entry.getValue();
                }
            }
            if (oldMainOrder == null) {
                return null;
            }
            List<String> states = CollUtil.newArrayList(OrderStateEnum.CHECK_IN.getCode());
            if (Objects.equals(handleType, BooleanEnum.TRUE.getValue())) {
                states.add(OrderStateEnum.CREDIT.getCode());
            }
            List<OrderDO> joinOrderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode)
                    .setHcode(hcode)
                    .setBindCode(firstOrder.getBindCode())
                    .setStates(states));
            // 在住联房订单
            List<OrderDO> inHouseJoinOrderList = CollectionUtils.filterList(joinOrderList, order -> !orderMap.containsKey(order.getOrderNo()));
            // 迭代orderMap,判断中是否存在联房主单
            if (CollUtil.isNotEmpty(inHouseJoinOrderList)) {
                // 找出在住的订单
                List<OrderDO> inHouseJoinOrder = CollectionUtils.filterList(inHouseJoinOrderList, order -> Objects.equals(order.getState(), OrderStateEnum.CHECK_IN.getCode()));
                if (CollUtil.isNotEmpty(inHouseJoinOrder)) {
                    OrderDO firstInHouseJoinOrder = inHouseJoinOrder.getFirst();
                    newMainOrder = OrderDO.builder().id(firstInHouseJoinOrder.getId()).orderNo(firstInHouseJoinOrder.getOrderNo()).isMain(BooleanEnum.TRUE.getValue()).build();
                } else {
                    // 找出挂账的订单
                    List<OrderDO> creditJoinOrder = CollectionUtils.filterList(inHouseJoinOrderList, order -> Objects.equals(order.getState(), OrderStateEnum.CREDIT.getCode()));
                    if (CollUtil.isNotEmpty(creditJoinOrder)) {
                        OrderDO firstCreditJoinOrder = creditJoinOrder.getFirst();
                        newMainOrder = OrderDO.builder().id(firstCreditJoinOrder.getId()).orderNo(firstCreditJoinOrder.getOrderNo()).isMain(BooleanEnum.TRUE.getValue()).build();
                    }
                }
            }
        }
        return newMainOrder;
    }

    /**
     * 获取订单下所有的客单
     *
     * @param gcode      集团代码
     * @param hcode      门店代码
     * @param orderNoSet 订单集合
     * @return 订单号对每间房下的客单进行分组 key:订单号 value:客单列表
     */
    private Map<String, List<OrderTogetherDO>> getOrderTogetherGroupMap(String gcode, String hcode, Set<String> orderNoSet) {
        // 获取订单下所有的客单
        List<OrderTogetherDO> guestsInMoreRoomList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode)
                .setHcode(hcode)
                .setOrderNos(orderNoSet.stream().toList()));
        //按照订单号对每间房下的客单进行分组 key:订单号 value:客单列表
        return CollectionUtils.convertMultiMap(guestsInMoreRoomList, OrderTogetherDO::getOrderNo);
    }

    /**
     * 处理退房客人，如果是房间下无在住客人，那么将房间设置为空脏，客人订单设置为退房；如果是房间下有在住客人，且无主客，那么默认一个客人为当前房间下的主客<br>
     * 如果房间下没有在住客人，那么将房间设置为空脏
     *
     * @param gcode      集团代码
     * @param hcode      门店代码
     * @param orderNoSet 订单号列表(退房客人对应的订单号)
     * @param guests     客单列表(退房的客人)
     * @param orderMap   退房订单Map
     * @param shiftNo    班次
     * @param now        当前时间
     */
    private Boolean checkOutOrderAndRoom(String gcode,
                                         String hcode,
                                         Set<String> orderNoSet,
                                         List<OrderTogetherDO> guests,
                                         Map<String, OrderDO> orderMap,
                                         String shiftNo,
                                         LocalDateTime now) {
        if (CollUtil.isEmpty(guests)) {
            return false;
        }
        // 需要修改的客单
        List<OrderTogetherDO> updateOrderTogetherList = CollUtil.newArrayList();
        // 需要修改的订单
        List<OrderDO> updateOrderList = CollUtil.newArrayList();
        // 需要退房的房间代码列表
        List<String> rCodeList = CollUtil.newArrayList();
        // 1. 联房进行退房或挂账时需要设置新的联房主单, 如果是团队的订单，则不需要设置主单
        OrderDO newMainOrder = handleMergeMainOrder(gcode, hcode, guests, orderMap, BooleanEnum.TRUE.getValue());

        //按照订单号对每间房下的客单进行分组 key:订单号 value:客单列表
        Map<String, List<OrderTogetherDO>> guestsInOneRoomGroupMap = getOrderTogetherGroupMap(gcode, hcode, orderNoSet);
        //按照订单号对本次退房房间的客单进行分组 key:订单号 value:退房客单
        Map<String, List<OrderTogetherDO>> checkOutGuestsGroupMap = CollectionUtils.convertMultiMap(guests, OrderTogetherDO::getOrderNo);
        // 2. 遍历退房客单分组，处理客单状态
        for (Map.Entry<String, List<OrderTogetherDO>> entry : checkOutGuestsGroupMap.entrySet()) {
            String orderNo = entry.getKey();
            List<OrderTogetherDO> checkOutGuests = entry.getValue();

            // 没有在本次退房名单中的客人列表
            List<OrderTogetherDO> checkOutGuestsNoRepeat = checkOutGuestsNoRepeat(guestsInOneRoomGroupMap.get(orderNo), checkOutGuests);
            // 2.1 判断是否组内全退,包括挂账
            if (isAllGuestCheckOut(guestsInOneRoomGroupMap.get(orderNo), checkOutGuests, checkOutGuestsNoRepeat)) {
                OrderDO order = orderMap.get(orderNo);
                int days = (int) ChronoUnit.DAYS.between(order.getCheckinTime().toLocalDate(), now.toLocalDate());
                OrderDO o = OrderDO.builder()
                        .id(order.getId())
                        .state(OrderStateEnum.CHECK_OUT.getCode())
                        .checkoutTime(now)
                        .build();
                if (days == 0) {
                    days = 1;
                }
                o.setDays(days);
                if (Objects.nonNull(newMainOrder)) {
                    o.setIsMain(BooleanEnum.FALSE.getValue());
                    updateOrderList.add(newMainOrder);
                }
                updateOrderList.add(o);
            } else {
                if (Objects.nonNull(newMainOrder)) {
                    newMainOrder.setIsMain(BooleanEnum.FALSE.getValue());
                    updateOrderList.add(newMainOrder);
                }

            }
            // 2.2 判断退房客人里是否存在主客
            OrderTogetherDO mainGuest = getMainGuestCheckOut(checkOutGuests);
            // 2.3 如果有在住的未退房客人，那么设置该客人为主客
            if (Objects.nonNull(mainGuest)) {
                OrderTogetherDO newMainGuest = newMainGuest(checkOutGuestsNoRepeat);
                if (Objects.nonNull(newMainGuest)) {
                    updateOrderTogetherList.add(newMainGuest);
                }
            }
            // 2.4 处理客单状态
            checkOutGuests.forEach(guest -> {
                // 判断是否需要修改主客的isMain字段：只有在订单下还有未退房客人时，才将原主客的isMain设置为FALSE
                String isMainValue = null;
                if (mainGuest != null && Objects.equals(guest.getTogetherCode(), mainGuest.getTogetherCode())) {
                    // 当前退房的是原主客，需要判断订单下是否还有未退房的客人
                    boolean hasRemainingGuests = checkOutGuestsNoRepeat.stream()
                            .anyMatch(remainingGuest -> Objects.equals(remainingGuest.getState(), OrderStateEnum.CHECK_IN.getCode()));
                    // 只有在还有未退房客人的情况下，才将原主客的isMain设置为FALSE
                    if (hasRemainingGuests) {
                        isMainValue = BooleanEnum.FALSE.getValue();
                    }
                }

                OrderTogetherDO checkOutGuest = OrderTogetherDO.builder()
                        .id(guest.getId())
                        .state(OrderStateEnum.CHECK_OUT.getCode())
                        .checkoutShiftNo(shiftNo)
                        .checkoutTime(now)
                        .checkoutOperator(SecurityFrameworkUtils.getLoginUserName())
                        .payShiftNo(shiftNo)
                        .payTime(now)
                        .payOperator(SecurityFrameworkUtils.getLoginUserName())
                        .isMain(isMainValue)
                        .build();
                updateOrderTogetherList.add(checkOutGuest);
            });

            // 3. 退房
            List<String> rCodes = checkOutRoom(guestsInOneRoomGroupMap.get(orderNo), checkOutGuests, checkOutGuestsNoRepeat, orderMap.get(orderNo));
            rCodeList.addAll(rCodes);
        }
        if (!updateOrderList.isEmpty()) {
            orderService.updateOrders(updateOrderList);
        }
        if (!updateOrderTogetherList.isEmpty()) {
            orderTogetherService.updateOrderTogetherByOrderTogetherDOList(updateOrderTogetherList);
        }
        if (!rCodeList.isEmpty()) {
            log.info("[checkOutOrderAndRoom][开始更新退房房间状态为空脏] 房间列表={}", rCodeList);
            roomService.updateStates(rCodeList, gcode, hcode, RoomStatusEnum.VD);
            log.info("[checkOutOrderAndRoom][退房房间状态更新完成] 更新房间数量={}", rCodeList.size());
        } else {
            log.info("[checkOutOrderAndRoom][没有房间需要更新状态]");
        }

        return !updateOrderList.isEmpty();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_CREDIT_CHECKOUT_SUCCESS, type = PMS_ORDER_CREDIT_CHECKOUT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.orderNo}}",
            extra = PMS_ORDER_property)
    public void creditCheckOut(ConfirmRoomFeeReqVO reqVO) {
        // 需要修改的订单
        List<OrderDO> updateOrderList = CollUtil.newArrayList();
        // 需要退房的房间代码列表
        List<String> rCodeList = CollUtil.newArrayList();
        // 验证其他账号是否已经退房
        List<String> togetherCodes = CollectionUtils.convertList(reqVO.getOrderTogethers(), ConfirmRoomFeeReqVO.OrderTogether::getTogetherCode);
        Set<String> orderNoSet = CollectionUtils.convertSet(reqVO.getOrderTogethers(), ConfirmRoomFeeReqVO.OrderTogether::getNo);
        List<OrderTogetherDO> guests = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(togetherCodes)) {
            boolean isMainOrderBilling = hotelParamConfigService.isMainOrderBilling(reqVO.getGcode(), reqVO.getHcode());
            if (isMainOrderBilling) {
                guests = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNoSet.stream().toList()));
            } else {
                guests = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setTogetherCodes(togetherCodes));
            }
            guests = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setTogetherCodes(togetherCodes));
        }
        // 退房的订单
        List<OrderDO> orderList = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(orderNoSet)) {
            orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNoSet.stream().toList()));
        }
        Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
        // 1. 验证客单是否可以挂账(在住状态才可以挂账退房)
        validateCreditCheckOut(guests);
        // 2. 联房进行退房或挂账时需要设置新的联房主单
        OrderDO newMainOrder = handleMergeMainOrder(reqVO.getGcode(), reqVO.getHcode(), guests, orderMap, BooleanEnum.FALSE.getValue());
       /* if (Objects.nonNull(newMainOrder)) {
            updateOrderList.add(newMainOrder);
        }*/
        //按照订单号对每间房下的客单进行分组 key:订单号 value:客单列表
        Map<String, List<OrderTogetherDO>> guestsInOneRoomGroupMap = getOrderTogetherGroupMap(reqVO.getGcode(), reqVO.getHcode(), orderNoSet);
        //按照订单号对本次退房房间的客单进行分组 key:订单号 value:退房客单
        Map<String, List<OrderTogetherDO>> checkOutGuestsGroupMap = CollectionUtils.convertMultiMap(guests, OrderTogetherDO::getOrderNo);
        LocalDateTime now = LocalDateTime.now();
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        // 需要修改的客单
        List<OrderTogetherDO> updateOrderTogetherList = CollUtil.newArrayList();
        // 3. 处理客单挂账
        // 3.1 遍历退房客单分组，处理客单状态
        for (Map.Entry<String, List<OrderTogetherDO>> entry : checkOutGuestsGroupMap.entrySet()) {
            String orderNo = entry.getKey();
            List<OrderTogetherDO> checkOutGuests = entry.getValue();

            // 没有在本次挂账名单中的客单列表
            List<OrderTogetherDO> creditGuestsNoRepeat = checkOutGuestsNoRepeat(guestsInOneRoomGroupMap.get(orderNo), checkOutGuests);
            // 3.2 判断是否可设置订单为挂账
            boolean isCreditOrder = isCreditOrder(creditGuestsNoRepeat);
            if (isCreditOrder) {
                int days = (int) ChronoUnit.DAYS.between(orderMap.get(orderNo).getCheckinTime().toLocalDate(), now.toLocalDate());
                if (days == 0) {
                    days = 1;
                }
                OrderDO o = OrderDO.builder().id(orderMap.get(orderNo).getId())
                        .state(OrderStateEnum.CREDIT.getCode())
                        .checkoutTime(now)
                        .days(days)
                        .build();
                if (Objects.nonNull(newMainOrder)) {
                    o.setIsMain(BooleanEnum.FALSE.getValue());
                    updateOrderList.add(newMainOrder);
                }
                updateOrderList.add(o);
            } else {
                if (Objects.nonNull(newMainOrder)) {
                    newMainOrder.setIsMain(BooleanEnum.FALSE.getValue());
                    updateOrderList.add(newMainOrder);
                }
            }

            // 3.5 处理客单状态
            List<OrderTogetherDO> updateOrderTogetherListX = buildUpdateOrderTogethers(checkOutGuests, creditGuestsNoRepeat, shiftNo, now, reqVO.getRemark());
            updateOrderTogetherList.addAll(updateOrderTogetherListX);

            // 3.6. 退房
            List<String> rCodeListX = checkOutRoom(guestsInOneRoomGroupMap.get(orderNo), checkOutGuests, creditGuestsNoRepeat, orderMap.get(orderNo));
            rCodeList.addAll(rCodeListX);
        }
        if (CollUtil.isNotEmpty(updateOrderList)) {
            orderService.updateOrders(updateOrderList);
        }
        if (CollUtil.isNotEmpty(updateOrderTogetherList)) {
            orderTogetherService.updateOrderTogetherByOrderTogetherDOList(updateOrderTogetherList);
        }
        if (CollUtil.isNotEmpty(rCodeList)) {
            log.info("[creditCheckOut][开始更新挂账退房房间状态为空脏] 房间列表={}", rCodeList);
            roomService.updateStates(rCodeList, reqVO.getGcode(), reqVO.getHcode(), RoomStatusEnum.VD);
            log.info("[creditCheckOut][挂账退房房间状态更新完成] 更新房间数量={}", rCodeList.size());
        } else {
            log.info("[creditCheckOut][没有房间需要更新状态]");
        }

        //记录日志
        creditCheckOutLogHandle(guests, reqVO);
        if (NumberEnum.ONE.getNumber().equals(reqVO.getIsAutoTask())) {
            // 创建任务
            taskProducer.sendRoomCleanTaskMessage(reqVO.getGcode(), reqVO.getHcode(), rCodeList, RoomCleanTaskEnum.CHECK_OUT.getCode());
        }

        // 推送到PSB
        psbSenderService.sendPSBMessage(reqVO.getGcode(), reqVO.getHcode(), BookingOrderActionTypeEnum.CHECK_OUT.getCode(), guests);
        // 推送到第三方
        hiiiSenderService.sendCheckOutAccountOrderRoomInfo(guests, now, CollUtil.isNotEmpty(updateOrderList));

    }

    private void creditCheckOutLogHandle(List<OrderTogetherDO> guests, ConfirmRoomFeeReqVO reqVO) {
        if (CollUtil.isNotEmpty(guests)) {
            MustacheFactory mf = new DefaultMustacheFactory();
            Mustache mustache = mf.compile(new StringReader(PMS_CREDIT_CHECKOUT_SUCCESS_TEMPLATE), "template");
            StringWriter writer = new StringWriter();
            Map<String, Object> context = new HashMap<>();
            context.put("orderList", guests);
            String content = mustache.execute(writer, context).toString();
            LogRecordContext.putVariable("order", guests.getFirst());
            LogRecordContext.putVariable("remark", reqVO.getRemark());
            LogRecordContext.putVariable("content", content);
        }

    }

    /**
     * (单个房间)封装需要处理的客单列表
     *
     * @param checkOutGuests       退房的客单列表
     * @param creditGuestsNoRepeat 没有在本次挂账名单中的客单列表
     * @param shiftNo              班次号
     * @param now                  系统时间
     * @param remark               挂账退房时的备注内容
     * @return 封装后的客单列表
     */
    private List<OrderTogetherDO> buildUpdateOrderTogethers(List<OrderTogetherDO> checkOutGuests, List<OrderTogetherDO> creditGuestsNoRepeat, String shiftNo, LocalDateTime now, String remark) {
        List<OrderTogetherDO> updateOrderTogetherList = CollUtil.newArrayList();
        // 3.3 判断挂账客单里是否存在主客
        OrderTogetherDO mainGuest = getMainGuestCheckOut(checkOutGuests);
        // 3.4 如果有在住的未退房客人，那么设置该客人为主客
        if (Objects.nonNull(mainGuest)) {
            OrderTogetherDO newMainGuest = newMainGuest(creditGuestsNoRepeat);
            if (Objects.nonNull(newMainGuest)) {
                updateOrderTogetherList.add(newMainGuest);
            }
        }
        checkOutGuests.forEach(guest -> {
            OrderTogetherDO creditGuest = OrderTogetherDO.builder()
                    .id(guest.getId())
                    .state(OrderStateEnum.CREDIT.getCode())
                    .checkoutShiftNo(shiftNo)
                    .checkoutTime(now)
                    .checkoutOperator(SecurityFrameworkUtils.getLoginUserName())
                    .payShiftNo(shiftNo)
                    .payTime(now)
                    .payOperator(SecurityFrameworkUtils.getLoginUserName())
                    .remark(remark)
                    .build();
            // 判断是否需要修改主客的isMain字段：只有在订单下还有未退房客人时，才将原主客的isMain设置为FALSE
            if (Objects.nonNull(mainGuest) && Objects.equals(guest.getTogetherCode(), mainGuest.getTogetherCode())) {
                // 当前挂账退房的是原主客，需要判断订单下是否还有未退房的客人
                boolean hasRemainingGuests = creditGuestsNoRepeat.stream()
                        .anyMatch(remainingGuest -> Objects.equals(remainingGuest.getState(), OrderStateEnum.CHECK_IN.getCode()));
                // 只有在还有未退房客人的情况下，才将原主客的isMain设置为FALSE
                if (hasRemainingGuests) {
                    creditGuest.setIsMain(BooleanEnum.FALSE.getValue());
                }
            }
            updateOrderTogetherList.add(creditGuest);
        });
        return updateOrderTogetherList;
    }

    /**
     * 验证挂账客单是否允许挂账
     *
     * @param guests 客单列表
     */
    private void validateCreditCheckOut(List<OrderTogetherDO> guests) {
        if (CollUtil.isNotEmpty(guests)) {
            guests.forEach(guest -> {
                if (!Objects.equals(guest.getState(), OrderStateEnum.CHECK_IN.getCode())) {
                    throw exception(ORDER_CREDIT_CHECK_OUT_ERROR);
                }
            });
        }
    }

    /**
     * 获取需要退房和挂账的房间代码<p>
     * 如果房间下没有在住客人，那么将房间设置为空脏
     *
     * @param guestsInOneRoom        房间下所有的客单
     * @param checkOutGuests         房间下要退房或挂账的客单
     * @param checkOutGuestsNoRepeat 不在本次退房或挂账名单中的客人列表
     * @param order                  要退房的订单
     * @return rCodeList              需要退房或挂账的房间列表
     */
    private List<String> checkOutRoom(List<OrderTogetherDO> guestsInOneRoom,
                                      List<OrderTogetherDO> checkOutGuests,
                                      List<OrderTogetherDO> checkOutGuestsNoRepeat, OrderDO order) {
        log.info("[checkOutRoom][开始判断房间是否需要退房] 订单号={}, 房间下总客人数={}, 退房客人数={}, 未退房客人数={}",
                order.getOrderNo(), guestsInOneRoom.size(), checkOutGuests.size(),
                checkOutGuestsNoRepeat != null ? checkOutGuestsNoRepeat.size() : 0);

        // 如果当前退房的订单是挂账状态，那么不需要修改房态
        if (Objects.equals(order.getState(), OrderStateEnum.CREDIT.getCode())) {
            log.info("[checkOutRoom][订单已是挂账状态，不需要修改房态] 订单号={}", order.getOrderNo());
            return CollUtil.newArrayList();
        }

        List<String> rCodeList = CollUtil.newArrayList();
        String roomCode = checkOutGuests.getFirst().getRCode();

        if (guestsInOneRoom.size() == checkOutGuests.size()) {
            log.info("[checkOutRoom][房间下所有客人都退房，房间需要变更为空脏] 房间代码={}", roomCode);
            rCodeList.add(roomCode);
        }

        // 如果房间下没有在住的客单，那么就可以退房
        if (CollUtil.isNotEmpty(checkOutGuestsNoRepeat)) {
            List<OrderTogetherDO> stillCheckInGuests = CollectionUtils.filterList(checkOutGuestsNoRepeat,
                    guest -> Objects.equals(guest.getState(), OrderStateEnum.CHECK_IN.getCode()));
            if (stillCheckInGuests.isEmpty()) {
                log.info("[checkOutRoom][房间下没有在住客人，房间需要变更为空脏] 房间代码={}", roomCode);
                rCodeList.add(roomCode);
            } else {
                log.info("[checkOutRoom][房间下还有在住客人，房间状态不变] 房间代码={}, 在住客人数={}",
                        roomCode, stillCheckInGuests.size());
            }
        }

        log.info("[checkOutRoom][房间退房判断完成] 需要更新状态的房间={}", rCodeList);
        return rCodeList;
    }

    /**
     * 获取新的主客单
     *
     * @param checkOutGuestsNoRepeat 不在本次退房名单中的客人列表
     * @return OrderTogetherDO 新主客单，无返回null
     */
    private OrderTogetherDO newMainGuest(List<OrderTogetherDO> checkOutGuestsNoRepeat) {
        for (OrderTogetherDO together : checkOutGuestsNoRepeat) {
            if (Objects.equals(together.getIsMain(), BooleanEnum.TRUE.getValue())) {
                return null;
            }
            if (Objects.equals(together.getState(), OrderStateEnum.CHECK_IN.getCode())) {
                return OrderTogetherDO.builder().id(together.getId()).isMain(BooleanEnum.TRUE.getValue()).build();
            }
        }
        return null;
    }

    /**
     * 退房或挂账列表中是否存在主客
     *
     * @param checkOutGuests 退房列表
     * @return OrderTogetherDO 主客，没有返回null
     */
    private OrderTogetherDO getMainGuestCheckOut(List<OrderTogetherDO> checkOutGuests) {
        for (OrderTogetherDO together : checkOutGuests) {
            if (Objects.equals(together.getIsMain(), BooleanEnum.TRUE.getValue())) {
                return together;
            }
        }
        return null;
    }

    /**
     * 比较是否全退(不包括挂账退房)
     *
     * @param guestsInOneRoom        房间下所有客人
     * @param checkOutGuests         房间下要退房的客人
     * @param checkOutGuestsNoRepeat 本次不参与退房的客人
     * @return true 全退 false 部分退
     */
    private boolean isAllGuestCheckOut(List<OrderTogetherDO> guestsInOneRoom, List<OrderTogetherDO> checkOutGuests, List<OrderTogetherDO> checkOutGuestsNoRepeat) {
        boolean allCheckOut = true;
        if (checkOutGuests.size() == guestsInOneRoom.size()) {
            return true;
        }
        for (OrderTogetherDO o : checkOutGuestsNoRepeat) {
            if (!List.of(OrderStateEnum.CHECK_OUT.getCode(), OrderStateEnum.CREDIT.getCode()).contains(o.getState())) {
                return false;
            }
        }
        return allCheckOut;
    }

    /**
     * 判断订单是否可以设置为挂账
     *
     * @param creditGuestsNoRepeat 本次不参与挂账的客单
     * @return true 可挂账 false 不可挂账
     */
    private boolean isCreditOrder(List<OrderTogetherDO> creditGuestsNoRepeat) {
        boolean f = true;
        if (CollUtil.isEmpty(creditGuestsNoRepeat)) {
            return true;
        }
        for (OrderTogetherDO o : creditGuestsNoRepeat) {
            if (Objects.equals(OrderStateEnum.CHECK_IN.getCode(), o.getState())) {
                return false;
            }
        }
        return f;
    }

    /**
     * 获取本次未办理退房客单
     *
     * @param guestsInOneRoom 房间下所有客单
     * @param checkOutGuests  房间下要退房的客单
     * @return List<OrderTogetherDO>
     */
    private List<OrderTogetherDO> checkOutGuestsNoRepeat(List<OrderTogetherDO> guestsInOneRoom, List<OrderTogetherDO> checkOutGuests) {
        return guestsInOneRoom.stream().filter(o -> checkOutGuests.stream()
                .noneMatch(together -> Objects.equals(together.getTogetherCode(), o.getTogetherCode()))).toList();
    }

    /**
     * 生成合并结账账务
     *
     * @param accountList 需要结账的账务列表
     * @return AccountDO 合并结账账务
     */
    private AccountDO buildMergeAccount(List<AccountDO> accountList) {
        if (CollUtil.isEmpty(accountList)) {
            return null;
        }
        AccountDO first = accountList.getFirst();
        AccountDO mergeAccount = AccountConvert.INSTANCE.mergeAccountConvert(first);
        long payFee = 0L;
        long consumeFee = 0L;
        for (AccountDO accountDO : accountList) {
            if (Objects.equals(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), accountDO.getSubType())) {
                payFee += accountDO.getFee();
            }
            if (Objects.equals(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), accountDO.getSubType())) {
                consumeFee += accountDO.getFee();
            }
        }
        if (payFee == consumeFee) {
            return null;
        }
        mergeAccount.setFee(consumeFee - payFee);
        return mergeAccount;
    }

    /**
     * 退房结账时验证并返回入账账号
     *
     * @param r 入参
     * @return 结账账务要入账的客单
     */
    private PayCheckOutAccountReqVO.OrderTogether validatePayCheckOut(PayCheckOutAccountReqVO r) {
        // 验证payCode不能为空
        List<String> needPayCodeList = List.of(
                PayAccountEnum.SCAN_GUN_WX.getCode(), PayAccountEnum.SCAN_GUN_ALIPAY.getCode(),
                SCAN_GUN.getCode(), PayAccountEnum.COUPON.getCode(),
                CREDIT_S_ACCOUNT.getCode(), PayAccountEnum.FUYOU_RECEIPT.getCode());
        if (STORE_CARD.getCode().equals(r.getPay().getSubCode()) && StrUtil.isBlank(r.getPay().getPayCode())) {
            throw exception(ACCOUNT_CARD_NOT_EXISTS);
        }
        if (needPayCodeList.contains(r.getPay().getSubCode()) && StrUtil.isBlank(r.getPay().getPayCode())) {
            throw exception(ACCOUNT_PAY_CODE_NOT_NULL);
        }
        if (List.of(PayAccountEnum.BANK_PRE_AUTH.getCode(), PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()).contains(r.getPay().getSubCode())) {
            throw exception(ACCOUNT_PAY_CODE_NOT_ALLOW);
        }
        PayCheckOutAccountReqVO.OrderTogether checked = CollectionUtils.findFirst(r.getOrderTogethers(), o -> BooleanEnum.TRUE.getValue().equals(o.getIsRecord()));
        if (Objects.isNull(checked)) {
            throw exception(ACCOUNT_NOT_CHECKED_EXISTS);
        }
        // 验证选中的入账账号对应的客人是否已经退房
        // 如果是团队主账，那么需要验证团队主账是否已经退房
        if (Objects.equals(BooleanEnum.TRUE.getValue(), checked.getIsTeam())) {
            TeamDO team = teamService.getTeam(checked.getTogetherCode());
            if (Objects.equals(AccountStatusEnum.CLOSED.getCode(), team.getAccState())) {
                throw exception(ACCOUNT_TEAM_CLOSED);
            }
        } else {
            OrderTogetherDO orderTogetherDO = orderTogetherService.getOrderTogether(checked.getTogetherCode());
            if (Objects.equals(OrderStateEnum.CHECK_OUT.getCode(), orderTogetherDO.getState())) {
                throw exception(ACCOUNT_ORDER_CHECK_OUT);
            }
        }
        return checked;
    }


    /**
     * 确认生成房费操作的验证
     *
     * @param reqVO 入参
     * @return 可加收房费的客单列表
     */
    private List<OrderTogetherDO> validateConfirmRoomFee(ConfirmRoomFeeReqVO reqVO) {
        List<String> togetherCodes = CollectionUtils.convertList(reqVO.getOrderTogethers(), ConfirmRoomFeeReqVO.OrderTogether::getTogetherCode);
        // 只对在住状态的客单进行房费加收, 挂账订单、团队主账不再加收
        List<OrderTogetherDO> orderTogetherDOList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setStates(List.of(OrderStateEnum.CHECK_IN.getCode()))
                .setTogetherCodes(togetherCodes));
        if (CollUtil.isEmpty(orderTogetherDOList)) {
            throw exception(CHECKED_ORDER_CHECKOUT);
        }
        return orderTogetherDOList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.accNo"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    public void confirmPreAuth(ConfirmPreAuthReqVO reqVO) {
        // 验证
        AccountDO preAuthAccount = validateConfirmPreAuth(reqVO, true);
        LocalDate bizDate = generalConfigService.getBizDate(preAuthAccount.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(preAuthAccount.getHcode());
        // 修改预授权状态
        AccountDO updateAccount = AccountDO.builder().id(preAuthAccount.getId()).state(AccountStatusEnum.CLOSED.getCode()).handleShiftNo(shiftNo).build();
        // 添加实收金额账务
        AccountDO confirmAccount = BeanUtils.toBean(preAuthAccount, AccountDO.class);
        confirmAccount.setId(null)
                .setAccNo(IdUtil.getSnowflakeNextIdStr())
                .setFee(reqVO.getFee())
                .setAfterTaxFee(reqVO.getFee())
                .setShiftNo(shiftNo)
                .setRecorder(SecurityFrameworkUtils.getLoginUserName())
                .setBizDate(bizDate)
                .setState(AccountStatusEnum.UNCLOSED.getCode())
                .setIsPreAuthAffirm(BooleanEnum.TRUE.getValue())
                .setPreAuthAccNo(preAuthAccount.getAccNo());
        String accDetail = "";
        // 银行预授权
        if (Objects.equals(preAuthAccount.getSubCode(), PayAccountEnum.BANK_PRE_AUTH.getCode())) {
            confirmAccount.setSubCode(PayAccountEnum.BANK_CARD.getCode()).setIsCanRev(BooleanEnum.TRUE.getValue());
            List<DictDataRespDTO> dictDataList = dictDataApi.getDictDataList(DictTypeEnum.DICT_TYPE_BANK_TYPE.getCode()).getData();
            Map<String, String> bankMap = CollectionUtils.convertMap(dictDataList, DictDataRespDTO::getCode, DictDataRespDTO::getLabel);
            accDetail = String.format("银行:%s 卡号:%s 授权码:%s", bankMap.getOrDefault(preAuthAccount.getBankType(), ""),
                    StrUtil.isNotBlank(preAuthAccount.getPayCode()) ? preAuthAccount.getPayCode() : "", preAuthAccount.getPayCode());
        }
        // 扫码预授权
        if (Objects.equals(preAuthAccount.getSubCode(), PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode())) {
            confirmAccount.setSubCode(validateAndConvertScanGunSubCode(preAuthAccount.getPayCode()));
            String finishOrderNo = preFinish(new PreFinishReqDTO().setPayOrderNo(preAuthAccount.getOutOrderNo())
                    .setHcode(preAuthAccount.getHcode())
                    .setPayPrice(preAuthAccount.getFee())
                    .setPlatform(PlatFormEnum.FUIOU.getCode())
                    .setFinishPrice(reqVO.getFee()));
            confirmAccount.setPreAuthFinishOrderNo(finishOrderNo);
            if (Objects.equals(confirmAccount.getSubCode(), PayAccountEnum.SCAN_GUN_WX.getCode())) {
                // 确认金额后返回的流水号
                accDetail = String.format("微信支付交易流水号：%s", finishOrderNo);
            }
            if (Objects.equals(confirmAccount.getSubCode(), PayAccountEnum.SCAN_GUN_ALIPAY.getCode())) {
                accDetail = String.format("支付宝支付交易流水号：%s", finishOrderNo);
            }
        }
        confirmAccount.setAccDetail(accDetail);
        accountMapper.updateById(updateAccount);
        accountMapper.insert(confirmAccount);
    }

    private String preFinish(PreFinishReqDTO reqDTO) {
        CommonResult<PreFinishRespDTO> preFinishRespDTO = payBaseApi.preFinish(reqDTO);
        if (!preFinishRespDTO.isSuccess()) {
            throw exception(PRE_FINISH_ERROR, preFinishRespDTO.getMsg());
        }
        return preFinishRespDTO.getData().getFinishOrderNo();
    }

    @Override
    public void cancelPreAuth(String accNo) {
        AccountDO account = validateConfirmPreAuth(new ConfirmPreAuthReqVO().setAccNo(accNo), false);
        LocalDate bizDate = generalConfigService.getBizDate(account.getHcode());
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(account.getHcode());
        // 如果是支付宝、微信预授权，需要调用支付接口取消
        if (Objects.equals(account.getSubCode(), PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode())) {
            CommonResult<PreCancelRespDTO> preCancelRespDTO = payBaseApi.preCancel(new PreCancelReqDTO().setHcode(account.getHcode())
                    .setPayPrice(account.getFee())
                    .setPayOrderNo(account.getOutOrderNo())
                    .setPlatform(PlatFormEnum.FUIOU.getCode()));
            if (!preCancelRespDTO.isSuccess()) {
                throw exception(PRE_CANCEL_ERROR, preCancelRespDTO.getMsg());
            }
        }
        accountMapper.updateById(AccountDO.builder()
                .id(account.getId())
                .state(AccountStatusEnum.CANCEL.getCode())
                .payNo(IdUtil.getSnowflakeNextIdStr())
                .payShiftNo(shiftNo)
                .payBizDate(bizDate)
                .payTime(LocalDateTime.now())
                .payer(SecurityFrameworkUtils.getLoginUserName())
                .handleShiftNo(shiftNo).build());
    }

    /**
     * 确认预授权操作时的验证
     *
     * @param reqVO 入参
     * @return 预授权账户
     */
    private AccountDO validateConfirmPreAuth(ConfirmPreAuthReqVO reqVO, Boolean isValidateFee) {
        AccountDO account = accountMapper.selectOne(AccountDO::getAccNo, reqVO.getAccNo());
        if (account == null) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        if (!Objects.equals(account.getState(), AccountStatusEnum.UNCLOSED.getCode())) {
            throw exception(ACCOUNT_NOT_UNCLOSED);
        }
        if (!List.of(PayAccountEnum.BANK_PRE_AUTH.getCode(), PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()).contains(account.getSubCode())) {
            throw exception(ACCOUNT_NOT_PRE_AUTH);
        }
        if (isValidateFee && account.getFee() < reqVO.getFee()) {
            throw exception(ACCOUNT_PRE_AUTH_FEE_ERROR);
        }
        return account;
    }


    @Override
    @Lock4j(keys = {"#reqVO.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    public List<ConfirmCheckOutAccountRespVO> getConfirmRoomFeeList(ConfirmCheckOutAccountReqVO reqVO) {
        List<ConfirmCheckOutAccountRespVO> confirmList = CollUtil.newArrayList();

        // 1. 验证订单
        List<OrderTogetherDO> orderTogetherList = validateConfirmRoomFee(reqVO);
        if (CollUtil.isEmpty(orderTogetherList)) {
            return confirmList;
        }

        // 过滤出主客单
        orderTogetherList = CollectionUtils.filterList(orderTogetherList, o -> Objects.equals(o.getIsMain(), BooleanEnum.TRUE.getValue()));
        if (CollUtil.isEmpty(orderTogetherList)) {
            return confirmList;
        }

        Set<String> orderNos = CollectionUtils.convertSet(orderTogetherList, OrderTogetherDO::getOrderNo);

        // 2. 获取订单和价格数据
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNos.stream().toList()));
        Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);

        // 2.1 获取每日订单价格
        List<OrderPriceDO> orderPriceList = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNos(orderNos.stream().toList()));
        Map<String, List<OrderPriceDO>> orderPriceMap = CollectionUtils.convertMultiMap(orderPriceList, OrderPriceDO::getOrderNo);

        LocalDateTime now = LocalDateTime.now();

        // 获取超时分钟数的配置
        GeneralConfigDO howMinute = generalConfigService.getGeneralConfig(new GeneralConfigReq2VO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setCode(GeneralConfigTypeEnum.HOW_MINUTE.getCode())
                .setType(GeneralConfigTypeEnum.HOW_MINUTE.getCode()));

        // 获取营业日期
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());

        long overtimeMinutes = Long.parseLong(howMinute.getValue());

        // 获得房费科目
        List<DictDataRespDTO> dictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT_ROOM_FEE.getCode()).getData();
        List<String> roomFeeSubCodes = CollectionUtils.convertList(dictData, DictDataRespDTO::getCode);
        // 获取需要退房账号在营业日当天是否计算了房费
        List<AccountDO> accountList = accountMapper.selectList(new AccountListReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setSubCodes(roomFeeSubCodes));
        // 过滤出reqVO.getOrderTogethers()中包含客单代码的账务
        accountList = CollectionUtils.filterList(accountList, a ->
                reqVO.getOrderTogethers().stream()
                        .anyMatch(orderTogether -> orderTogether.getTogetherCode().equals(a.getTogetherCode()))
        );
        // 转换为map
        Map<String, List<AccountDO>> accountMap = CollectionUtils.convertMultiMap(accountList, AccountDO::getTogetherCode);
        // 过滤出当前营业日的map
        Map<String, List<AccountDO>> bizDateAccountMap = accountMap.entrySet().stream()
                .filter(entry -> entry.getValue().stream().anyMatch(accountDO -> accountDO.getBizDate().isEqual(bizDate.minusDays(1l))))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 3. 处理每个主订单，计算费用
        for (OrderTogetherDO orderTogether : orderTogetherList) {
            OrderDO order = orderMap.get(orderTogether.getOrderNo());
            if (order == null) {
                throw exception(ORDER_NOT_EXISTS);
            }
            List<AccountDO> accountDOList = accountMap.getOrDefault(orderTogether.getTogetherCode(), new ArrayList<>());
            // 计算房费之和
            long roomFee = accountDOList.stream().mapToLong(AccountDO::getFee).sum();

            ConfirmCheckOutAccountRespVO confirm = BeanUtils.toBean(orderTogether, ConfirmCheckOutAccountRespVO.class)
                    .setCheckinType(order.getCheckinType())
                    .setFee(0L)
                    .setNo(order.getOrderNo())
                    .setName(orderTogether.getName())
                    .setRoomFee(roomFee)
                    .setCheckoutTime(orderTogether.getPlanCheckoutTime());

            List<OrderPriceDO> orderPriceDOList = orderPriceMap.get(orderTogether.getOrderNo());

            CheckInTypeEnum checkInType = CheckInTypeEnum.getCheckInTypeEnumByCode(order.getCheckinType());

            switch (checkInType) {
                case HOUR_ROOM:
                    confirm.setFee(calculateHourRoomFee(reqVO, orderTogether, order, orderPriceDOList, overtimeMinutes, now));
                    break;
                case FREE, SELF_USE:
                    confirm.setFee(0L);
                    break;
                case ALL_DAY, LONG_STAY, TRAVEL_GROUP, MEETING_GROUP:
                    PriceAllDayRuleRespVO priceAllDayRule = priceAllDayRuleService.getPriceAllDayRule(new PriceAllDayRuleReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
                    confirm.setFee(calculateAllDayRoomFee(reqVO, orderTogether, priceAllDayRule, order, orderPriceDOList, bizDateAccountMap, overtimeMinutes, now, bizDate));
                    break;
                default:
                    throw exception(ORDER_CHECKIN_TYPE_ERROR);
            }

            confirmList.add(confirm);
        }

        return confirmList;
    }


    /**
     * 验证确认房费, 验证订单状态,当前是团队结账：如果选中了所有的主客订单，且有团队主账未结，则不能确认<br>
     * 挂账订单、团队主账、非主客单不需要确认房费
     *
     * @param reqVO 入参
     * @return 需要确认的客单
     */
    private List<OrderTogetherDO> validateConfirmRoomFee(ConfirmCheckOutAccountReqVO reqVO) {
        // 获取非团队主账
        List<ConfirmCheckOutAccountReqVO.OrderTogether> orderTogethers = CollectionUtils.filterList(reqVO.getOrderTogethers(),
                o -> Objects.equals(o.getIsTeam(), BooleanEnum.FALSE.getValue()));
        List<String> togetherCodes = CollectionUtils.convertList(orderTogethers, ConfirmCheckOutAccountReqVO.OrderTogether::getTogetherCode);
        // 必须选中客单才能确认房费操作,不能单独选中团队主单
        if (CollUtil.isEmpty(togetherCodes)) {
            throw exception(ORDER_TOGETHER_NO_EXIST);
        }
        // 如果没有主客单，那么直接返回null，表示不需要计算房费 并且所有主客单都不能为入住状态
        List<ConfirmCheckOutAccountReqVO.OrderTogether> mainOrderList = CollectionUtils.filterList(orderTogethers, o -> BooleanEnum.TRUE.getValue().equals(o.getIsMain()));
        if (mainOrderList.isEmpty() || mainOrderList.stream().noneMatch(o -> Objects.equals(o.getState(), OrderStateEnum.CHECK_IN.getCode()))) {
            return null;
        }
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setStates(List.of(OrderStateEnum.CHECK_IN.getCode()))
                .setTogetherCodes(togetherCodes));
        if (CollUtil.isEmpty(orderTogetherList)) {
            throw exception(ORDER_NOT_EXISTS);
        }
        // 是否需要验证团队主账
        String teamCode = null;
        // 如果 "团队主账" 中有未结账务，那么最后一间房不能退房，但可以挂账
        if (Objects.equals(reqVO.getIsCreditCheckout(), BooleanEnum.FALSE.getValue()) && Objects.equals(OrderTypeEnum.GROUP.getCode(), reqVO.getOrderType())) {
            // 获取团队主账
            List<ConfirmCheckOutAccountReqVO.OrderTogether> teamMainOrders = CollectionUtils.filterList(reqVO.getOrderTogethers(),
                    o -> Objects.equals(o.getIsTeam(), BooleanEnum.TRUE.getValue()));
            // 结算未带团队主账，则无需验证团队中是否为最后一间退房
            if (CollUtil.isEmpty(teamMainOrders)) {
                OrderDO order = orderService.getOrderByOrderNo(reqVO.getOrderTogethers().getFirst().getNo());
                teamCode = order.getTeamCode();
                // 验证团队主账未结账务
                List<AccountDO> teamMainAccountList = accountMapper.selectList(new AccountListReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                        .setState(AccountStatusEnum.UNCLOSED.getCode())
                        .setTogetherCode(teamCode)
                        .setNo(teamCode));
                if (CollUtil.isNotEmpty(teamMainAccountList)) {
                    // 获取所有在住和挂账的团队订单
                    List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(reqVO.getGcode())
                            .setHcode(reqVO.getHcode())
                            .setTeamCode(teamCode)
                            .setStates(List.of(OrderStateEnum.CHECK_IN.getCode(), OrderStateEnum.CREDIT.getCode())));
                    List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
                    // 结算中的主客订单号
                    //List<ConfirmCheckOutAccountReqVO.OrderTogether> mainOrderList = CollectionUtils.filterList(orderTogethers, o -> BooleanEnum.TRUE.getValue().equals(o.getIsMain()));
                    List<String> checkOrderNoList = CollectionUtils.convertList(mainOrderList, ConfirmCheckOutAccountReqVO.OrderTogether::getNo);
                    if (new HashSet<>(checkOrderNoList).containsAll(orderNoList)) {
                        throw exception(ORDER_TEAM_MAIN_ACCOUNT_NOT_CLOSED);
                    }
                }
            }
        }
        return orderTogetherList;
    }

    /**
     * 计算全天房费<br>
     * 1.当天住当天退，按照超时计费规则计算房费<br>
     * 2.未超时退房，判断是否夜审产生房费，没有则计算房费<br>
     * 3.超时退房，判断是否夜审产生房费，没有则计算房费，同时计算超时费用<br>
     * <p>
     * 起步费适用与当天住当天退，未超时退房
     * 超时收费分按半日租、按全日租、按小时加收
     *
     * @param orderTogether    客单
     * @param priceAllDayRule  全天房计费规则
     * @param order            订单
     * @param orderPriceDOList 订单当日房价列表
     * @param accountMap       客单营业日当天的房费账务
     * @param howMinute        超过多少分钟算1小时
     * @param now              系统时间
     * @param bizDate          业务日期
     * @return 全天房费
     */
    private Long calculateAllDayRoomFee(ConfirmCheckOutAccountReqVO reqVO, OrderTogetherDO orderTogether,
                                        PriceAllDayRuleRespVO priceAllDayRule, OrderDO order, List<OrderPriceDO> orderPriceDOList, Map<String, List<AccountDO>> accountMap,
                                        Long howMinute, LocalDateTime now, LocalDate bizDate) {
        LocalDateTime planCheckoutTime = orderTogether.getPlanCheckoutTime();
        LocalDateTime checkInTime = orderTogether.getCheckinTime();

        // 入住分钟数
        long checkInMinutes = LocalDateTimeUtil.between(checkInTime, now, ChronoUnit.MINUTES);

        // 获取房型计费规则
        Map<String, RtFeeRule> rtFeeRuleMap = CollectionUtils.convertMap(priceAllDayRule.getRtFeeRule(), RtFeeRule::getRtCode);

        // 获取营业日当天的房价
        OrderPriceDO orderPrice = CollectionUtils.findFirst(orderPriceDOList, o -> o.getPriceDate().isEqual(bizDate));

        OrderPriceDO orderPriceToday = orderPrice == null ? orderPriceDOList.getLast() : orderPrice;


        // 当天住当天退的全天房（提前退房）
        if (checkInTime.toLocalDate().isEqual(bizDate)) {
            if (accountMap.get(orderTogether.getTogetherCode()) == null) {
                return calculateFeeBySubCodeOrRule(reqVO.getSubCode(), orderPriceToday,
                        () -> calculateAllDayFeeBeforeCheckout(checkInMinutes, priceAllDayRule, order, orderPriceToday, rtFeeRuleMap));
            }
        }
        // 隔夜退房
        // 预离的时间
        LocalTime planOutTime = planCheckoutTime.toLocalTime();
        // 当前的时间
        LocalTime nowTime = now.toLocalTime();
        long exceedMinutes = Duration.between(planOutTime, nowTime).toMinutes();
        long hours = calculateHours(exceedMinutes, howMinute);
        return calculateFeeBySubCodeOrRule(reqVO.getSubCode(), orderPriceToday,
                () -> calculateAllDayFeeAfterCheckout(hours, exceedMinutes, priceAllDayRule, order, orderPriceToday, rtFeeRuleMap, orderTogether, accountMap));
    }

    /**
     * 根据房费类型或规则生成费用
     *
     * @param subCode        生成房费类型(房费科目代码)
     * @param orderPrice     营业日当天订单价格
     * @param ruleCalculator 规则计费
     * @return 房费
     */
    private Long calculateFeeBySubCodeOrRule(String subCode, OrderPriceDO orderPrice, Supplier<Long> ruleCalculator) {
        Long fee = generateFeeByType(subCode, orderPrice);
        return fee != null ? fee : ruleCalculator.get();
    }

    /**
     * 计算全天房当天住当天离的提前退房房费
     * <p>
     * 起步计费、全价计费
     *
     * @param checkInMinutes  入住分钟数
     * @param priceAllDayRule 全天房计费规则
     * @param order           订单
     * @param orderPrice      每日订单价格
     * @param rtFeeRuleMap    房型计费规则
     * @return 全天房当天住当天离的提前退房房费
     */
    private Long calculateAllDayFeeBeforeCheckout(long checkInMinutes, PriceAllDayRuleRespVO priceAllDayRule, OrderDO order,
                                                  OrderPriceDO orderPrice, Map<String, RtFeeRule> rtFeeRuleMap) {
        if (checkInMinutes > priceAllDayRule.getAllPriceNMin()) {
            return orderPrice.getVipPrice();
        } else if (checkInMinutes > priceAllDayRule.getStartPriceNMin()) {
            RtFeeRule rtFeeRule = rtFeeRuleMap.getOrDefault(order.getRtCode(), null);
            if (rtFeeRule != null) {
                return rtFeeRule.getStartPrice();
            }
        }
        return 0L;
    }

    /**
     * 计算全天房超时加收房费
     * 需要判断是否已经生成了房费
     *
     * @param hours         超时小时数
     * @param exceedMinutes 超时分钟数
     * @param order         订单
     * @param orderPrice    订单价格
     * @param rtFeeRuleMap  房型计费规则
     * @param orderTogether 客单
     * @param accountMap    客单营业日当天的房费账务
     * @return 全天房超时加收房费
     */
    private Long calculateAllDayFeeAfterCheckout(long hours, long exceedMinutes, PriceAllDayRuleRespVO priceAllDayRule,
                                                 OrderDO order, OrderPriceDO orderPrice, Map<String, RtFeeRule> rtFeeRuleMap,
                                                 OrderTogetherDO orderTogether, Map<String, List<AccountDO>> accountMap) {
        Long fee = 0L;
        // 如果accountMap不存在客单的房费账务，则返回orderPrice.getVipPrice()
       /* if (accountMap.get(orderTogether.getTogetherCode()) == null) {
            fee = orderPrice.getVipPrice();
        }*/
        // 没有超过退房时间N分钟，不收加收费
        if (exceedMinutes <= priceAllDayRule.getOverNMinCollect()) {
            return fee;
        }
        // 按半日租加收模式
        if (Objects.equals(NumberEnum.ZERO.getNumber(), priceAllDayRule.getOverCollectStyle())) {
            // 如果超过了全天房的超时分钟数，则返回orderPrice.getVipPrice()
            if (exceedMinutes > priceAllDayRule.getOverNMinAllDay()) {
                fee += orderPrice.getVipPrice();
            }
            if (exceedMinutes > priceAllDayRule.getOverNMinCollect() && exceedMinutes <= priceAllDayRule.getOverNMinAllDay()) {
                fee += orderPrice.getVipPrice() / 2;
            }
            return fee;
        }
        // 按小时加收,根据超时计费规则计算加收房费
        if (Objects.equals(NumberEnum.ONE.getNumber(), priceAllDayRule.getOverCollectStyle())) {
            if (hours == 0) {
                return fee;
            }
            RtFeeRule rtFeeRule = rtFeeRuleMap.getOrDefault(order.getRtCode(), null);
            if (rtFeeRule != null) {
                String guestSrcType = order.getGuestSrcType();
                return switch (GuestSrcTypeEnum.getGuestSrcTypeEnumByCode(guestSrcType)) {
                    case WALK_IN -> fee + rtFeeRule.getWalkInAddPrice() * hours;
                    case MEMBER -> fee + rtFeeRule.getMemberAddPrice() * hours;
                    case AGENT -> fee + rtFeeRule.getAgentAddPrice() * hours;
                    case PROTOCOL -> fee + rtFeeRule.getProtocolAddPrice() * hours;
                    default -> fee;
                };
            }
        }

        return fee;
    }

    /**
     * 计算时租房房费<br>
     * 时租房按照真实入住时长来计费<br>
     *
     * @param orderTogether    宾客入住信息
     * @param reqVO            入参
     * @param order            订单
     * @param orderPriceDOList 每日订单价格列表
     * @param howMinute        超时多少分钟算1小时
     * @param now              当前系统时间
     * @return 小时房费
     */
    private Long calculateHourRoomFee(ConfirmCheckOutAccountReqVO reqVO,
                                      OrderTogetherDO orderTogether,
                                      OrderDO order,
                                      List<OrderPriceDO> orderPriceDOList,
                                      Long howMinute,
                                      LocalDateTime now) {
        LocalDateTime planCheckoutTime = orderTogether.getPlanCheckoutTime();
        LocalDateTime checkInTime = orderTogether.getCheckinTime();
        // 入住分钟数
        long checkInMinutes = LocalDateTimeUtil.between(checkInTime, now, ChronoUnit.MINUTES);
        // 钟点房房价就一条记录
        OrderPriceDO orderPrice = orderPriceDOList.isEmpty() ? null : orderPriceDOList.getLast();
        if (orderPrice == null) {
            throw exception(ORDER_PRICE_IS_NULL);
        }
        // 根据生成房费类型来返回不同的房费
        Long fee = generateFeeByType(reqVO.getSubCode(), orderPrice);
        if (fee != null) {
            return fee;
        }
        // 钟点房计费规则
        PriceHourRuleRespVO priceHourRule = priceHourRuleService.getPriceHourRule(new PriceHourRuleReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()), true);
        Map<String, List<RtFeeRule>> rtFeeRuleMap = CollectionUtils.convertMap(priceHourRule.getRtFeeRule(), HourRtFeeRule::getHourCode, HourRtFeeRule::getHourRules);

        // 时租房时长(单位:分钟)
        long hourMinuteNum = HourRoomEnum.getHourNumByCode(order.getHourCode()) * 60;
        // 入住分钟数 - 时租房时长 = 超时分钟数 正数表示超时，负数表示未超时
        long overMinutes = checkInMinutes - hourMinuteNum;

        // 提前退房
        if (overMinutes < 0) {
            return calculateHourRoomFeeBeforeCheckout(checkInMinutes, priceHourRule, order, orderPrice, rtFeeRuleMap);
        } else {
            // 超时退房
            long exceedMinute = LocalDateTimeUtil.between(planCheckoutTime, now, ChronoUnit.MINUTES);
            long hours = calculateHours(exceedMinute, howMinute);
            return calculateFeeAfterCheckout(hours, order, orderPrice, rtFeeRuleMap);
        }
    }

    /**
     * 根据生成房费类型来返回不同的房费
     *
     * @param generateFeeType 生成房费类型
     * @param orderPrice      生成房费参考的订单房价
     * @return 生成的房费，如果是系统自动计费返回null
     */
    private Long generateFeeByType(String generateFeeType, OrderPriceDO orderPrice) {
        if (orderPrice == null) {
            return 0L;
        }
        return switch (GenerateFeeTypeEnum.getGenerateFeeTypeEnumByCode(generateFeeType)) {
            case ALL_DAY -> // 加收全天
                    orderPrice.getVipPrice();
            case HALF_DAY -> // 加收半天
                    orderPrice.getVipPrice() / 2;
            case HAND -> // 手工房费 不加收房费
                    null;
            case NO_ADD -> // 手工房费 不加收房费
                    0L;
            case RULE -> // 系统自动计费
                    null;
            default -> throw exception(GENERALTE_FEE_TYPE_NOT_EXISTS);
        };
    }

    /**
     * 计算钟点房提前退房收费
     *
     * @param checkInMinutes 入住分钟数
     * @param priceHourRule  计费规则
     * @param order          订单
     * @param orderPrice     订单价格
     * @param rtFeeRuleMap   房型计费规则
     * @return 钟点房费
     */
    private Long calculateHourRoomFeeBeforeCheckout(long checkInMinutes, PriceHourRuleRespVO priceHourRule,
                                                    OrderDO order, OrderPriceDO orderPrice, Map<String, List<RtFeeRule>> rtFeeRuleMap) {
        if (checkInMinutes > priceHourRule.getAllPriceNMin()) {
            return orderPrice.getVipPrice();
        } else if (checkInMinutes > priceHourRule.getStartPriceNMin()) {
            List<RtFeeRule> ruleList = rtFeeRuleMap.getOrDefault(order.getHourCode(), Collections.emptyList());
            RtFeeRule rtFeeRule = ruleList.stream()
                    .filter(rule -> rule.getRtCode().equals(order.getRtCode()))
                    .findFirst()
                    .orElse(null);
            return (rtFeeRule != null) ? rtFeeRule.getStartPrice() : 0L;
        }
        return 0L;
    }

    /**
     * 计算超时退房收费
     *
     * @param hours        超时小时数
     * @param order        订单
     * @param orderPrice   订单价格
     * @param rtFeeRuleMap 房型计费规则
     * @return 加收房费(钟点房是返回房费)
     */
    private Long calculateFeeAfterCheckout(long hours, OrderDO order, OrderPriceDO orderPrice, Map<String, List<RtFeeRule>> rtFeeRuleMap) {
        if (hours == 0) {
            return orderPrice.getVipPrice();
        }
        List<RtFeeRule> ruleList = rtFeeRuleMap.getOrDefault(order.getHourCode(), Collections.emptyList());
        RtFeeRule rtFeeRule = ruleList.stream()
                .filter(rule -> rule.getRtCode().equals(order.getRtCode()))
                .findFirst()
                .orElse(null);
        if (rtFeeRule != null) {
            String guestSrcType = order.getGuestSrcType();
            return switch (GuestSrcTypeEnum.getGuestSrcTypeEnumByCode(guestSrcType)) {
                case WALK_IN -> orderPrice.getVipPrice() + rtFeeRule.getWalkInAddPrice() * hours;
                case MEMBER -> orderPrice.getVipPrice() + rtFeeRule.getMemberAddPrice() * hours;
                case AGENT -> orderPrice.getVipPrice() + rtFeeRule.getAgentAddPrice() * hours;
                case PROTOCOL -> orderPrice.getVipPrice() + rtFeeRule.getProtocolAddPrice() * hours;
                default -> orderPrice.getVipPrice();
            };
        }
        return orderPrice.getVipPrice();
    }

    /**
     * 计算小时数
     *
     * @param stayDurationInMinutes 超时分钟数
     * @param howMinute             超过多少分钟算1小时
     * @return 小时数
     */
    private long calculateHours(long stayDurationInMinutes, long howMinute) {
        // 如果入住时间为0或负数，则返回0小时
        if (stayDurationInMinutes <= 0) {
            return 0;
        }
        // 计算完整的小时数
        long fullHours = stayDurationInMinutes / MINUTE_TO_HOUR_CONVERSION_FACTOR;
        // 计算剩余的分钟数
        long remainingMinutes = stayDurationInMinutes % MINUTE_TO_HOUR_CONVERSION_FACTOR;
        // 如果剩余分钟数超过howMinute，则增加一个小时
        return (remainingMinutes > howMinute) ? fullHours + 1 : fullHours;
    }


    /**
     * 批量插入账务
     */
    @Override
    public Boolean createAccounts(List<AccountSaveReqVO> createAccountList) {
        List<AccountDO> bean = BeanUtils.toBean(createAccountList, AccountDO.class);
        return accountMapper.insertBatch(bean);
    }

    @Override
    public void createAccountDOList(List<AccountDO> accountDOList) {
        if (CollUtil.isEmpty(accountDOList)) {
            return;
        }
        accountMapper.insertBatch(accountDOList);
    }

    @Override
    public void updateAccountByIds(List<AccountDO> accountDOList) {
        if (CollUtil.isEmpty(accountDOList)) {
            return;
        }
        accountMapper.updateBatch(accountDOList);
    }

    @Override
    public void revocation(String accNo, String isVerify) {
        // 校验存在
        AccountDO accountDO = accountMapper.selectOne(AccountDO::getAccNo, accNo);
        if (accountDO == null) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        AccountDO newAccountDO = new AccountDO().setId(accountDO.getId()).setIsVerify(isVerify);
        accountMapper.updateById(newAccountDO);
    }

    @Override
    public OrderAccountStatBO getOrderAccountStatByTogetherCode(String togetherCode) {
        return statOrderAccountStatByNo(List.of(togetherCode), NumberEnum.ONE.getNumberInt());
    }


    @Override
    public OrderAccountStatBO getOrderAccountStatByNo(String gcode, String hcode, List<String> noList, String noType) {
        if (CollUtil.isEmpty(noList)) {
            return new OrderAccountStatBO();
        }

        // 使用 Map 来简化 noType 的判断逻辑
        Map<String, Integer> noTypeMap = Map.of(
                NoTypeEnum.ORDER.getCode(), NumberEnum.ONE.getNumberInt(),
                NoTypeEnum.TEAM_RECEPTION.getCode(), NumberEnum.ZERO.getNumberInt(),
                NoTypeEnum.TEAM_MAIN.getCode(), NumberEnum.ZERO.getNumberInt(),
                NoTypeEnum.ROOM.getCode(), NumberEnum.ZERO.getNumberInt(),
                NoTypeEnum.ORDER_LIST.getCode(), NumberEnum.ZERO.getNumberInt(),
                NoTypeEnum.BOOK.getCode(), NumberEnum.ZERO.getNumberInt(),
                NoTypeEnum.TEAM.getCode(), NumberEnum.ZERO.getNumberInt()
        );

        if (noTypeMap.containsKey(noType)) {
            if (NoTypeEnum.TEAM_RECEPTION.getCode().equals(noType)) {
                String teamCode = noList.getFirst();
                List<OrderDO> orderDOS = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setTeamCode(teamCode));
                List<String> nos = CollectionUtils.convertList(orderDOS, OrderDO::getOrderNo);
                nos.addAll(noList);
                return statOrderAccountStatByNo(nos, NumberEnum.ZERO.getNumberInt());
            } else {
                return statOrderAccountStatByNo(noList, noTypeMap.get(noType));
            }
        }
        return new OrderAccountStatBO();
    }


    @Override
    public OrderAccountStatBO getOrderAccountStatByNo(List<String> noList) {
        return statOrderAccountStatByNo(noList, NumberEnum.ZERO.getNumberInt());
    }

    /**
     * 统计账务
     *
     * @param noList 账号(入账账号)或宾客代码 列表
     * @param type   0：账务号 1：宾客代码
     * @return 统计结果 OrderAccountStatBO
     */
    private OrderAccountStatBO statOrderAccountStatByNo(List<String> noList, int type) {
        List<AccountDO> accounts = CollUtil.newArrayList();
        if (type == NumberEnum.ZERO.getNumberInt()) {
            accounts = accountMapper.selectList(AccountDO::getNo, noList);

        } else {
            accounts = accountMapper.selectList(AccountDO::getTogetherCode, noList);
        }

        // 获取付款科目的账务
        List<AccountDO> payAccounts = CollectionUtils.filterList(accounts, account -> DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(account.getSubType()));
        // 获取消费科目的账务
        List<AccountDO> consumeAccounts = CollectionUtils.filterList(accounts, account -> DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(account.getSubType()));
        // 消费：订单账号下的所有消费科目求和
        Long consume = consumeAccounts.stream().map(AccountDO::getFee).reduce(0L, Long::sum);
        // 预授权：微信和支付宝预授权 + 银行卡预授权
        /*Long preAuth = payAccounts.stream().filter(account -> AccountStatusEnum.UNCLOSED.getCode().equals(account.getState()) && (PayAccountEnum.BANK_PRE_AUTH.getCode().equals(account.getSubCode())
                || PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode().equals(account.getSubCode()))).map(AccountDO::getFee).reduce(0L, Long::sum);*/
        //预售款集合
        List<AccountDO> preAuthList = CollectionUtils.filterList(payAccounts, account -> PayAccountEnum.BANK_PRE_AUTH.getCode().equals(account.getSubCode())
                || PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode().equals(account.getSubCode()));
        //未结预售款集合
        List<AccountDO> preAuthOpenList = CollectionUtils.filterList(preAuthList, account -> AccountStatusEnum.UNCLOSED.getCode().equals(account.getState()));
        Long preAuth = preAuthList.stream().map(account -> account.getFee()).reduce(0L, Long::sum);
        Long preAuthOpen = preAuthOpenList.stream().map(account -> account.getFee()).reduce(0L, Long::sum);
        // 优惠：优惠券金额求和
        Long coupon = payAccounts.stream().filter(account -> PayAccountEnum.COUPON.getCode().equals(account.getSubCode()))
                .map(AccountDO::getFee).reduce(0L, Long::sum);
        // 付款：该订单房间账务表所有付款科目求和，不包含预授权和优惠券 付款= 所有付款科目求和 - 预授权 - 优惠券
        Long fee = payAccounts.stream().map(AccountDO::getFee).reduce(0L, Long::sum) - preAuth - coupon;
        // 余额= 全部付款金额+预授权+优惠 - 消费金额
        Long balance = fee + preAuthOpen + coupon - consume;
        return new OrderAccountStatBO().setBalanceAmount(balance).setPayAmount(fee).setNoList(noList).setCouponAmount(coupon).setConsumeAmount(consume).setPreAuthAmount(preAuthOpen);
    }


    @Override
    public List<AccountStatRespVO> getOrderAccountStatByNos(AccountStatReqVO reqVO) {
        return accountMapper.statTogetherAccountByNoList(reqVO);
    }

    @Override
    public List<AccountDO> getAccountList(AccountListReqVO reqVO) {
        return accountMapper.selectList(reqVO);
    }

    @Override
    public List<AccountDO> getSettleAccountList(SettleAccountListReqVO reqVO) {
        return accountMapper.selectSettleList(reqVO);
    }

    @Override
    public PageResult<AccountDO> getCashBillAccountPage(AccRecordPageReqVO reqVO) {
        return accountMapper.selectCashBillPage(reqVO);
    }

    @Override
    public List<AccountDO> getCashBillAccountList(AccRecordReqVO reqVO) {
        return accountMapper.selectList(new AccountListReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setNo(reqVO.getCashBillOrderNo()));
    }

    /**
     * 报表获得房间账务列表
     *
     * @param reqVO
     * @return 账务
     */
    @Override
    public List<AccountDO> getAccountListForReport(AccountListReqVO reqVO) {
        return accountMapper.selectListForReport(reqVO);
    }

    @Override
    public List<AccountDO> getPayOrConsumeAccountList(PayOrConsumeDetailReqVO reqVO) {
        return accountMapper.selectPayOrConsumeList(reqVO);
    }

    @Override
    public List<AccountDO> getCheckoutAccountList(PaymentRecoveryDetailReqVO reqVO) {
        return accountMapper.selectPaymentRecoveryList(reqVO);
    }

    @Override
    public List<AccountDO> getConsumeCheckoutAccountList(ConsumeRecoveryDetailReqVO reqVO) {
        return accountMapper.selectConsumeRecoveryList(reqVO);
    }

    /**
     * 应收账获取账务分页
     *
     * @return
     */
    @Override
    public PageResult<AccountDO> getAccountPageByArSet(AccountPageReqVO pageReqVO) {
        if (StrUtil.isBlank(pageReqVO.getArSetCode())) {
            throw exception(AR_SET_CODE_NOT_NULL);
        }
        return accountMapper.selectPageByArSet(pageReqVO);
    }


    @Override
    public List<AccountSubRespVO> getAccountSubList(AccountSubReqVO reqVO) {
        // 根据条件查询出所有账务
        List<AccountDO> accountDOList = accountMapper.selectAccountSubList(reqVO);
        if (CollUtil.isEmpty(accountDOList)) {
            return new ArrayList<>();
        }
        List<DictDataRespDTO> dictList = dictDataApi.getDictDataListByDicTypes(List.of(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(),
                DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())).getData();
        // 将dictList转换为map,key:dictType_code, value: label
        Map<String, String> dictMap = dictList.stream().collect(Collectors.toMap(
                item -> item.getDictType() + "*" + item.getCode(),
                DictDataRespDTO::getLabel,
                (existing, replacement) -> existing // 保留第一个遇到的值
        ));

        // 从accountDOList对subCode,subType进行分组,最终返回List<AccountSubRespVO>
        // 定义分组键
        Function<AccountDO, String> groupingKey = account -> account.getSubType() + "*" + account.getSubCode();

        // 分组
        Map<String, List<AccountDO>> groupedAccounts = accountDOList.stream().collect(Collectors.groupingBy(groupingKey));

        // 转换为 AccountSubRespVO
        List<AccountSubRespVO> accountSubRespVOList = groupedAccounts.entrySet().stream()
                .map(entry -> {
                    String subTypeAndCode = entry.getKey();
                    String subType = subTypeAndCode.split("\\*")[0]; // 使用正则表达式来分割星号
                    String subCode = subTypeAndCode.split("\\*")[1]; // 使用正则表达式来分割星号
                    String subTypeName = dictMap.getOrDefault(subType + "*" + subCode, "");

                    return new AccountSubRespVO()
                            .setSubType(subType)
                            .setSubCode(subCode)
                            .setSubName(subTypeName);
                })
                .collect(Collectors.toList());

        return accountSubRespVOList;
    }


    /**
     * 获得账务分页
     *
     * @param pageReqVO 分页查询
     * @return 账务分页
     */
    @Override
    public PageResult<AccountRespVO> getAccountPageByTogetherCodes(AccountPageReq2VO pageReqVO) {
        if (CollUtil.isEmpty(pageReqVO.getTogetherCodes())) {
            return new PageResult<>();
        }
        PageResult<AccountDO> pageResult = accountMapper.selectPage(pageReqVO);
        return new PageResult<>(buildAccountList(pageResult.getList()), pageResult.getTotal());
    }

    /**
     * 接待： 所有房间账务  no: 订单号列表, noType: orderList
     * 团队接待：所有账务，包括房间和团队的账务  no: teamCode noType: teamReception
     * 团队主单：团队主单账务 no: teamCode noType: teamMain
     * 房间：房间下账务 no: orderNo noType: room
     * 客人：客人账务 no: orderNo noType: order
     *
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<AccountRespVO> getAccountPageByNos(AccountPageReq2VO pageReqVO) {
        if (CollUtil.isEmpty(pageReqVO.getNoList())) {
            return new PageResult<>();
        }
        // 接待： 获取房间下所有账务；团队接待：获取团队主单账务和团队下房间所有账务；团队主单：获取团队主单账务；客人：获取客人账务；预订单：获取预订单账务
        // 如果是团队接待单
        if (NoTypeEnum.TEAM_RECEPTION.getCode().equals(pageReqVO.getNoType())) {
            List<OrderDO> orderDOS = orderService.getOrderList(new OrderReqVO().setGcode(pageReqVO.getGcode()).setHcode(pageReqVO.getHcode()).setTeamCode(pageReqVO.getNoList().getFirst()));
            List<String> orderNoList = CollectionUtils.convertList(orderDOS, OrderDO::getOrderNo);
            pageReqVO.getNoList().addAll(orderNoList);
        }
        PageResult<AccountDO> pageResult = accountMapper.selectPage(pageReqVO);
        return new PageResult<>(buildAccountList(pageResult.getList()), pageResult.getTotal());
    }

    private List<AccountRespVO> buildAccountList(List<AccountDO> accountDOList) {
        List<AccountRespVO> accountList = BeanUtils.toBean(accountDOList, AccountRespVO.class);
        // 获得科目字典
        List<DictDataRespDTO> dictDataRespDTOS = dictDataApi.getDictDataListByDicTypes(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())).getData();
        Map<String, String> dictMap = CollectionUtils.convertMap(dictDataRespDTOS, DictDataRespDTO::getCode, DictDataRespDTO::getLabel);
        accountList.forEach(account -> {
            account.setSubName(dictMap.getOrDefault(account.getSubCode(), ""));
            if (account.getFee() > 0) {
                account.setIsRefundButtonVisible(true);
            } else {
                account.setIsRefundButtonVisible(false);
            }
        });
        return accountList;
    }

    /**
     * 获得房间账务
     */
    @Override
    public AccountDO getAccount(AccountReqVO reqVO) {
        return accountMapper.selectOne(reqVO);
    }

    /**
     * 交班报表
     */
    @Override
    public HandoverReportRespVO handoverReport(HandoverReportReqVO reqVO) {
        // 获取交班模式配置
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null && reqVO.getStartDate().isAfter(reqVO.getEndDate())) {
            throw exception(DATE_ERROR);
        }
        HandoverReportRespVO handoverReportRespVO = new HandoverReportRespVO();
        // 获得房间账务
        List<AccountDO> accountDOList = accountMapper.selectListForHandoverReport(reqVO);
        // 过滤出只有未结的预授权
        // 排除所有预授权类型账务
        List<AccountDO> filteredAccountDOList = accountDOList.stream()
                .filter(account -> !Set.of(
                        PayAccountEnum.BANK_PRE_AUTH.getCode(),
                        PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()
                ).contains(account.getSubCode()))
                .toList();

        List<AccountRespVO> accountRespVOList = BeanUtils.toBean(filteredAccountDOList, AccountRespVO.class);
        // 获取酒店名称
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();

        // 设置参数
        handoverReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setLastSelectTime(LocalDateTime.now())
                .setOperator(reqVO.getOperator());
        if (ObjectUtil.isNotEmpty(reqVO.getShiftNo())) {
            handoverReportRespVO.setShiftNo(String.join(",", reqVO.getShiftNo()));
        }
        if (reqVO.getStartDate() != null && reqVO.getEndDate() != null) {
            handoverReportRespVO
                    .setStartDate(reqVO.getStartDate())
                    .setEndDate(reqVO.getEndDate());
        } else {
            handoverReportRespVO
                    .setStartDate(reqVO.getBizDate())
                    .setEndDate(reqVO.getBizDate());
        }

        // 获取ar账收款信息
        List<AccOutInDO> accOutInList = accOutInService.getAccOutInList(new AccOutInListReqVO()
                .setHcode(reqVO.getHcode())
                .setGcode(reqVO.getGcode())
                .setAccType(NumberEnum.ONE.getNumber())
                .setBizDate(reqVO.getBizDate())
                .setOperator(reqVO.getRecorder())
                .setShiftNo(reqVO.getShiftNo()));

        if (CollUtil.isEmpty(accountRespVOList) && CollUtil.isEmpty(accOutInList)) {
            return handoverReportRespVO;
        }

        // 获得科目字典
        List<String> dictTypes = CollUtil.newArrayList(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode());
        List<DictDataRespDTO> dictDataList = dictDataApi.getDictDataListByDicTypes(dictTypes).getData();
        Map<String, DictDataRespDTO> dictDataMap = CollectionUtils.convertMap(dictDataList, DictDataRespDTO::getCode);

        // 填充科目名称
        List<AccountRespVO> accountVoList = CollectionUtils.convertList(accountRespVOList, account -> {
            DictDataRespDTO dictData = dictDataMap.get(account.getSubCode());
            if (dictData != null) {
                account.setSubName(dictData.getLabel());
            }
            return account;
        });

        // 获得班次列表 建立班次名称映射
        List<ShiftTimeDO> shiftTimeList = shiftTimeService.getShiftTimeList(new ShiftTimeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(NumberEnum.ONE.getNumber()));
        Map<String, String> shiftMap = CollectionUtils.convertMap(shiftTimeList, ShiftTimeDO::getShiftCode, ShiftTimeDO::getShiftName);

        // 获取现付账套列表 建立 账套代码与现付账套关系映射  科目代码与现付账套关系映射
        List<AccSetRespVO> accSetList = accSetService.getAccSetList(new AccSetReqVO().setGcode(reqVO.getGcode()).setIsEnable(NumberEnum.ONE.getNumber()));
        Map<String, AccSetRespVO> accSetAccCodeMap = CollectionUtils.convertMap(accSetList, AccSetRespVO::getAccCode);
        Map<String, AccSetRespVO> accSetSubCodeMap = CollectionUtils.convertMap(accSetList, AccSetRespVO::getSubCode);

        // 转换交班报表账务
        List<HandoverReportAccountRespVO> accounts = BeanUtils.toBean(accountVoList, HandoverReportAccountRespVO.class);
        // 设置现付账套名称
        accounts.forEach(o -> {
            o.setAccSetName(accSetAccCodeMap.getOrDefault(o.getPayCode(), new AccSetRespVO()).getAccName());
        });

        // 生成交接班报告
        return generateHandoverReport(accounts, accSetSubCodeMap, handoverReportRespVO, dictDataMap, shiftMap, reqVO, accOutInList);
    }

    private HandoverReportRespVO generateHandoverReport(List<HandoverReportAccountRespVO> accounts, Map<String, AccSetRespVO> accSetSubCodeMap,
                                                        HandoverReportRespVO handoverReportRespVO, Map<String, DictDataRespDTO> dictDataMap,
                                                        Map<String, String> shiftMap, HandoverReportReqVO reqVO, List<AccOutInDO> accOutInList) {

        // 第一步：初步筛选 根据 subType 进行分类
        categorizeBySubType(accounts, handoverReportRespVO);

        // 第二步：根据 subCode 进行整合并求和
        handoverReportRespVO.setPaymentDetails(groupBySubCodeAndSummarize(handoverReportRespVO.getPaymentTypeAccounts(), dictDataMap, shiftMap, NumberEnum.ONE.getNumber()));
        handoverReportRespVO.setConsumptionDetails(groupBySubCodeAndSummarize(handoverReportRespVO.getConsumptionTypeAccounts(), dictDataMap, shiftMap, NumberEnum.ZERO.getNumber()));

        // 第三步：筛选 recorder 字段相同且 subType 为 付款 的数据
        handoverReportRespVO.setRecorderPaymentDetails(groupByRecorderAndSummarize(handoverReportRespVO.getPaymentTypeAccounts(), dictDataMap));

        // 第四步：筛选 会员充值 的数据
        filterAndSummarizeMemberRecharge(accounts, accSetSubCodeMap, handoverReportRespVO, dictDataMap);

        // 第五步：筛选 Ar账收款的数据
        filterArAccounts(handoverReportRespVO, reqVO, accOutInList);

        return handoverReportRespVO;
    }

    private void filterArAccounts(HandoverReportRespVO handoverReportRespVO, HandoverReportReqVO reqVO, List<AccOutInDO> accOutInList) {
        // 建立账套代码与应收账套的映射
        List<String> arSetCodes = CollectionUtils.convertList(accOutInList, AccOutInDO::getArSetCode);
        List<ArSetDO> arsetListByArSetCodes = arSetService.getArSetListByArSetCodes(new ArSetListReqVO().setArSetCodes(arSetCodes).setGcode(reqVO.getGcode()));
        Map<String, ArSetDO> arSetDOMap = CollectionUtils.convertMap(arsetListByArSetCodes, ArSetDO::getArSetCode);
        // 本班收银员AR账核销收款分类汇总  信息构建
        List<HandoverReportRespVO.ArDetail> arAccounts = CollectionUtils.convertMultiMap(accOutInList, AccOutInDO::getCreator).entrySet().stream()
                .map(entry -> {
                    List<AccOutInDO> value = entry.getValue();
                    HandoverReportRespVO.ArDetail detail = new HandoverReportRespVO.ArDetail()
                            .setRecorder(entry.getKey())
                            .setTotalFee(value.stream().mapToLong(AccOutInDO::getFee).sum());
                    List<AccOutInAccountDetail> accOutInAccountDetails = CollectionUtils.convertList(value, accOutIn -> {
                        AccOutInAccountDetail accOutInAccountDetail = new AccOutInAccountDetail();
                        accOutInAccountDetail.setArSetName(arSetDOMap.getOrDefault(accOutIn.getArSetCode(), new ArSetDO()).getArSetName())
                                .setSubCode(accOutIn.getSubCode())
                                .setSubName(accOutIn.getSubName())
                                .setFee(accOutIn.getFee());
                        return accOutInAccountDetail;
                    });
                    detail.setAccounts(accOutInAccountDetails);
                    return detail;
                })
                .collect(Collectors.toList());
        handoverReportRespVO.setArDetails(arAccounts);
    }

    private void categorizeBySubType(List<HandoverReportAccountRespVO> accountShift, HandoverReportRespVO handoverReportRespVO) {
        // 分类 消费与付款  数据
        Map<String, List<HandoverReportAccountRespVO>> categorizedBySubType = CollectionUtils.convertMultiMap(accountShift, HandoverReportAccountRespVO::getSubType);
        List<HandoverReportAccountRespVO> paymentTypeAccounts = categorizedBySubType.getOrDefault(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), new ArrayList<>());
        List<HandoverReportAccountRespVO> consumptionTypeAccounts = categorizedBySubType.getOrDefault(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), new ArrayList<>());

        List<HandoverReportAccountRespVO> rmbAccounts = CollectionUtils.filterList(paymentTypeAccounts, account ->
                PayAccountEnum.RMB_RECEIPT.getCode().equals(account.getSubCode()) ||
                        PayAccountEnum.CASH_REFUND.getCode().equals(account.getSubCode()) ||
                        PayAccountEnum.RMB_DEPOSIT.getCode().equals(account.getSubCode()));

        handoverReportRespVO.setRmbPayTotalFee(calculateTotalFee(rmbAccounts));
        handoverReportRespVO.setPaymentTypeAccounts(paymentTypeAccounts);
        handoverReportRespVO.setPaymentTypeTotalFee(calculateTotalFee(paymentTypeAccounts));
        handoverReportRespVO.setConsumptionTypeTotalFee(calculateTotalFee(consumptionTypeAccounts));
        handoverReportRespVO.setConsumptionTypeAccounts(consumptionTypeAccounts);
    }

    private void filterAndSummarizeMemberRecharge(List<HandoverReportAccountRespVO> accountVoList, Map<String, AccSetRespVO> accSetSubCodeMap, HandoverReportRespVO handoverReportRespVO, Map<String, DictDataRespDTO> dictDataMap) {
        AccSetRespVO accSetRespVO = accSetSubCodeMap.getOrDefault(ConsumeAccountEnum.MEMBER_RECHARGE.getCode(), new AccSetRespVO());
        if (accSetRespVO == null) {
            return;
        }
        List<HandoverReportAccountRespVO> memberRechargeAccounts = CollectionUtils.filterList(accountVoList, account ->
                account.getPayCode().equals(accSetRespVO.getAccCode()) && DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode().equals(account.getSubType())
        );
        List<HandoverReportAccountRespVO> rmbAccounts = CollectionUtils.filterList(memberRechargeAccounts, account ->
                PayAccountEnum.RMB_RECEIPT.getCode().equals(account.getSubCode()) ||
                        PayAccountEnum.CASH_REFUND.getCode().equals(account.getSubCode()) ||
                        PayAccountEnum.RMB_DEPOSIT.getCode().equals(account.getSubCode()));
        // 集团账务
        List<HandoverReportAccountRespVO> gAccounts = CollectionUtils.filterList(memberRechargeAccounts, account -> NumberEnum.ONE.getNumber().equals(account.getIsGAcc()));
        // 门店账务
        List<HandoverReportAccountRespVO> mAccounts = CollectionUtils.filterList(memberRechargeAccounts, account -> NumberEnum.ZERO.getNumber().equals(account.getIsGAcc()));
        // 构建科目合计
        List<HandoverReportRespVO.Detail> list = CollectionUtils.convertMultiMap(memberRechargeAccounts, HandoverReportAccountRespVO::getSubCode).entrySet().stream()
                .map(o -> {
                    HandoverReportRespVO.Detail detail = new HandoverReportRespVO.Detail()
                            .setSubCode(o.getKey())
                            .setSubName(dictDataMap.get(o.getKey()).getLabel())
                            .setAccounts(o.getValue())
                            .setTotalFee(calculateTotalFee(o.getValue()));
                    return detail;
                })
                .collect(Collectors.toList());

        handoverReportRespVO.setMemberRechargeAccounts(list);
        handoverReportRespVO.setMemberRechargeTotalFee(calculateTotalFee(memberRechargeAccounts));
        handoverReportRespVO.setGMemberRechargeTotalFee(calculateTotalFee(gAccounts));
        handoverReportRespVO.setMMemberRechargeTotalFee(calculateTotalFee(mAccounts));
        handoverReportRespVO.setMemberRechargeRmbPayTotalFee(calculateTotalFee(rmbAccounts));
    }

    /**
     * 计算账务集合的合计
     *
     * @param accounts
     * @return
     */
    private long calculateTotalFee(List<HandoverReportAccountRespVO> accounts) {
        return accounts.stream().mapToLong(HandoverReportAccountRespVO::getFee).sum();
    }

    private List<HandoverReportRespVO.Detail> groupBySubCodeAndSummarize(List<HandoverReportAccountRespVO> accounts, Map<String, DictDataRespDTO> dictDataMap, Map<String, String> shiftMap, String isPayAccount) {
        return CollectionUtils.convertMultiMap(accounts, HandoverReportAccountRespVO::getSubCode).entrySet().stream()
                .map(entry -> {
                    HandoverReportRespVO.Detail detail = new HandoverReportRespVO.Detail()
                            .setSubCode(entry.getKey())
                            .setSubName(dictDataMap.getOrDefault(entry.getKey(), new DictDataRespDTO()).getLabel())
                            .setTotalFee(calculateTotalFee(entry.getValue()));
                    // 付款科目二级分类合计
                    if (NumberEnum.ONE.getNumber().equals(isPayAccount)) {
                        detail.setClassCode(PayAccountClassEnum.TWENTY.getCode())
                                .setClassName(PayAccountClassEnum.TWENTY.getName());
                        classPayAccount(entry, detail);
                    }
                    // 设置班次名称
                    List<HandoverReportAccountRespVO> value = entry.getValue();
                    value.forEach(account -> {
                        account.setShiftName(shiftMap.getOrDefault(account.getShiftNo(), "")).setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(account.getGuestSrcType()));
                    });
                    detail.setAccounts(value);
                    return detail;
                })
                .collect(Collectors.toList());
    }

    private static void classPayAccount(Map.Entry<String, List<HandoverReportAccountRespVO>> entry, HandoverReportRespVO.Detail detail) {
        // 配置付款科目二级分类代码以及名称
        if (PayAccountEnum.RMB_RECEIPT.getCode().equals(entry.getKey()) || PayAccountEnum.CASH_REFUND.getCode().equals(entry.getKey()) || PayAccountEnum.RMB_DEPOSIT.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.ONE.getCode()).setClassName(PayAccountClassEnum.ONE.getName());
        }
        if (PayAccountEnum.BANK_CARD.getCode().equals(entry.getKey()) || PayAccountEnum.BANK_CARD_REFUND.getCode().equals(entry.getKey())
                || PayAccountEnum.BANK_TRANSFER.getCode().equals(entry.getKey()) || PayAccountEnum.BANK_TRANSFER_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.TWO.getCode()).setClassName(PayAccountClassEnum.TWO.getName());
        }
        if (PayAccountEnum.SCAN_GUN_WX.getCode().equals(entry.getKey()) || PayAccountEnum.SCAN_GUN_WX_REFUND.getCode().equals(entry.getKey())
                || PayAccountEnum.SCAN_GUN_ALIPAY.getCode().equals(entry.getKey()) || PayAccountEnum.SCAN_GUN_ALIPAY_REFUND.getCode().equals(entry.getKey())
                || SCAN_GUN.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.EIGHT.getCode()).setClassName(PayAccountClassEnum.EIGHT.getName());
        }
        if (PayAccountEnum.COUPON.getCode().equals(entry.getKey()) || PayAccountEnum.DISCOUNT_COUPON.getCode().equals(entry.getKey())
                || PayAccountEnum.VOUCHER.getCode().equals(entry.getKey()) || PayAccountEnum.PRE_TICKET.getCode().equals(entry.getKey())
                || PayAccountEnum.FREE_VOUCHER.getCode().equals(entry.getKey()) || PayAccountEnum.COUPON_REFUND.getCode().equals(entry.getKey())
                || PayAccountEnum.PRE_TICKET_REFUND.getCode().equals(entry.getKey()) || PayAccountEnum.FREE_VOUCHER_REFUND.getCode().equals(entry.getKey())
                || PayAccountEnum.DISCOUNT_COUPON_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.SEVEN.getCode()).setClassName(PayAccountClassEnum.SEVEN.getName());
        }
        if (STORE_CARD.getCode().equals(entry.getKey()) || PayAccountEnum.STORE_CARD_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.THREE.getCode()).setClassName(PayAccountClassEnum.THREE.getName());
        }
        if (PayAccountEnum.CHEQUE.getCode().equals(entry.getKey()) || PayAccountEnum.CHEQUE_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.FOUR.getCode()).setClassName(PayAccountClassEnum.FOUR.getName());
        }
        if (CREDIT_S_ACCOUNT.getCode().equals(entry.getKey()) || PayAccountEnum.AR_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.FIVE.getCode()).setClassName(PayAccountClassEnum.FIVE.getName());
        }
        if (PayAccountEnum.DRAFT.getCode().equals(entry.getKey()) || PayAccountEnum.DRAFT_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.NINE.getCode()).setClassName(PayAccountClassEnum.NINE.getName());
        }
        if (PayAccountEnum.HK_DOLLAR.getCode().equals(entry.getKey()) || PayAccountEnum.HK_DOLLAR_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.TEN.getCode()).setClassName(PayAccountClassEnum.TEN.getName());
        }
        if (PayAccountEnum.DISCOUNT.getCode().equals(entry.getKey()) || PayAccountEnum.DISCOUNT_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.ELEVEN.getCode()).setClassName(PayAccountClassEnum.ELEVEN.getName());
        }
        if (PayAccountEnum.CLOUD_QUICK_PAY.getCode().equals(entry.getKey()) || PayAccountEnum.CLOUD_QUICK_PAY_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.TWELVE.getCode()).setClassName(PayAccountClassEnum.TWELVE.getName());
        }
        if (PayAccountEnum.POINT_CHANGE.getCode().equals(entry.getKey()) || PayAccountEnum.POINT_CHANGE_REFUND.getCode().equals(entry.getKey())) {
            detail.setClassCode(PayAccountClassEnum.THIRTEEN.getCode()).setClassName(PayAccountClassEnum.THIRTEEN.getName());
        }
    }

    private List<HandoverReportRespVO.RecorderDetail> groupByRecorderAndSummarize(List<HandoverReportAccountRespVO> accounts, Map<String, DictDataRespDTO> dictDataMap) {
        return CollectionUtils.convertMultiMap(accounts, HandoverReportAccountRespVO::getRecorder).entrySet().stream()
                .map(entry -> {
                    List<HandoverReportAccountRespVO> rmbAccounts = CollectionUtils.filterList(entry.getValue(), account ->
                            PayAccountEnum.RMB_RECEIPT.getCode().equals(account.getSubCode()) ||
                                    PayAccountEnum.CASH_REFUND.getCode().equals(account.getSubCode()) ||
                                    PayAccountEnum.RMB_DEPOSIT.getCode().equals(account.getSubCode()));

                    List<HandoverReportRespVO.Detail> list = CollectionUtils.convertMultiMap(entry.getValue(), HandoverReportAccountRespVO::getSubCode).entrySet().stream()
                            .map(o -> {
                                HandoverReportRespVO.Detail detail = new HandoverReportRespVO.Detail()
                                        .setSubCode(o.getKey())
                                        .setSubName(dictDataMap.getOrDefault(o.getKey(), new DictDataRespDTO()).getLabel())
                                        .setTotalFee(calculateTotalFee(o.getValue()))
                                        .setClassCode(PayAccountClassEnum.TWENTY.getCode())
                                        .setClassName(PayAccountClassEnum.TWENTY.getName());
                                // 付款科目二级分类
                                classPayAccount(o, detail);
                                return detail;
                            })
                            .collect(Collectors.toList());

                    return new HandoverReportRespVO.RecorderDetail()
                            .setRecorder(entry.getKey())
                            .setTotalFee(calculateTotalFee(entry.getValue()))
                            .setAccounts(list)
                            .setMemberRechargeRmbPayTotalFee(calculateTotalFee(rmbAccounts));
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取选中房间账务详情
     */
    @Override
    public List<AccountDO> getArSetRoomAccountList(AccountChooseReqVO reqVO) {
        return accountMapper.selectList(reqVO);
    }

    @Override
    public List<AccRecordStatByBizDateRespVO> statAccRecordByBizDate(String gcode, String hcode, LocalDate bizDate) {
        return accountMapper.statAccRecordByBizDate(gcode, hcode, bizDate, AccountTypeEnum.CASH.getCode());
    }

    @Override
    public StatCheckOutAccountRespVO statCloseAccount(FinishCloseAccountReqVO reqVO) {
        StatCheckOutAccountRespVO statCheckOutAccountRespVO = statAccount(reqVO.getGcode(), reqVO.getHcode(), null, reqVO.getAccNoList(), null, null);
        return statCheckOutAccountRespVO;
    }

    @Override
    public PageResult<AccountRespVO> getTransactionPage(TransactionPageReqVO reqVO) {
        PageResult<AccountDO> pageResult = accountMapper.selectTransactionPage(reqVO);
        return new PageResult<>(buildAccountList(pageResult.getList()), pageResult.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#r.hcode"}, expire = ACCOUNT_TIMEOUT_MILLIS, acquireTimeout = 5000)
    @LogRecord(success = PMS_ORDER_REFUND_ACCOUNT_SUCCESS, type = PMS_ORDER_REFUND_ACCOUNT_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public void refundAccount(RefundAccountReqVO r) {
        LocalDate bizDate = generalConfigService.getBizDate(r.getHcode());
        List<AccountDO> accountList = validateRefundAccount(r, bizDate);
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(r.getHcode());

        String payNo = IdUtil.getSnowflakeNextIdStr();

        LocalDateTime now = LocalDateTime.now();
        // 修改原账务状态为已退款
        List<AccountDO> oldAccounts = CollUtil.newArrayList();
        // 增加新的平衡账务
        List<AccountDO> newAccountList = CollUtil.newArrayList();
        accountList.forEach(account -> {
            String accNo = IdUtil.getSnowflakeNextIdStr();
            oldAccounts.add(AccountConvert.INSTANCE.oldRefundAccountConvert(r, account, shiftNo, bizDate, payNo, now));
            AccountDO newAccount = BeanUtils.toBean(account, AccountDO.class);
            newAccount.setRemark(r.getRemark());
            AccountConvert.INSTANCE.newRefundAccountConvert(r, newAccount, account, shiftNo, bizDate, payNo, now, accNo);

            String refundNo = "";
            LocalDateTime successTime;
            if (BooleanEnum.TRUE.getValue().equals(account.getIsPreAuthAffirm())) {
                PreFinishCancelRespDTO preFinishCancelRespDTO = scanGunFinishCancel(account);
                refundNo = preFinishCancelRespDTO.getFinishCancelNo();
                successTime = preFinishCancelRespDTO.getSuccessTime();
            } else {
                RefundRespDTO refundRespDTO = scanGunRefund(account, r.getFee());
                refundNo = refundRespDTO.getRefundNo();
                successTime = refundRespDTO.getSuccessTime();
            }
            newAccount.setOutOrderNo(refundNo).setRefundAccNo(account.getAccNo())
                    .setCreateTime(successTime).setUpdateTime(successTime);

            newAccountList.add(newAccount);
        });
        accountMapper.updateBatch(oldAccounts);
        accountMapper.insertBatch(newAccountList);
        // 记录日志
        refundAccountLogHandle(oldAccounts, newAccountList);
    }

    private void refundAccountLogHandle(List<AccountDO> oldAccounts, List<AccountDO> newAccountList) {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(new StringReader(PMS_ORDER_REFUND_ACCOUNT_SUCCESS_TEMPLATE), "template");
        StringWriter writer = new StringWriter();
        Map<String, Object> context = new HashMap<>();
        context.put("oldAccounts", oldAccounts);
        context.put("newAccountList", newAccountList);
        String content = mustache.execute(writer, context).toString();
        LogRecordContext.putVariable("order", newAccountList.getFirst());
        LogRecordContext.putVariable("content", content);
    }

    @Override
    public AccountStatRefundRespVO getAccountStatRefund(AccountChooseReqVO accountChooseReqVO) {
        // 查询已经退款的账务列表
        List<AccountDO> refundAccountList = accountMapper.selectRefundAccountList(accountChooseReqVO);
        // 查询被退款的账务
        List<AccountDO> accountDOS = accountMapper.selectList(accountChooseReqVO);
        AccountDO account = accountDOS.getFirst();

        // 计算已退款的金额
        long refundFee = refundAccountList.stream().mapToLong(AccountDO::getFee).sum();
       /* if(account.getFee()+refundFee==0){
            throw exception(ACCOUNT_REFUND_AMOUNT_EQUAL_ZERO);
        }*/
        return buildAccountStatRefundRespVO(account, refundFee);
    }

    @Override
    public DepositAccountRespVO getDeposit(AccountReqVO reqVO) {
        DepositAccountRespVO depositAccountRespVO = new DepositAccountRespVO();
        HotelParamConfigDO hotelParamConfig = hotelParamConfigService.getHotelParamConfig(new HotelParamConfigReqVO().setParamType(ParamConfigTypeEnum.PARAM_TYPE_DEPOSIT.getParamType())
                .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
        if (ObjectUtil.isEmpty(hotelParamConfig)) {
            throw exception(DEPOSIT_CONFIG_NOT_EXISTS);
        }
        if (hotelParamConfig.getValue() instanceof Deposit depositConfig) {
            long deposit = depositConfig.getDeposit().longValue();
            if (depositConfig.getMode().equals(NumberEnum.ZERO.getNumber())) {
                // 3. 获取订单价格信息
                List<OrderPriceDO> orderPrices = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setOrderNos(List.of(reqVO.getNo()))
                        .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
                // 3.1 获取当天订单价格
                OrderPriceDO todayOrderPrice = CollectionUtils.findFirst(orderPrices, o -> o.getPriceDate().isEqual(LocalDate.now()));
                if (todayOrderPrice == null) {
                    // 取最后一天订单价格
                    todayOrderPrice = orderPrices.getLast();
                }
                Long vipPrice = todayOrderPrice.getVipPrice();
                // 向上取整 整百
                Long roundedVipPrice = (long) (Math.ceil((vipPrice) / 10000.0) * 10000);
                depositAccountRespVO.setFee(deposit * 100 + roundedVipPrice);
            } else {
                depositAccountRespVO.setFee(deposit * 100);
            }
        }
        depositAccountRespVO.setSubCode(RMB_DEPOSIT.getCode());
        return depositAccountRespVO;
    }

    @Override
    @LogRecord(success = PMS_RECORDING_SUCCESS, type = PMS_ORDER_RECORDING_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public String recordingAccount(RecordingAccountReqVO reqVO) {
        validatePayAccount(reqVO.getOutOrderNo(), null);
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(reqVO.getGcode(), reqVO.getHcode(), ServiceTypeEnum.PAYMENT.getCode());
        ServiceIntegrationPaymentRespVO serviceIntegrationPaymentRespVO = BeanUtils.toBean(serviceIntegration, ServiceIntegrationPaymentRespVO.class);
        CommonResult<QueryOrderRespDTO> query = payBaseApi.query(new QueryOrderReqDTO().setHcode(reqVO.getHcode()).setPlatform(serviceIntegrationPaymentRespVO.getSolutionProvider())
                .setOrderNo(reqVO.getOutOrderNo()));
        // 查询失败
        if (query.getCode() != 0) {
            throw exception(QUERY_FAILURE, query.getMsg());
        }
        QueryOrderRespDTO queryData = query.getData();

        validatePayAccount(null, queryData);
        if (ObjectUtil.isEmpty(queryData) || !NumberEnum.TWO.getNumber().equals(queryData.getStatus().toString())) {
            throw exception(PAYMENT_FAILED_INSERT_NOT_ALLOWED);
        }
        PayAccountSaveReqVO payAccountSaveReqVO = new PayAccountSaveReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setNo(reqVO.getNo())
                .setAccType(reqVO.getAccType()).setFee(queryData.getPayPrice()).setRemark(reqVO.getRemark())
                .setPayCode("").setTogetherCode(reqVO.getTogetherCode());
        recording(payAccountSaveReqVO, queryData);
        return queryData.getAccOrderNo();
    }

    /**
     * 校验支付的账务是否在pms账务中
     *
     * @param outOrderNo
     * @param queryData
     */
    public void validatePayAccount(String outOrderNo, QueryOrderRespDTO queryData) {
        // 忽略租户id
        TenantContextHolder.setIgnore(true);
        if (StrUtil.isNotEmpty(outOrderNo)) {
            AccountDO accountDO = accountMapper.selectOne(AccountDO::getOutOrderNo, outOrderNo);
            if (ObjectUtil.isNotEmpty(accountDO)) {
                throw exception(ACCOUNT_EXISTS);
            }
        }

        if (ObjectUtil.isNotEmpty(queryData)) {
            // 判断channelOrderNo，accOrderNo是否在账务中存在
            AccountDO accountDO2 = accountMapper.selectOne(
                    new LambdaQueryWrapper<AccountDO>()
                            .eq(AccountDO::getOutOrderNo, queryData.getChannelOrderNo())  // 按照 channelOrderNo 查询
                            .or()  // OR 条件
                            .eq(AccountDO::getOutOrderNo, queryData.getAccOrderNo())     // 按照 accOrderNo 查询
            );
            if (ObjectUtil.isNotEmpty(accountDO2)) {
                throw exception(ACCOUNT_EXISTS);
            }
        }

    }


    private String recording(PayAccountSaveReqVO reqVO, QueryOrderRespDTO payScanRespDTO) {
        // 关闭忽略租户id
        TenantContextHolder.setIgnore(false);
        // 校验支付
        validPay(reqVO.getGcode(), reqVO.getHcode(), reqVO.getAccType(), reqVO.getNo());
        // 根据收款类型，设置正负数
        buildFee(reqVO);
        //校验付款方式与门店设置的付款方式是否一致
        String currencyUnit = validUnit(reqVO.getSubCode(), reqVO.getHcode(), reqVO.getGcode(), reqVO.getCurrencyUnit());
        // 将请求VO转换为账户DO对象
        AccountDO account = BeanUtils.toBean(reqVO, AccountDO.class);
        account.setCurrencyUnit(currencyUnit);
        // 获取业务日期
        // 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
        // 获取当前登录用户的班次信息
        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());

        // 设置账户的子类型、账号、状态、费用、业务日期、记录者和班次等信息
        account.setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())
                .setAccNo(IdUtil.getSnowflakeNextIdStr())
                .setState(StrUtil.isNotBlank(reqVO.getState()) ? reqVO.getState() : AccountStatusEnum.UNCLOSED.getCode())
                .setAfterTaxFee(reqVO.getFee())
                .setBizDate(bizDate)
                .setRecorder(SecurityFrameworkUtils.getLoginUserName())
                .setOutOrderNo(payScanRespDTO.getOrderNo())
                .setAccDetail("账户端交易单号" + payScanRespDTO.getAccOrderNo())
                .setPlatForm(payScanRespDTO.getPlatform())
                .setShiftNo(shiftNo);
        if (Objects.equals(reqVO.getState(), AccountStatusEnum.CLOSED.getCode())) {
            account.setPayShiftNo(shiftNo)
                    .setPayBizDate(bizDate)
                    .setPayTime(LocalDateTime.now())
                    .setIsCanRev(BooleanEnum.FALSE.getValue())
                    .setPayer(SecurityFrameworkUtils.getLoginUserName());
        }
        if (PayAccountTypeEnum.WECHAT.getCode().equals(payScanRespDTO.getAccountType())) {
            account.setSubCode(SCAN_GUN_WX.getCode());
        } else if (PayAccountTypeEnum.ALIPAY.getCode().equals(payScanRespDTO.getAccountType())) {
            account.setSubCode(SCAN_GUN_ALIPAY.getCode());
        }
        if (StrUtil.isNotEmpty(reqVO.getPayNo())) {
            account.setPayNo(reqVO.getPayNo());
        } else {
            account.setPayNo(account.getAccNo());
        }
        if (Objects.nonNull(reqVO.getOrderTogether())) {
            account.setRCode(reqVO.getOrderTogether().getRCode())
                    .setRNo(reqVO.getOrderTogether().getRNo())
                    .setGuestName(reqVO.getOrderTogether().getGuestName())
                    .setGuestSrcType(reqVO.getOrderTogether().getGuestSrcType());
        }
        // 根据账户类型处理不同的业务逻辑
        handleAccountType(account, reqVO);
        // 插入账户信息到数据库
        accountMapper.insert(account);
        // 记录日志
        LogRecordContext.putVariable("order", account);
        LogRecordContext.putVariable("subCode", PayAccountEnum.getLabelByCode(account.getSubCode()));
        // 返回账户的账号
        return account.getAccNo();
    }

    @Override
    public List<AccountDO> getTransactionReport(TransactionDetailReqVO reqVO) {
        return accountMapper.selectTransactionReport(reqVO);
    }

    @Override
    public void judgeARAccount(FinishCheckOutAccountReqVO reqVO) {
        StatCheckOutAccountRespVO statCheckOutAccountRespVO = statCheckOutAccount(reqVO);
        if (statCheckOutAccountRespVO.getIsAR().equals(true) && CREDIT_S_ACCOUNT.getCode().equals(reqVO.getSubCode())) {
            throw exception(ORDER_IS_AR_ACCOUNT);
        }
    }

    @Override
    public RecordingAccountRespVO getRecordingAccount(RecordingAccountQueryReqVO reqVO) {
        validatePayAccount(reqVO.getOutOrderNo(), null);
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(reqVO.getGcode(), reqVO.getHcode(), ServiceTypeEnum.PAYMENT.getCode());
        ServiceIntegrationPaymentRespVO serviceIntegrationPaymentRespVO = BeanUtils.toBean(serviceIntegration, ServiceIntegrationPaymentRespVO.class);
        CommonResult<QueryOrderRespDTO> query = payBaseApi.query(new QueryOrderReqDTO().setHcode(reqVO.getHcode()).setPlatform(serviceIntegrationPaymentRespVO.getSolutionProvider())
                .setOrderNo(reqVO.getOutOrderNo()));
        // 查询失败
        if (query.getCode() != 0) {
            throw exception(QUERY_FAILURE, query.getMsg());
        }
        QueryOrderRespDTO queryData = query.getData();

        validatePayAccount(null, queryData);
        if (ObjectUtil.isEmpty(queryData) || !NumberEnum.TWO.getNumber().equals(queryData.getStatus().toString())) {
            throw exception(ORDER_NOT_EXISTS_IN_DB);
        }

        return new RecordingAccountRespVO().setFee(queryData.getPayPrice()).setOutOrderNo(queryData.getOrderNo());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundGood(ConsumeRefundGoodReqVO reqVO) {
        AccountDO accountDO = validateAccount(reqVO.getAccNo());
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());

        String shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
        LocalDateTime now = LocalDateTime.now();
        // 增加新的账务
        String accNo = IdUtil.getSnowflakeNextIdStr();

        AccountDO newAccount = BeanUtils.toBean(accountDO, AccountDO.class);
        //newAccount.setRemark(r.getRemark());
        AccountConvert.INSTANCE.newRefundGoodsAccountConvert(newAccount, accountDO, shiftNo, bizDate, now, accNo);
        newAccount.setIsVerify(NumberEnum.TWO.getNumber());
        newAccount.setRemark(reqVO.getRemark());

        // 库存集合
        List<ErpStockOutUpdateNumberReqDTO.Item> itemList = new ArrayList<>();
        // 记录退货商品明细
        returnGoods(accountDO, bizDate, shiftNo, reqVO.getGoodsList(), newAccount, itemList);

        // 获得仓库配置
        String warehouseConfig = generalConfigService.getWarehouseConfig(reqVO.getGcode(), reqVO.getHcode());
        // 判断是否开启配置
        if (NumberEnum.ONE.getNumber().equals(warehouseConfig)) {
            // 退还库存
            CommonResult<Boolean> res = stockOutApi.updateBrewingNumber(new ErpStockOutUpdateNumberReqDTO()
                    .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setOrderNo(accountDO.getNo())
                    .setAccountNo(accountDO.getAccNo()).setItems(itemList));
            if (res.getCode() != 0) {
                throw exception(FAILURE_REASON, res.getMsg());
            }
        }


        accountMapper.insert(newAccount);
        accountMapper.updateById(new AccountDO().setId(accountDO.getId()).setTags(AccountTagEnum.REFUND_GOODS.getTitle()));
        // 商品冲调
        //itemRed(accountList);

    }

    @Override
    public List<ConsumeGoodRespVO> getRefundGood(ConsumeGoodReqVO reqVO) {
        List<GoodsSellRecordDO> goodsSellRecordList = goodsSellRecordService.getGoodsSellRecordList(new GoodsSellRecordListReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setAccNo(reqVO.getAccNo())
                .setAccType(NumberEnum.ZERO.getNumber())
                .setOffset(NumberEnum.ZERO.getNumber()));

        Map<String, List<GoodsSellRecordDO>> goodSellListMap = CollectionUtils.convertMultiMap(goodsSellRecordList, GoodsSellRecordDO::getGoodsCode);

        List<ConsumeGoodRespVO> goodList = new ArrayList<>();
        for (Map.Entry<String, List<GoodsSellRecordDO>> entry : goodSellListMap.entrySet()) {
            ConsumeGoodRespVO goodRespVO = new ConsumeGoodRespVO();
            String goodCode = entry.getKey();
            List<GoodsSellRecordDO> sellRecordDOS = entry.getValue();
            int returnNum = sellRecordDOS.stream()
                    .mapToInt(GoodsSellRecordDO::getNum) // 提取 num 字段并转为 int
                    .sum(); // 求和

            if (returnNum <= 0) {
                continue;
            }

            goodRespVO.setGoodsCode(goodCode);
            goodRespVO.setGoodsName(sellRecordDOS.getFirst().getGoodsName());
            goodRespVO.setRemainCount(returnNum);
            goodRespVO.setPrice(sellRecordDOS.getFirst().getPrice());

            goodList.add(goodRespVO);
        }
        return goodList;
    }

    @Override
    public List<AccountDO> getAccountChangeList(TransferAccountReqVO reqVO) {
        return accountMapper.selectChangeList(reqVO);
    }

    @Override
    public List<AccountRoomFeeRespVO> getAccountRoomFeeList(AccountListReqVO reqVO) {
        // 获得房费科目
        List<DictDataRespDTO> dictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT_ROOM_FEE.getCode()).getData();
        List<String> subCodes = CollectionUtils.convertList(dictData, DictDataRespDTO::getCode);
        List<AccountDO> accountDOS = accountMapper.selectList(reqVO.setSubCodes(subCodes).setState(AccountStatusEnum.UNCLOSED.getCode())
                .setRecorder(RECORDER));

        if (CollUtil.isEmpty(accountDOS)) {
            return new ArrayList<>();
        }

        List<String> accNos = CollectionUtils.convertList(accountDOS, AccountDO::getAccNo);

        List<CouponRespDTO> coupons = couponApi.getCouponList(new CouponReqDTO().setGcode(reqVO.getGcode())
                .setAccNos(accNos)).getData();
        Map<Object, CouponRespDTO> couponRespDTOMap = CollectionUtils.convertMap(coupons, CouponRespDTO::getAccNo);

        List<AccountRoomFeeRespVO> list = new ArrayList<>();
        accountDOS.forEach(account -> {
            AccountRoomFeeRespVO accountRoomFeeRespVO = BeanUtils.toBean(account, AccountRoomFeeRespVO.class);
            accountRoomFeeRespVO.setSubName(ConsumeAccountEnum.getLabelByCode(account.getSubCode()));
            accountRoomFeeRespVO.setIsUsed(couponRespDTOMap.get(account.getAccNo()) == null ? NumberEnum.ZERO.getNumber() : NumberEnum.ONE.getNumber());
            if (NumberEnum.ONE.getNumber().equals(accountRoomFeeRespVO.getIsUsed())) {
                accountRoomFeeRespVO.setCouponCode(couponRespDTOMap.get(account.getAccNo()).getCouponCode());
            }
            list.add(accountRoomFeeRespVO);
        });

        return list;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @LogRecord(success = PMS_PAY_SUCCESS, type = PMS_ORDER_PAY_TYPE, subType = PMS_ORDER_SUBTYPE,
            bizNo = "{{#order.no}}",
            extra = PMS_ORDER_property)
    public String batchPay(BatchPayAccountSaveReqVO reqVO) {
     /*   // 判断是否验证短信
        if (NumberEnum.TWO.getNumberInt().equals(reqVO.getVerifyMode())) {
            CommonResult<Boolean> valid = smsCodeApi.useSmsCode(new SmsCodeUseReqDTO()
                    .setMobile(reqVO.getPhone()).setScene(5).setCode(reqVO.getSmsCode()).setUsedIp(getClientIP()));
            if (valid.getCode() != 0) throw exception(SMS_CODE_ERROR, valid.getMsg());
        }
        // 验证密码
        if (NumberEnum.ONE.getNumberInt().equals(reqVO.getVerifyMode())) {
            CommonResult<Boolean> verify = memberApi.verifyMemberPassword(reqVO.getGcode(), reqVO.getMcode(), reqVO.getPwd());
            if (verify.getCode() != 0) throw exception(MEMBER_PAY_ERROR, verify.getMsg());
        }*/
        // 校验支付
        validPay(reqVO.getGcode(), reqVO.getHcode(), reqVO.getAccType(), reqVO.getNo());
        //获得门店信息
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(reqVO.getHcode()).getData();
        List<AccountDO> accountDOS = new ArrayList<>();
        for (BatchPayAccountSaveReqVO.PayInfo payInfo : reqVO.getPayInfoList()) {
            // 暂只支持优惠券
            if (!COUPON.getCode().equals(payInfo.getSubCode())) {
                throw exception(COUPON_ONLY_SUPPORTED);
            }
            // 根据收款类型，设置正负数
            if (NumberEnum.MINUS.getNumber().equals(payInfo.getPayValue())) {
                payInfo.setFee(-payInfo.getFee());
            }
            AccountDO account = BeanUtils.toBean(payInfo, AccountDO.class);

            // 获取业务日期
            // 获取营业日
            LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());
            // 获取当前登录用户的班次信息
            String shiftNo = "";
            if (OrderSrcEnum.APP.getCode().equals(reqVO.getSource()) || OrderSrcEnum.AGENT.getCode().equals(reqVO.getSource())) {
                List<ShiftTimeRespDTO> shiftTimeList = shiftTimeApi.getShiftTimeList(new ShiftTimeReqDTO()
                        .setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(BooleanEnum.TRUE.getValue())).getData();
                shiftNo = assignShiftCodeBasedOnTime(shiftTimeList);
            } else {
                shiftNo = shiftTimeService.getShiftTimeByUserIdFromCache(reqVO.getHcode());
            }

            // 设置账户的子类型、账号、状态、费用、业务日期、记录者和班次等信息
            account.setGcode(reqVO.getGcode())
                    .setHcode(reqVO.getHcode())
                    .setNo(reqVO.getNo())
                    .setTogetherCode(reqVO.getTogetherCode())
                    .setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())
                    .setAccNo(IdUtil.getSnowflakeNextIdStr())
                    .setState(StrUtil.isNotBlank(reqVO.getState()) ? reqVO.getState() : AccountStatusEnum.UNCLOSED.getCode())
                    .setAfterTaxFee(payInfo.getFee())
                    .setBizDate(bizDate)
                    .setAccType(reqVO.getAccType())
                    .setRecorder(SecurityFrameworkUtils.getLoginUserName())
                    .setCurrencyUnit(merchantRespDTO.getCurrencyUnit())
                    .setShiftNo(shiftNo);
            if (Objects.equals(reqVO.getState(), AccountStatusEnum.CLOSED.getCode())) {
                account.setPayShiftNo(shiftNo)
                        .setPayBizDate(bizDate)
                        .setPayTime(LocalDateTime.now())
                        .setIsCanRev(BooleanEnum.FALSE.getValue())
                        .setPayer(SecurityFrameworkUtils.getLoginUserName());
            }

            account.setPayNo(account.getAccNo());

            if (Objects.nonNull(reqVO.getOrderTogether())) {
                account.setRCode(reqVO.getOrderTogether().getRCode())
                        .setRNo(reqVO.getOrderTogether().getRNo())
                        .setGuestName(reqVO.getOrderTogether().getGuestName())
                        .setGuestSrcType(reqVO.getOrderTogether().getGuestSrcType());
            }

            accountDOS.add(account);

        }
        //account.setCurrencyUnit(currencyUnit);
        // 根据账户类型处理不同的业务逻辑
        handleAccountTypeList(accountDOS, reqVO);

        // 插入账户信息到数据库
        accountMapper.insertBatch(accountDOS);

        // 记录日志
        LogRecordContext.putVariable("order", accountDOS.getFirst());
        LogRecordContext.putVariable("subCode", PayAccountEnum.getLabelByCode(accountDOS.getFirst().getSubCode()));

        return "";
    }

    @Override
    public List<AccountReminderRespVO> getReminderList(ReminderReqVO pageReqVO) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO()
                .setGcode(pageReqVO.getGcode())
                .setHcode(pageReqVO.getHcode())
                .setRNo(pageReqVO.getRNo())
                .setStates(List.of(OrderStateEnum.CHECK_IN.getCode())));

        if (CollUtil.isEmpty(orderList)) {
            return new ArrayList<>();
        }

        // 对所有订单进行催账逻辑处理
        List<AccountReminderRespVO> reminderList = processOrdersForReminder(orderList, pageReqVO.getGcode(), pageReqVO.getHcode(), pageReqVO.getIsShowTogether());

        // 根据types字段过滤结果
        if (CollUtil.isNotEmpty(pageReqVO.getTypes())) {
            reminderList = CollectionUtils.filterList(reminderList,
                    reminder -> pageReqVO.getTypes().contains(reminder.getType()));
        }

        // 按type字段排序，从1到4
        reminderList.sort((r1, r2) -> {
            String type1 = r1.getType();
            String type2 = r2.getType();
            return type1.compareTo(type2);
        });

        return reminderList;
    }

    @Override
    public ReminderCountRespVO getReminderCount(ReminderReqVO pageReqVO) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO()
                .setGcode(pageReqVO.getGcode())
                .setHcode(pageReqVO.getHcode())
                .setRNo(pageReqVO.getRNo())
                .setStates(List.of(OrderStateEnum.CHECK_IN.getCode())));

        if (CollUtil.isEmpty(orderList)) {
            return new ReminderCountRespVO().setArrearsCount(0L);
        }

        // 获得订单号列表
        List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);


        // 获取订单价格信息
        List<OrderPriceDO> orderPrices = orderPriceService.getOrderPriceList(new OrderPriceReqVO()
                .setOrderNos(orderNoList).setGcode(pageReqVO.getGcode()).setHcode(pageReqVO.getHcode()));

        // 获取当天订单价格列表
        List<OrderPriceDO> todayOrderPriceList = CollectionUtils.filterList(orderPrices,
                o -> o.getPriceDate().equals(LocalDate.now()) || o.getPriceDate().equals(LocalDate.now().minusDays(1)));
        List<OrderPriceDO> distinct = CollectionUtils.distinct(todayOrderPriceList, OrderPriceDO::getOrderNo);
        Map<String, OrderPriceDO> todayOrderPriceMap = CollectionUtils.convertMap(distinct, OrderPriceDO::getOrderNo);

        // 获得所有在住客人账务
        List<AccountDO> accountList = getAccountList(new AccountListReqVO().setGcode(pageReqVO.getGcode()).setHcode(pageReqVO.getHcode()).setNos(orderNoList));
        Map<String, List<AccountDO>> accountListMap = CollectionUtils.convertMultiMap(accountList, AccountDO::getNo);

        // 获得预授权科目
        List<DictDataRespDTO> preDictData = dictDataApi.getDictDataListByDictTypeAndcssClass(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_PAY_ACCOUNT_PRE.getCode()).getData();
        List<String> preCodes = CollectionUtils.convertList(preDictData, DictDataRespDTO::getCode);
        long arrearsCount = 0;

        for (OrderDO orderDO : orderList) {
            // 获取当天房价
            OrderPriceDO todayPrice = todayOrderPriceMap.get(orderDO.getOrderNo());

            // 计算账务费用
            List<AccountDO> orderAccounts = accountListMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
            AccountFeeCalculation feeCalc = calculateAccountFees(orderAccounts, preCodes);

            // 计算需加收房费和预计欠费
            Long addRoomFee = calculateAddRoomFee(orderDO, todayPrice);
            Long arrearsFee = feeCalc.getConsumeFee() - feeCalc.getPayFee() - feeCalc.getPreFee() + addRoomFee;

            // 判断是否有预计欠费
            if (arrearsFee != null && arrearsFee > 0) {
                arrearsCount++;
            }
        }

        return new ReminderCountRespVO().setArrearsCount(arrearsCount);
    }

    /**
     * 对订单列表进行催账逻辑处理
     */
    private List<AccountReminderRespVO> processOrdersForReminder(List<OrderDO> orderList, String gcode, String hcode, String isShowTogether) {
        if (CollUtil.isEmpty(orderList)) {
            return new ArrayList<>();
        }

        // 获得所以中介列表
        List<ProtocolAgentDO> protocolAgentList = protocolAgentService.getProtocolAgentList(new ProtocolAgentReqVO().setGcode(gcode));
        Map<String, ProtocolAgentDO> agentMap = CollectionUtils.convertMap(protocolAgentList, ProtocolAgentDO::getPaCode);

        // 获得所以会员列表
        List<MemberAndStoreCardRespDTO> memberList = memberApi.getMemberList(new MemberListReqDTO().setGcode(gcode).setHcode(hcode)).getData();
        Map<String, MemberAndStoreCardRespDTO> memberMap = CollectionUtils.convertMap(memberList, MemberAndStoreCardRespDTO::getMcode);

        /*List<MemberTypeSimpleRespDTO> memberTypeList = memberTypeApi.getTypeSimpleList(new MemberTypeReqDTO().setGcode(gcode)).getData();
        Map<String, MemberTypeSimpleRespDTO> memberTypeMap = CollectionUtils.convertMap(memberTypeList, MemberTypeSimpleRespDTO::getMtCode);*/

        // 获得订单号列表
        List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);

        // 获得所有在住宾客列表
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO()
                .setGcode(gcode).setHcode(hcode).setState(OrderStateEnum.CHECK_IN.getCode()).setOrderNos(orderNoList));
        // 过滤出主单的信息
        List<OrderTogetherDO> mainOrderTogetherList = CollectionUtils.filterList(orderTogetherList, o -> NumberEnum.ONE.getNumber().equals(o.getIsMain()));
        Map<String, OrderTogetherDO> mainOrderTogetherMap = CollectionUtils.convertMap(mainOrderTogetherList, OrderTogetherDO::getOrderNo);
        // 过滤出非主单的信息
        List<OrderTogetherDO> nonMainOrderTogetherList = CollectionUtils.filterList(orderTogetherList, o -> NumberEnum.ZERO.getNumber().equals(o.getIsMain()));
        Map<String, List<OrderTogetherDO>> nonMainOrderTogetherMap = CollectionUtils.convertMultiMap(nonMainOrderTogetherList, OrderTogetherDO::getOrderNo);
        //Map<String, List<OrderTogetherDO>> togetherMap = CollectionUtils.convertMultiMap(orderTogetherList, OrderTogetherDO::getOrderNo);

        // 获取订单价格信息
        List<OrderPriceDO> orderPrices = orderPriceService.getOrderPriceList(new OrderPriceReqVO()
                .setOrderNos(orderNoList).setGcode(gcode).setHcode(hcode));

        // 获取当天订单价格列表
        List<OrderPriceDO> todayOrderPriceList = CollectionUtils.filterList(orderPrices,
                o -> o.getPriceDate().equals(LocalDate.now()) || o.getPriceDate().equals(LocalDate.now().minusDays(1)));
        List<OrderPriceDO> distinct = CollectionUtils.distinct(todayOrderPriceList, OrderPriceDO::getOrderNo);
        Map<String, OrderPriceDO> todayOrderPriceMap = CollectionUtils.convertMap(distinct, OrderPriceDO::getOrderNo);

        // 获得所有在住客人账务
        List<AccountDO> accountList = getAccountList(new AccountListReqVO().setGcode(gcode).setHcode(hcode).setNos(orderNoList));
        Map<String, List<AccountDO>> accountListMap = CollectionUtils.convertMultiMap(accountList, AccountDO::getTogetherCode);

        // 获得预授权科目
        List<DictDataRespDTO> preDictData = dictDataApi.getDictDataListByDictTypeAndcssClass(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_PAY_ACCOUNT_PRE.getCode()).getData();
        List<String> preCodes = CollectionUtils.convertList(preDictData, DictDataRespDTO::getCode);

        List<AccountReminderRespVO> reminderRespVOs = new ArrayList<>();
        Set<String> processedTeamCodes = new HashSet<>(); // 记录已处理的团队代码

        for (OrderDO orderDO : orderList) {
            OrderTogetherDO mainTogetherDO = mainOrderTogetherMap.get(orderDO.getOrderNo());
            if (ObjectUtil.isEmpty(mainTogetherDO)) {
                continue;
            }

            // 判断订单类型
            String type = determineOrderType(orderDO);
            String typeName = getOrderTypeName(type);

            // 获取当天房价
            OrderPriceDO todayPrice = todayOrderPriceMap.get(orderDO.getOrderNo());
            Long price = todayPrice != null ? todayPrice.getVipPrice() : 0L;

            // 计算账务费用
            List<AccountDO> orderAccounts = accountListMap.getOrDefault(mainTogetherDO.getTogetherCode(), new ArrayList<>());
            AccountFeeCalculation feeCalc = calculateAccountFees(orderAccounts, preCodes);

            // 计算需加收房费和预计欠费
            Long addRoomFee = calculateAddRoomFee(orderDO, todayPrice);
            Long arrearsFee = feeCalc.getConsumeFee() - feeCalc.getPayFee() - feeCalc.getPreFee() + addRoomFee;

            if (NumberEnum.FOUR.getNumber().equals(type)) {
                // 团队类型：创建团队成员催账对象 + 团队主账催账对象（每个团队只创建一次）
                // 1. 为团队主账创建催账对象（只为每个团队创建一次）
                String teamCode = orderDO.getTeamCode();
                if (StrUtil.isNotBlank(teamCode) && !processedTeamCodes.contains(teamCode)) {
                    AccountReminderRespVO teamMainReminderRespVO = createTeamMainReminderRespVO(
                            orderDO, mainTogetherDO, type, typeName,
                            accountListMap, memberMap, agentMap, preCodes);
                    reminderRespVOs.add(teamMainReminderRespVO);
                    processedTeamCodes.add(teamCode); // 标记该团队已处理
                }

                // 2. 为团队成员创建催账对象
                AccountReminderRespVO memberReminderRespVO = createReminderRespVO(
                        orderDO, mainTogetherDO, type, typeName, price,
                        feeCalc, addRoomFee, arrearsFee, memberMap, agentMap, NumberEnum.ZERO.getNumber());
                reminderRespVOs.add(memberReminderRespVO);
            } else {
                AccountReminderRespVO reminderRespVO = createReminderRespVO(
                        orderDO, mainTogetherDO, type, typeName, price,
                        feeCalc, addRoomFee, arrearsFee, memberMap, agentMap, NumberEnum.ZERO.getNumber());
                reminderRespVOs.add(reminderRespVO);
            }

            if (NumberEnum.ONE.getNumber().equals(isShowTogether)) {
                List<OrderTogetherDO> orderTogetherDOS = nonMainOrderTogetherMap.get(orderDO.getOrderNo());
                if (CollUtil.isEmpty(orderTogetherDOS)) {
                    continue;
                }
                orderTogetherDOS.forEach(togetherDO -> {
                    // 计算账务费用
                    List<AccountDO> togetherOrderAccounts = accountListMap.getOrDefault(togetherDO.getTogetherCode(), new ArrayList<>());
                    AccountFeeCalculation togetherFeeCalc = calculateAccountFees(togetherOrderAccounts, preCodes);

                    // 计算需加收房费和预计欠费
                    Long togetherArrearsFee = togetherFeeCalc.getConsumeFee() - togetherFeeCalc.getPayFee() - togetherFeeCalc.getPreFee();

                    AccountReminderRespVO reminderRespVO = createReminderRespVO(
                            orderDO, togetherDO, type, typeName, price,
                            togetherFeeCalc, addRoomFee, togetherArrearsFee, memberMap, agentMap, isShowTogether);
                    reminderRespVOs.add(reminderRespVO);
                });
            }

        }

        return reminderRespVOs;
    }

    /**
     * 创建催账响应对象
     *
     * @param orderDO    订单信息
     * @param togetherDO 同住人信息
     * @param type       订单类型代码（1:单订单, 2:单房间同住订单, 3:联房, 4:团队）
     * @param typeName   订单类型名称
     * @param price      当天房价
     * @param feeCalc    账务费用计算结果（包含消费费用、付款费用、预付费用）
     * @param addRoomFee 需加收房费
     * @param arrearsFee 预计欠费
     * @param memberMap  会员信息映射表（key: 会员代码, value: 会员信息）
     * @param agentMap   中介信息映射表（key: 中介代码, value: 中介信息）
     * @return 催账响应对象
     */
    private AccountReminderRespVO createReminderRespVO(OrderDO orderDO, OrderTogetherDO togetherDO,
                                                       String type, String typeName, Long price,
                                                       AccountFeeCalculation feeCalc, Long addRoomFee, Long arrearsFee,
                                                       Map<String, MemberAndStoreCardRespDTO> memberMap,
                                                       Map<String, ProtocolAgentDO> agentMap, String isShowTogether) {
        AccountReminderRespVO reminderRespVO = new AccountReminderRespVO();
        reminderRespVO.setType(type);
        reminderRespVO.setTypeName(typeName);
        reminderRespVO.setOrderNo(orderDO.getOrderNo());
        reminderRespVO.setTogetherCode(togetherDO.getTogetherCode());
        reminderRespVO.setName(togetherDO.getName());
        reminderRespVO.setRNo(togetherDO.getRNo());
        reminderRespVO.setCheckinTime(orderDO.getCheckinTime());
        reminderRespVO.setPlanCheckoutTime(orderDO.getPlanCheckoutTime());
        reminderRespVO.setCheckinType(orderDO.getCheckinType());
        reminderRespVO.setCheckinTypeName(CheckInTypeEnum.getNameByCode(orderDO.getCheckinType()));
        reminderRespVO.setGuestSrcType(orderDO.getGuestSrcType());
        reminderRespVO.setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(orderDO.getGuestSrcType()));
        reminderRespVO.setPrice(price);
        reminderRespVO.setConsumeFee(feeCalc.getConsumeFee());
        reminderRespVO.setPayFee(feeCalc.getPayFee());
        reminderRespVO.setPreFee(feeCalc.getPreFee());
        reminderRespVO.setArrearsFee(arrearsFee);
        reminderRespVO.setTeamCode(orderDO.getTeamCode());
        reminderRespVO.setBindCode(orderDO.getBindCode());
        reminderRespVO.setCreateTime(orderDO.getCreateTime());

        if (NumberEnum.ONE.getNumber().equals(isShowTogether)) {
            reminderRespVO.setAddRoomFee(0L);
            reminderRespVO.setIsMain(NumberEnum.ZERO.getNumber());
        } else {
            reminderRespVO.setAddRoomFee(addRoomFee);
            reminderRespVO.setIsMain(togetherDO.getIsMain());
        }

        // 设置会员级别或公司名称
        setLevelOrCompanyName(reminderRespVO, orderDO, memberMap, agentMap);

        return reminderRespVO;
    }

    /**
     * 创建团队主账催账响应对象
     *
     * @param orderDO        订单信息
     * @param togetherDO     同住人信息（用于获取房号等基础信息）
     * @param type           订单类型代码（4:团队）
     * @param typeName       订单类型名称
     * @param accountListMap 账务信息映射表（key: 订单号或团队代码, value: 账务列表）
     * @param memberMap      会员信息映射表（key: 会员代码, value: 会员信息）
     * @param agentMap       中介信息映射表（key: 中介代码, value: 中介信息）
     * @param preCodes       预付科目代码列表
     * @return 团队主账催账响应对象
     */
    private AccountReminderRespVO createTeamMainReminderRespVO(OrderDO orderDO, OrderTogetherDO togetherDO,
                                                               String type, String typeName,
                                                               Map<String, List<AccountDO>> accountListMap,
                                                               Map<String, MemberAndStoreCardRespDTO> memberMap,
                                                               Map<String, ProtocolAgentDO> agentMap, List<String> preCodes) {
        AccountReminderRespVO reminderRespVO = new AccountReminderRespVO();

        // 基础信息设置
        reminderRespVO.setType(type);
        reminderRespVO.setTypeName(typeName);
        reminderRespVO.setOrderNo(orderDO.getTeamCode()); // 订单号设为团队代码
        reminderRespVO.setTogetherCode(orderDO.getTeamCode()); // 宾客代码也设为团队代码
        reminderRespVO.setName("团队主账"); // 名称为团队主账
        reminderRespVO.setRNo(togetherDO.getRNo()); // 使用房号
        reminderRespVO.setCheckinTime(orderDO.getCheckinTime());
        reminderRespVO.setPlanCheckoutTime(orderDO.getPlanCheckoutTime());
        reminderRespVO.setCheckinType(orderDO.getCheckinType());
        reminderRespVO.setCheckinTypeName(CheckInTypeEnum.getNameByCode(orderDO.getCheckinType()));
        reminderRespVO.setGuestSrcType(orderDO.getGuestSrcType());
        reminderRespVO.setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(orderDO.getGuestSrcType()));

        // 团队主账特殊设置：房价、需加收房费、预计欠费都设为0
        reminderRespVO.setPrice(0L);
        reminderRespVO.setAddRoomFee(0L);
        reminderRespVO.setArrearsFee(0L);

        // 计算团队主账的账务费用（使用团队代码查找账务）
        List<AccountDO> teamAccounts = accountListMap.getOrDefault(orderDO.getTeamCode(), new ArrayList<>());
        if (CollUtil.isNotEmpty(teamAccounts)) {
            AccountFeeCalculation teamFeeCalc = calculateAccountFees(teamAccounts, preCodes);
            reminderRespVO.setConsumeFee(teamFeeCalc.getConsumeFee());
            reminderRespVO.setPayFee(teamFeeCalc.getPayFee());
            reminderRespVO.setPreFee(teamFeeCalc.getPreFee());
        } else {
            // 没有团队账务时，费用都设为0
            reminderRespVO.setConsumeFee(0L);
            reminderRespVO.setPayFee(0L);
            reminderRespVO.setPreFee(0L);
        }

        reminderRespVO.setTeamCode(orderDO.getTeamCode());
        reminderRespVO.setBindCode(orderDO.getBindCode());
        reminderRespVO.setCreateTime(orderDO.getCreateTime());

        // 设置会员级别或公司名称
        setLevelOrCompanyName(reminderRespVO, orderDO, memberMap, agentMap);

        return reminderRespVO;
    }

    /**
     * 判断订单类型
     */
    private String determineOrderType(OrderDO orderDO) {
        if (OrderTypeEnum.GROUP.getCode().equals(orderDO.getOrderType())) {
            return NumberEnum.FOUR.getNumber(); // 团队
        } else if (OrderTypeEnum.JOIN.getCode().equals(orderDO.getOrderType())) {
            return NumberEnum.THREE.getNumber(); // 联房
        } else {
            return NumberEnum.ONE.getNumber(); // 单订单
        }
    }

    /**
     * 获取订单类型名称
     */
    private String getOrderTypeName(String type) {
        return switch (type) {
            case "1" -> "单订单";
            case "2" -> "单房间同住订单";
            case "3" -> "联房";
            case "4" -> "团队";
            default -> "未知";
        };
    }

    /**
     * 计算账务费用
     */
    private AccountFeeCalculation calculateAccountFees(List<AccountDO> accounts,
                                                       List<String> preCodes) {
        long consumeFee = 0L;
        long payFee = 0L;
        long preFee = 0L;

        for (AccountDO account : accounts) {

            if (DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(account.getSubType())) {
                // 消费
                consumeFee += account.getFee();
            } else {
                // 付款
                if (preCodes.contains(account.getSubCode())) {
                    if (AccountStatusEnum.UNCLOSED.getCode().equals(account.getState())) {
                        // 预授权
                        preFee += account.getFee();
                    }
                } else {
                    // 普通付款
                    payFee += account.getFee();
                }
            }
        }

        return new AccountFeeCalculation(consumeFee, payFee, preFee);
    }

    /**
     * 计算需加收房费
     */
    private Long calculateAddRoomFee(OrderDO orderDO, OrderPriceDO todayPrice) {
        // 如果今天有房价且客人还在住，可能需要加收房费
        if (todayPrice != null && OrderStateEnum.CHECK_IN.getCode().equals(orderDO.getState())) {
            // 这里可以根据业务逻辑计算需要加收的房费
            // 暂时返回0，具体逻辑需要根据业务需求实现
            return todayPrice.getVipPrice();
        }
        return 0L;
    }

    /**
     * 设置会员级别或公司名称
     */
    private void setLevelOrCompanyName(AccountReminderRespVO reminderRespVO, OrderDO orderDO,
                                       Map<String, MemberAndStoreCardRespDTO> memberMap,
                                       Map<String, ProtocolAgentDO> agentMap) {
        if (GuestSrcTypeEnum.MEMBER.getCode().equals(orderDO.getGuestSrcType())) {
            String mtName = null;
            if (orderDO.getGuestCode() != null && memberMap.get(orderDO.getGuestCode()) != null) {
                mtName = memberMap.getOrDefault(orderDO.getGuestCode(), new MemberAndStoreCardRespDTO()).getMtName();
            }
            reminderRespVO.setLevelOrCompanyName(mtName);
        }
        if (GuestSrcTypeEnum.AGENT.getCode().equals(orderDO.getGuestSrcType())) {
            reminderRespVO.setLevelOrCompanyName(agentMap.getOrDefault(orderDO.getGuestCode(), new ProtocolAgentDO()).getPaName());
        }
    }

    /**
     * 账务费用计算结果
     */
    private static class AccountFeeCalculation {
        private final Long consumeFee;
        private final Long payFee;
        private final Long preFee;

        public AccountFeeCalculation(Long consumeFee, Long payFee, Long preFee) {
            this.consumeFee = consumeFee;
            this.payFee = payFee;
            this.preFee = preFee;
        }

        public Long getConsumeFee() {
            return consumeFee;
        }

        public Long getPayFee() {
            return payFee;
        }

        public Long getPreFee() {
            return preFee;
        }
    }

    private static AccountStatRefundRespVO buildAccountStatRefundRespVO(AccountDO account, long refundFee) {
        return new AccountStatRefundRespVO().setFee(account.getFee())
                .setRefundedAmount(-refundFee).setAvailableAmount(account.getFee() + refundFee)
                .setSubCode(account.getSubCode()).setSubName(PayAccountEnum.getLabelByCode(account.getSubCode()));
    }

    /**
     * 验证退款
     *
     * @param reqVO   退款对象
     * @param bizDate 营业日期
     * @return 账务列表
     */
    private List<AccountDO> validateRefundAccount(RefundAccountReqVO reqVO, LocalDate bizDate) {
        List<AccountDO> accountList = accountMapper.selectList(AccountDO::getAccNo, reqVO.getAccNoList());
        AccountStatRefundRespVO accountStatRefund = getAccountStatRefund(new AccountChooseReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setAccNoList(reqVO.getAccNoList()));
        if (CollUtil.isEmpty(accountList) || accountList.size() != reqVO.getAccNoList().size()) {
            throw exception(ACCOUNT_RED_ERROR_ACCOUNT);
        }
        if (reqVO.getFee() < 0) {
            throw exception(REFUND_AMOUNT_NEGATIVE);
        }
        accountList.forEach(account -> {
            // 如果账务状态不是未结且则不能退款
            if (!AccountStatusEnum.UNCLOSED.getCode().equals(account.getState())) {
                throw exception(ACCOUNT_REFUND_ERROR_ACCOUNT);
            }
            // 判断是否是扫码付相关账务，如果不是，则不允许退款
            if (!isScanGunPayment(account.getSubCode())) {
                throw exception(ACCOUNT_REFUND_ERROR_ACCOUNT2);
            }
            if (reqVO.getFee() > accountStatRefund.getAvailableAmount()) {
                throw exception(ACCOUNT_REFUND_ERROR_ACCOUNT3);
            }
        });
        return accountList;
    }

    // 判断是否属于扫码付相关的账务
    private boolean isScanGunPayment(String subCode) {
        return SCAN_GUN_WX.getCode().equals(subCode) ||
                SCAN_GUN_ALIPAY.getCode().equals(subCode);
    }

    /**
     * 交班报表(收付实现制)
     */
    @Override
    public HandoverReportCashRealizationRespVO handoverReportCashRealization(HandoverReportCashRealizationReqVO reqVO) {
        HandoverReportCashRealizationRespVO handoverReportCashRealizationRespVO = new HandoverReportCashRealizationRespVO();

        // 获取门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        handoverReportCashRealizationRespVO.setHname(merchant.getHname());
        handoverReportCashRealizationRespVO.setHcode(reqVO.getHcode());
        handoverReportCashRealizationRespVO.setShiftNo(reqVO.getShiftNo());
        handoverReportCashRealizationRespVO.setBizDate(reqVO.getBizDate());
        handoverReportCashRealizationRespVO.setLastSelectTime(LocalDateTime.now());
        handoverReportCashRealizationRespVO.setOperator(reqVO.getOperator());

        // 查询账务数据
        List<AccountDO> accounts = getListForHandoverCashRealization(new AccountListReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setBizDate(reqVO.getBizDate()).setShiftNo(reqVO.getShiftNo()).setPayShiftNo(reqVO.getShiftNo()));

        // 一次遍历完成所有分类，避免多次循环
        List<AccountDO> paymentAccounts = new ArrayList<>();
        List<AccountDO> consumeAccounts = new ArrayList<>();
        List<AccountDO> currentShiftPaymentAccounts = new ArrayList<>();
        List<AccountDO> handoverAccounts = new ArrayList<>();

        String currentShiftNo = reqVO.getShiftNo();
        String payAccountType = DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode();
        String consumeAccountType = DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode();

        for (AccountDO account : accounts) {
            String subType = account.getSubType();

            // 分类付款账务和消费账务
            if (payAccountType.equals(subType)) {
                paymentAccounts.add(account);

                // 同时判断是否为本班付款金额
                if (currentShiftNo.equals(account.getShiftNo())) {
                    currentShiftPaymentAccounts.add(account);
                }

                // 同时判断是否为上缴金额的账务
                if (currentShiftNo.equals(account.getPayShiftNo())) {
                    handoverAccounts.add(account);
                }
            } else if (consumeAccountType.equals(subType)) {
                consumeAccounts.add(account);
            }
        }

        // 1. 获取上班留存数据（从rp_handover_report表查询上一班的数据）同时判断该班次是否已经交班
        HandoverReportCashRealizationRespVO.HandoverAmountDetail previousShiftAmount = getPreviousShiftAmount(reqVO, handoverReportCashRealizationRespVO);

        // 2. 计算本班收款数据（使用accounts - 本班收款的账务）
        HandoverReportCashRealizationRespVO.HandoverAmountDetail currentShiftAmount = calculateAmountFromAccounts(currentShiftPaymentAccounts);

        // 3. 计算本班合计存手头金额（上班留存 + 本班收款）
        HandoverReportCashRealizationRespVO.HandoverAmountDetail totalAmount = calculateTotalAmount(previousShiftAmount, currentShiftAmount);

        // 4. 计算上缴金额数据（使用handoverAccounts - 上缴金额的账务）
        HandoverReportCashRealizationRespVO.HandoverAmountDetail handoverAmount = calculateAmountFromAccounts(handoverAccounts);

        // 5. 计算本班留存（本班合计 - 上缴金额）
        HandoverReportCashRealizationRespVO.HandoverAmountDetail remainingAmount = calculateRemainingAmount(totalAmount, handoverAmount);

        // 6. 设置交班明细
        HandoverReportCashRealizationRespVO.HandoverDetail handoverDetail = new HandoverReportCashRealizationRespVO.HandoverDetail();
        handoverDetail.setPreviousShiftAmount(previousShiftAmount);
        handoverDetail.setCurrentShiftAmount(currentShiftAmount);
        handoverDetail.setTotalAmount(totalAmount);
        handoverDetail.setHandoverAmount(handoverAmount);
        handoverDetail.setRemainingAmount(remainingAmount);

        handoverReportCashRealizationRespVO.setHandoverDetail(handoverDetail);


        // 7. 获取消费科目和付款科目明细
        // 获取科目名称映射
        Map<String, String> dictMap = dictDataApi.getDictDataLabelMap(List.of(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode()));

        handoverReportCashRealizationRespVO.setConsumeSubjects(getConsumeOrPaymentSubjectsFromAccounts(consumeAccounts, dictMap, reqVO.getShiftNo()));
        handoverReportCashRealizationRespVO.setPaymentSubjects(getConsumeOrPaymentSubjectsFromAccounts(paymentAccounts, dictMap, reqVO.getShiftNo()));

        return handoverReportCashRealizationRespVO;
    }

    private List<AccountDO> getListForHandoverCashRealization(AccountListReqVO accountListReqVO) {
        return accountMapper.selectListForHandoverCashRealization(accountListReqVO);
    }

    /**
     * 获取上班留存数据（从rp_handover_report表查询上一班的数据）
     */
    private HandoverReportCashRealizationRespVO.HandoverAmountDetail getPreviousShiftAmount(HandoverReportCashRealizationReqVO reqVO,
                                                                                            HandoverReportCashRealizationRespVO handoverReportCashRealizationRespVO) {
        // 调用report模块的API查询上一班的交班报表数据
        HandoverReportCashRealizationRespVO.HandoverAmountDetail amount = new HandoverReportCashRealizationRespVO.HandoverAmountDetail();

        // 根据当前班次获得上个班次的数据
        // 如果当前班次没有查询到，代表数据库中最后一条记录为上个班次
        // 如果当前班次查询到了，则查询出的这条记录的上条记录为上个班次
        // 注意：当前班次和上个班次有可能会出现跨营业日的可能性（因为夜审会强制换班）
        HandoverReportRespDTO previousReport = null;

        // 先查询当前营业日的交班报表数据
        List<HandoverReportRespDTO> currentDayReports = handoverReportApi.getHandoverReportListByBizDate(
                reqVO.getGcode(), reqVO.getHcode(), reqVO.getBizDate()).getData();

        // 先标为未交班
        handoverReportCashRealizationRespVO.setHandoverStatus(BooleanEnum.FALSE.getValue());

        if (CollUtil.isNotEmpty(currentDayReports)) {
            // 查找当前班次的记录
            HandoverReportRespDTO currentReport = null;
            for (HandoverReportRespDTO report : currentDayReports) {
                if (reqVO.getShiftNo().equals(report.getShiftNo())) {
                    currentReport = report;
                    handoverReportCashRealizationRespVO.setHandoverStatus(BooleanEnum.TRUE.getValue());
                    break;
                }
            }

            if (currentReport == null) {
                // 如果当前班次没有查询到，代表数据库中最后一条记录为上个班次
                previousReport = currentDayReports.getLast();
            } else {
                // 如果当前班次查询到了，则查询出的这条记录的上条记录为上个班次
                int currentIndex = currentDayReports.indexOf(currentReport);
                if (currentIndex > 0) {
                    // 上个班次在同一营业日
                    previousReport = currentDayReports.get(currentIndex - 1);
                } else {
                    // 当前记录是第一条，上个班次可能在前一营业日（跨营业日情况）
                    List<HandoverReportRespDTO> previousDayReports = handoverReportApi.getHandoverReportListByBizDate(
                            reqVO.getGcode(), reqVO.getHcode(), reqVO.getBizDate().minusDays(1)).getData();

                    if (CollUtil.isNotEmpty(previousDayReports)) {
                        // 取前一营业日的最后一条记录作为上个班次
                        previousReport = previousDayReports.getLast();
                    }
                }
            }
        } else {
            // 当前营业日没有数据，查询前一营业日的最后一条记录
            List<HandoverReportRespDTO> previousDayReports = handoverReportApi.getHandoverReportListByBizDate(
                    reqVO.getGcode(), reqVO.getHcode(), reqVO.getBizDate().minusDays(1)).getData();

            if (CollUtil.isNotEmpty(previousDayReports)) {
                previousReport = previousDayReports.getLast();
            }
        }

        // 如果找到了上个班次的数据，设置各支付方式的金额
        if (previousReport != null) {
            amount.setCashAmount(previousReport.getCashAmount() != null ? previousReport.getCashAmount() : 0L);
            amount.setBankCardAmount(previousReport.getBankCardAmount() != null ? previousReport.getBankCardAmount() : 0L);
            amount.setWechatAmount(previousReport.getWechatAmount() != null ? previousReport.getWechatAmount() : 0L);
            amount.setAlipayAmount(previousReport.getAlipayAmount() != null ? previousReport.getAlipayAmount() : 0L);
        }

        // 计算合计
        amount.calculateTotal();

        return amount;
    }

    /**
     * 获取本班收款数据（accounts - 本班收款的账务）
     */
    private HandoverReportCashRealizationRespVO.HandoverAmountDetail getCurrentShiftAmount(HandoverReportCashRealizationReqVO reqVO) {
        // 构建查询条件 - 查询本班收款的账务数据
        PayOrConsumeDetailReqVO accountReqVO = new PayOrConsumeDetailReqVO();
        accountReqVO.setGcode(reqVO.getGcode());
        accountReqVO.setHcode(reqVO.getHcode());
        accountReqVO.setBizDate(reqVO.getBizDate());
        accountReqVO.setShiftNo(reqVO.getShiftNo());
        accountReqVO.setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode()); // 付款科目

        // 查询本班收款的账务数据
        List<AccountDO> accounts = getPayOrConsumeAccountList(accountReqVO);

        HandoverReportCashRealizationRespVO.HandoverAmountDetail amount = new HandoverReportCashRealizationRespVO.HandoverAmountDetail();

        if (accounts != null && !accounts.isEmpty()) {
            // 按支付方式分类统计
            for (AccountDO account : accounts) {
                String subCode = account.getSubCode();
                Long fee = account.getFee();

                // 根据PayAccountEnum判断支付方式
                if (isCashPayment(subCode)) {
                    // 现金类：人民币付款、人民币押金、人民币退款
                    amount.setCashAmount(amount.getCashAmount() + fee);
                } else if (isBankCardPayment(subCode)) {
                    // 银行卡类
                    amount.setBankCardAmount(amount.getBankCardAmount() + fee);
                } else if (isWechatPayment(subCode)) {
                    // 微信类
                    amount.setWechatAmount(amount.getWechatAmount() + fee);
                } else if (isAlipayPayment(subCode)) {
                    // 支付宝类
                    amount.setAlipayAmount(amount.getAlipayAmount() + fee);
                }
            }
        }

        // 计算合计
        amount.calculateTotal();

        return amount;
    }

    /**
     * 计算本班合计存手头金额
     */
    private HandoverReportCashRealizationRespVO.HandoverAmountDetail calculateTotalAmount(
            HandoverReportCashRealizationRespVO.HandoverAmountDetail previousAmount,
            HandoverReportCashRealizationRespVO.HandoverAmountDetail currentAmount) {

        HandoverReportCashRealizationRespVO.HandoverAmountDetail totalAmount = new HandoverReportCashRealizationRespVO.HandoverAmountDetail();

        totalAmount.setCashAmount(previousAmount.getCashAmount() + currentAmount.getCashAmount());
        totalAmount.setBankCardAmount(previousAmount.getBankCardAmount() + currentAmount.getBankCardAmount());
        totalAmount.setWechatAmount(previousAmount.getWechatAmount() + currentAmount.getWechatAmount());
        totalAmount.setAlipayAmount(previousAmount.getAlipayAmount() + currentAmount.getAlipayAmount());

        // 计算合计
        totalAmount.calculateTotal();

        return totalAmount;
    }

    /**
     * 获取上缴金额数据（handoverAccounts - 上缴金额的账务）
     */
    private HandoverReportCashRealizationRespVO.HandoverAmountDetail getHandoverAmount(HandoverReportCashRealizationReqVO reqVO) {
        // 构建查询条件 - 查询上缴金额的账务数据
        PayOrConsumeDetailReqVO accountReqVO = new PayOrConsumeDetailReqVO();
        accountReqVO.setGcode(reqVO.getGcode());
        accountReqVO.setHcode(reqVO.getHcode());
        accountReqVO.setBizDate(reqVO.getBizDate());
        accountReqVO.setShiftNo(reqVO.getShiftNo());
        accountReqVO.setSubType("1"); // 付款科目
        // 这里可能需要添加特定的查询条件来区分上缴金额的账务
        // 例如：accountReqVO.setAccountType("handover"); // 如果有特定的账务类型标识

        // 查询上缴金额的账务数据
        List<AccountDO> handoverAccounts = getPayOrConsumeAccountList(accountReqVO);

        HandoverReportCashRealizationRespVO.HandoverAmountDetail amount = new HandoverReportCashRealizationRespVO.HandoverAmountDetail();

        if (handoverAccounts != null && !handoverAccounts.isEmpty()) {
            // 按支付方式分类统计上缴金额
            for (AccountDO account : handoverAccounts) {
                String subCode = account.getSubCode();
                Long fee = account.getFee();

                // 根据PayAccountEnum判断支付方式
                if (isCashPayment(subCode)) {
                    // 现金类：人民币付款、人民币押金、人民币退款
                    amount.setCashAmount(amount.getCashAmount() + fee);
                } else if (isBankCardPayment(subCode)) {
                    // 银行卡类
                    amount.setBankCardAmount(amount.getBankCardAmount() + fee);
                } else if (isWechatPayment(subCode)) {
                    // 微信类
                    amount.setWechatAmount(amount.getWechatAmount() + fee);
                } else if (isAlipayPayment(subCode)) {
                    // 支付宝类
                    amount.setAlipayAmount(amount.getAlipayAmount() + fee);
                }
            }
        }

        // 计算合计
        amount.calculateTotal();

        return amount;
    }

    /**
     * 计算本班留存（本班合计 - 上缴金额）
     */
    private HandoverReportCashRealizationRespVO.HandoverAmountDetail calculateRemainingAmount(
            HandoverReportCashRealizationRespVO.HandoverAmountDetail totalAmount,
            HandoverReportCashRealizationRespVO.HandoverAmountDetail handoverAmount) {

        HandoverReportCashRealizationRespVO.HandoverAmountDetail remainingAmount = new HandoverReportCashRealizationRespVO.HandoverAmountDetail();

        remainingAmount.setCashAmount(totalAmount.getCashAmount() - handoverAmount.getCashAmount());
        remainingAmount.setBankCardAmount(totalAmount.getBankCardAmount() - handoverAmount.getBankCardAmount());
        remainingAmount.setWechatAmount(totalAmount.getWechatAmount() - handoverAmount.getWechatAmount());
        remainingAmount.setAlipayAmount(totalAmount.getAlipayAmount() - handoverAmount.getAlipayAmount());

        // 计算合计
        remainingAmount.calculateTotal();

        return remainingAmount;
    }

    /**
     * 根据账务数据计算各支付方式金额
     */
    private HandoverReportCashRealizationRespVO.HandoverAmountDetail calculateAmountFromAccounts(List<AccountDO> accounts) {
        HandoverReportCashRealizationRespVO.HandoverAmountDetail amount = new HandoverReportCashRealizationRespVO.HandoverAmountDetail();

        if (accounts != null && !accounts.isEmpty()) {
            // 按支付方式分类统计
            for (AccountDO account : accounts) {
                String subCode = account.getSubCode();
                Long fee = account.getFee();

                // 根据PayAccountEnum判断支付方式
                if (isCashPayment(subCode)) {
                    // 现金类：人民币付款、人民币押金、人民币退款
                    amount.setCashAmount(amount.getCashAmount() + fee);
                } else if (isBankCardPayment(subCode)) {
                    // 银行卡类
                    amount.setBankCardAmount(amount.getBankCardAmount() + fee);
                } else if (isWechatPayment(subCode)) {
                    // 微信类
                    amount.setWechatAmount(amount.getWechatAmount() + fee);
                } else if (isAlipayPayment(subCode)) {
                    // 支付宝类
                    amount.setAlipayAmount(amount.getAlipayAmount() + fee);
                }
            }
        }

        // 计算合计
        amount.calculateTotal();

        return amount;
    }


    /**
     * 判断是否为现金支付（人民币付款、人民币押金、人民币退款）
     */
    private boolean isCashPayment(String subCode) {
        return PayAccountEnum.RMB_RECEIPT.getCode().equals(subCode) ||
                PayAccountEnum.RMB_DEPOSIT.getCode().equals(subCode) ||
                PayAccountEnum.CASH_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.VND_RECEIPT.getCode().equals(subCode) ||
                PayAccountEnum.VND_REFUND.getCode().equals(subCode);
    }

    /**
     * 判断是否为银行卡支付
     */
    private boolean isBankCardPayment(String subCode) {
        return PayAccountEnum.BANK_CARD.getCode().equals(subCode) ||
                PayAccountEnum.BANK_CARD_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.BANK_TRANSFER.getCode().equals(subCode) ||
                PayAccountEnum.BANK_TRANSFER_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.CLOUD_QUICK_PAY.getCode().equals(subCode) ||
                PayAccountEnum.CLOUD_QUICK_PAY_REFUND.getCode().equals(subCode);
    }

    /**
     * 判断是否为微信支付
     */
    private boolean isWechatPayment(String subCode) {
        return PayAccountEnum.WX.getCode().equals(subCode) ||
                PayAccountEnum.WX_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.SCAN_GUN_WX.getCode().equals(subCode) ||
                PayAccountEnum.SCAN_GUN_WX_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.MEITUAN_WX_QR_PAY.getCode().equals(subCode) ||
                PayAccountEnum.MEITUAN_WX_REFUND.getCode().equals(subCode);
    }

    /**
     * 判断是否为支付宝支付
     */
    private boolean isAlipayPayment(String subCode) {
        return PayAccountEnum.ALIPAY.getCode().equals(subCode) ||
                PayAccountEnum.ALIPAY_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.ALIPAY_DEPOSIT.getCode().equals(subCode) ||
                PayAccountEnum.SCAN_GUN_ALIPAY.getCode().equals(subCode) ||
                PayAccountEnum.SCAN_GUN_ALIPAY_REFUND.getCode().equals(subCode) ||
                PayAccountEnum.MEITUAN_ALIPAY_QR_PAY.getCode().equals(subCode) ||
                PayAccountEnum.MEITUAN_ALIPAY_REFUND.getCode().equals(subCode);
    }

    /**
     * 根据账务数据获取消费或付款科目明细
     */
    private List<HandoverReportCashRealizationRespVO.SubjectDetail> getConsumeOrPaymentSubjectsFromAccounts(List<AccountDO> accounts, Map<String, String> dictMap, String shiftNo) {
        List<HandoverReportCashRealizationRespVO.SubjectDetail> subjectDetails = new ArrayList<>();

        if (CollUtil.isNotEmpty(accounts)) {
            // 一次遍历同时计算总金额和今日结账收回金额
            Map<String, Long> totalAmountMap = new HashMap<>();
            Map<String, Long> todaySettleAmountMap = new HashMap<>();
            Set<String> subCodeSet = new HashSet<>();

            // 单次遍历计算所有统计数据
            for (AccountDO account : accounts) {
                String subCode = account.getSubCode();
                Long fee = account.getFee();
                subCodeSet.add(subCode);

                // 今日发生金额
                if (shiftNo.equals(account.getShiftNo())) {
                    totalAmountMap.merge(subCode, fee, Long::sum);
                }

                // 如果结账班次等于当前班次，累加今日结账收回金额
                if (shiftNo.equals(account.getPayShiftNo())) {
                    todaySettleAmountMap.merge(subCode, fee, Long::sum);
                }
            }

            // 构建结果列表
           subCodeSet.forEach(subCode -> {
                HandoverReportCashRealizationRespVO.SubjectDetail subjectDetail = new HandoverReportCashRealizationRespVO.SubjectDetail();
                subjectDetail.setSubjectCode(subCode)
                        .setSubjectName(dictMap.getOrDefault(subCode, subCode))
                        .setAmount(totalAmountMap.getOrDefault(subCode, 0L))
                        .setTodaySettle(todaySettleAmountMap.getOrDefault(subCode, 0L));
                subjectDetails.add(subjectDetail);
            });
        }

        return subjectDetails;
    }

}