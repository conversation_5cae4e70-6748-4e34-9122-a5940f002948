package info.qizhi.aflower.module.pms.service.account;

import info.qizhi.aflower.framework.common.core.KeyValue;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.pay.api.base.dto.RefundRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.ScanRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.*;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordReqVO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordStatByBizDateRespVO;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.AccountChooseReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.PayOrConsumeDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.PaymentRecoveryDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.TransactionDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.TransferAccountReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import info.qizhi.aflower.module.pms.service.account.bo.OrderAccountStatBO;
import jakarta.validation.Valid;

import java.time.LocalDate;
import java.util.List;

/**
 * 账务 Service 接口
 *
 * <AUTHOR>
 */
public interface AccountService {

    /**
     * 创建账务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAccount(@Valid AccountSaveReqVO createReqVO);

    /**
     * 付款、预授权
     *
     * @param reqVo 入参
     * @return 账务号
     */
    String pay(PayAccountSaveReqVO reqVo);

    /**
     * 账务转移走
     *
     * @param accountDOS 入参
     * @return 账务号
     */
    void remove(List<AccountDO> accountDOS);

    /**
     * 扫码抢支付，并返回支付成功后的商户支付订单编号
     *
     * @param hcode     集团代码
     * @param payCode   支付码
     * @param price     付款金额
     * @param accDetail 账务明细
     * @param account   账务
     * @return 成功返回商户支付订单编号, 失败返回null
     */
    ScanRespDTO scanGunPay(String hcode, String payCode, Long price, String accDetail, AccountDO account, String platform);

    /**
     * 线上支付退款<br>
     * 条件：<br>
     * 1. 未结状态的账务<br>
     * 2. 科目为 scan_gun_alipay 或 scan_gun_wx<br>
     * 3. 账务日期不能超过60天<br>
     * 4. 金额必须大于0<br>
     *
     * @param account 需要退款的账务
     * @return 返回渠道退款单号
     */
    RefundRespDTO scanGunRefund(AccountDO account, Long fee);


    /**
     * 创建消费账务
     *
     * @param reqVo 入参
     */
    String consume(ConsumeAccountSaveReqVO reqVo);

    /**
     * 拆分账务<br>
     * 条件：<br>
     * 1. 未结账务<br>
     * 2. 早餐、会员卡科目不允许拆<br>
     * 3. 付款科目只支持现金账务拆分<br>
     * 过程: <br>
     * 1. 将原账务改为已结，同时添加一笔已结相反账务来平衡<br>
     * 2. 将拆分后的账务插入账务表，同时记录下拆分前的账务号<br>
     *
     * @param reqVO 入参
     */
    void splitAccount(SplitAccountReqVO reqVO);

    /**
     * 撤销结账操作<br>
     * 1. 不能跨营业日、不能跨班次撤销<br>
     * 2. 撤销时将原账务的付款信息置空<br>
     *
     * @param r 入参
     */
    void undoPay(CloseAccountPayNoRepVO r);

    /**
     * 获取已结账务<br>
     * 1. 结账不能跨营业日、不能跨班次撤销<br>
     * 2. 根据结账号返回批次<br>
     *
     * @param r 入参
     * @return 可撤销结账记录
     */
    List<CloseAccountRespVO> getCloseAccountList(CloseAccountRepVO r);

    /**
     * 根据结账号获取账务列表
     *
     * @param gcode 集团代码
     * @param hcode 门店代码
     * @param payNo 结账号
     * @return 已经结账的账务列表
     */
    List<CloseAccountPayNoRespVO> getAccountByPayNo(String gcode, String hcode, String payNo);

    /**
     * 根据不同的编号类型和编号获取对应的入账账号信息
     *
     * @param gcode  编号的字母前缀，用于区分不同的业务类型
     * @param hcode  编号的字母后缀，与gcode一起用于进一步的业务区分
     * @param noType 编号类型，决定了使用哪种业务逻辑来获取入账账号信息
     * @param no     实际的业务编号，根据noType的不同而有不同的意义
     * @return 返回一个TogetherAccRespVO对象，包含了入账账号的相关信息
     */
    List<TogetherAccRespVO> getRecordAccountList(String gcode, String hcode, String noType, List<String> no);

    /**
     * 获取在住的账号列表
     *
     * @param gcode
     * @param hcode
     * @return
     */
    @Deprecated
    List<TogetherAccRespVO> getRecordAccountList(String gcode, String hcode);

    /**
     * 转账
     *
     * @param reqVO 入参
     */
    void transferAccount(TransferOutAccountReqVO reqVO);

    /**
     * AR支付
     *
     * @param gcode   集团代码
     * @param payCode 支付代码
     * @param fee     金额
     * @return
     */
    KeyValue<String, List<String>> arPay(String gcode, String payCode, Long fee);

    /**
     * 部分结账功能主方法
     * 该方法负责验证部分结账请求的合法性，处理不同支付方式的结账逻辑，并更新账务状态
     * 使用分布式事务和锁机制确保操作的原子性和一致性
     *
     * @param reqVO 部分结账请求对象，包含结账所需的各种参数
     */
    void partCloseAccount(PartCloseAccountReqVO reqVO);

    /**
     * 根据账单号获取账务列表(结账、部分结账时获取账务数据)
     *
     * @param reqVO 入参
     * @return
     */
    PartCloseAccountRespVO getPartCloseAccount(AccountSelectPartCloseReqVO reqVO);


    /**
     * 更新账务备注
     *
     * @param reqVO 入参
     */
    void updateRemark(AccountRemarkSaveReqVO reqVO);

    /**
     * 冲红<br>
     * 条件：<br>
     * 1. 不能跨营业日冲红<br>
     * 2.
     *
     * @param req 入参
     */
    void redAccount(RedAccountReqVO req);

    /**
     * 验证冲调账务中是否存在超过1条的扫码付账务，超过抛出异常，不允许冲红<br>
     *
     * @param r 入参
     */
    void validateMoreRedScanGanAccount(AccountSelectedReqVO r);

    /**
     * 获取结账(挂账)退房宾客列表
     *
     * @param no     单号，根据订单类型 可能是 客单号、预订单号、团队代码
     * @param noType 订单类型  OrderTypeRespVO里的定义
     * @return OrderCheckOutRespVO
     */
    OrderCheckOutGuestRespVO getCheckOutGuestList(String no, String noType);


    OrderCheckOutGuestRespVO getCheckOutGuestList(String no, String noType, String all);

    /**
     * 统计退房账务
     *
     * @param r 入参
     */
    StatCheckOutAccountRespVO statCheckOutAccount(FinishCheckOutAccountReqVO r);

    /**
     * 结账退房<br>
     * 1. 如果输入付款金额与应付金额不相等，则当做一笔入账操作，完成后需要重新计算退房金额<br>
     * 2. 如果输入付款金额与应付金额相等，则当作是退房结账操作，修改相关账务状态和房态<br>
     *
     * @param reqVO 入参
     */
    void payCheckOut(PayCheckOutAccountReqVO reqVO);

    /**
     * 按照账号、账务类型进行分类统计消费、付款合计
     *
     * @param gcode   集团代码
     * @param hcode   门店代码
     * @param bizDate 营业日期
     * @return AccountStatByBizDateRespVO
     */
    List<AccountStatByBizDateRespVO> statAccountByBizDate(String gcode, String hcode, LocalDate bizDate);

    /**
     * 挂账退房<br>
     * 1. 账务不做操作<br>
     * 2. 将客单状态改为挂账<br>
     * 3. 如果订单下客单全部挂账，则将订单状态改为挂账,同时将订单关联的房间改为空脏<br>
     * 4. 如果团队主单进行挂账，则将团队状态改为挂账<br>
     *
     * @param r 入参
     */
    void creditCheckOut(@Valid ConfirmRoomFeeReqVO r);


    /**
     * 确认房费列表<br>
     * 条件：<br>
     * 1. 团队主账、非主客单不需要确认房费<br>
     *
     * @param reqVO 入参
     * @return List<ConfirmCheckOutAccountRespVO>
     */
    List<ConfirmCheckOutAccountRespVO> getConfirmRoomFeeList(ConfirmCheckOutAccountReqVO reqVO);

    /**
     * 确认预授权列表
     *
     * @param reqVO 入参
     * @return List<ConfirmCheckOutPreAuthRespVO>
     */
    List<ConfirmCheckOutPreAuthRespVO> getConfirmPreAuthList(ConfirmPreAuthListReqVO reqVO);

    /**
     * 确认生成房费
     *
     * @param reqVO 入参
     */
    void confirmRoomFee(ConfirmRoomFeeReqVO reqVO);

    /**
     * 确认预授权金额
     *
     * @param reqVO 入参
     */
    void confirmPreAuth(ConfirmPreAuthReqVO reqVO);

    /**
     * 取消预授权
     *
     * @param accNo 账务号
     */
    void cancelPreAuth(String accNo);

    /**
     * 批量插入账务
     */
    Boolean createAccounts(List<AccountSaveReqVO> createAccountList);

    /**
     * 批量插入账务
     *
     * @param accountDOList
     */
    void createAccountDOList(List<AccountDO> accountDOList);

    /**
     * 根据id批量修改账务
     *
     * @param accountDOList
     */
    void updateAccountByIds(List<AccountDO> accountDOList);

    /**
     * 撤销核销(AR账)
     *
     * @param accNo    账务号
     * @param isVerify 是否核销 0：否 1：是，这里是0
     */
    void revocation(String accNo, String isVerify);

    /**
     * 通过宾客代码统计账务
     *
     * @param togetherCode 宾客代码
     * @return OrderAccountStatBO
     */
    OrderAccountStatBO getOrderAccountStatByTogetherCode(String togetherCode);

    /**
     * 接待： 所有房间账务  no: 订单号列表, noType: orderList<br>
     * 团队接待：所有账务，包括房间和团队的账务  no: teamCode, noType: teamReception<br>
     * 团队主单：团队主单账务 no: teamCode, noType: teamMain<br>
     * 房间：房间下账务 no: orderNo, noType: room<br>
     * 客人：客人账务 no: orderNo (这里前端传的是togetherCode), noType: order<br>
     *
     * @param gcode  集团代码
     * @param hcode  酒店代码
     * @param noList 单号列表
     * @param noType 单号类型
     * @return
     */
    OrderAccountStatBO getOrderAccountStatByNo(String gcode, String hcode, List<String> noList, String noType);

    /**
     * 通过账号(入账账号)统计账务
     *
     * @param noList 账号(入账账号)列表
     * @return
     */
    OrderAccountStatBO getOrderAccountStatByNo(List<String> noList);

    /**
     * 统计多个单号下宾客消费、付款合计
     *
     * @param reqVO
     * @return
     */
    List<AccountStatRespVO> getOrderAccountStatByNos(AccountStatReqVO reqVO);

    /**
     * 获得房间账务列表
     *
     * @return 账务
     */
    List<AccountDO> getAccountList(AccountListReqVO reqVO);

    /**
     * 获得房间账务列表
     *
     * @return 账务
     */
    List<AccountDO> getSettleAccountList(SettleAccountListReqVO reqVO);

    /**
     * 获取现付账单账务分页
     *
     * @param reqVO
     * @return
     */
    PageResult<AccountDO> getCashBillAccountPage(AccRecordPageReqVO reqVO);

    /**
     * 获取现付账单账务列表
     *
     * @param reqVO
     * @return
     */
    List<AccountDO> getCashBillAccountList(AccRecordReqVO reqVO);

    /**
     * ar账明细报表获得房间账务列表
     *
     * @return 账务
     */
    List<AccountDO> getAccountListForReport(AccountListReqVO reqVO);

    /**
     * 账务明细报表获得列表
     *
     * @return 账务
     */
    List<AccountDO> getPayOrConsumeAccountList(PayOrConsumeDetailReqVO reqVO);

    /**
     * 付款(结账)收回明细报表获得列表 - 按结账营业日查询
     *
     * @return 账务
     */
    List<AccountDO> getCheckoutAccountList(PaymentRecoveryDetailReqVO reqVO);

    /**
     * 应收账获取账务分页
     *
     * @param pageReqVO 分页查询
     * @return 账务分页
     */
    PageResult<AccountDO> getAccountPageByArSet(AccountPageReqVO pageReqVO);

    /**
     * 获得账务科目列表(通过账单号查询)
     *
     * @param reqVO 条件
     * @return List<AccountSubRespVO>
     */
    List<AccountSubRespVO> getAccountSubList(AccountSubReqVO reqVO);

    /**
     * 获得账务分页(通过宾客代码查询)
     *
     * @param pageReqVO 分页查询
     * @return 账务分页
     */
    PageResult<AccountRespVO> getAccountPageByTogetherCodes(AccountPageReq2VO pageReqVO);

    /**
     * 获得账务分页(通过账单号查询)
     *
     * @param pageReqVO 分页查询
     * @return 账务分页
     */
    PageResult<AccountRespVO> getAccountPageByNos(AccountPageReq2VO pageReqVO);

    /**
     * 获得房间账务
     *
     * @return 账务
     */
    AccountDO getAccount(AccountReqVO reqVO);

    /**
     * 交班报表
     */
    HandoverReportRespVO handoverReport(HandoverReportReqVO reqVO);

    /**
     * 交班报表(收付实现制)
     */
    HandoverReportCashRealizationRespVO handoverReportCashRealization(HandoverReportCashRealizationReqVO reqVO);

    /**
     * 获取选中房间账务详情
     */
    List<AccountDO> getArSetRoomAccountList(AccountChooseReqVO reqVO);

    /**
     * 根据营业日统计现付账消费、付款合计
     *
     * @param gcode   集团代码
     * @param hcode   门店代码
     * @param bizDate 营业日期
     * @return 统计结果
     */
    List<AccRecordStatByBizDateRespVO> statAccRecordByBizDate(String gcode,
                                                              String hcode,
                                                              LocalDate bizDate);

    /**
     * 统计部分结账账务
     *
     * @param reqVO 入参
     */
    StatCheckOutAccountRespVO statCloseAccount(@Valid FinishCloseAccountReqVO reqVO);

    /**
     * 交易明细
     *
     * @param reqVO
     * @return
     */
    PageResult<AccountRespVO> getTransactionPage(TransactionPageReqVO reqVO);

    /**
     * 退款<br>
     *
     * @param req 入参
     */
    void refundAccount(@Valid RefundAccountReqVO req);

    /**
     * 统计退款金额
     *
     * @return
     */
    AccountStatRefundRespVO getAccountStatRefund(AccountChooseReqVO accountChooseReqVO);

    /**
     * 统计人民币押金
     *
     * @param reqVO
     * @return
     */
    DepositAccountRespVO getDeposit(AccountReqVO reqVO);

    /**
     * 补录
     *
     * @param
     * @return
     */
    String recordingAccount(RecordingAccountReqVO reqVO);

    /**
     * 交易明细报表
     *
     * @param reqVO
     * @return
     */
    List<AccountDO> getTransactionReport(TransactionDetailReqVO reqVO);

    /**
     * 判断是否还有AR账可挂
     *
     * @param reqVO
     */
    void judgeARAccount(@Valid FinishCheckOutAccountReqVO reqVO);

    /**
     * 查询丢失的账务
     *
     * @param reqVO
     * @return
     */
    RecordingAccountRespVO getRecordingAccount(@Valid RecordingAccountQueryReqVO reqVO);

    /**
     * 退货
     *
     * @param createReqVO
     */
    void refundGood(@Valid ConsumeRefundGoodReqVO createReqVO);

    /**
     * 获得可退货的商品数量
     *
     * @param reqVO
     * @return
     */
    List<ConsumeGoodRespVO> getRefundGood(@Valid ConsumeGoodReqVO reqVO);

    /**
     * 获得转账账务列表
     *
     * @return 账务
     */
    List<AccountDO> getAccountChangeList(TransferAccountReqVO reqVO);


    /**
     * 获得每日房费列表
     * @param reqVO
     * @return
     */
    List<AccountRoomFeeRespVO> getAccountRoomFeeList(@Valid AccountListReqVO reqVO);

    /**
     * 批量付款操作(暂时只适用优惠券类型)
     * @param createReqVO
     * @return
     */
    String batchPay(@Valid BatchPayAccountSaveReqVO createReqVO);

    /**
     * 获得催账列表
     *
     * @param pageReqVO
     * @return
     */
    List<AccountReminderRespVO> getReminderList(@Valid ReminderReqVO pageReqVO);

    /**
     * 统计预计欠费的订单数
     *
     * @param pageReqVO
     * @return
     */
    ReminderCountRespVO getReminderCount(@Valid ReminderReqVO pageReqVO);
}