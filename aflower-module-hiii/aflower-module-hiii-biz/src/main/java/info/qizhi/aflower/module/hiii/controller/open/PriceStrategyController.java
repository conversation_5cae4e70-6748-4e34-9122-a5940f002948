package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.pricestrategy.PriceStrategyBoardRespVO;
import info.qizhi.aflower.module.hiii.service.open.PriceStrategyService;
import info.qizhi.aflower.module.pms.api.price.dto.PriceStrategyBoardReqDTO;
import info.qizhi.aflower.module.pms.api.price.dto.PriceStrategyBoardRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 房价策略")
@RestController
@RequestMapping("/open/price-strategy")
@Validated
public class PriceStrategyController {
    @Resource
    private PriceStrategyService priceStrategyService;

    @GetMapping("/list-board")
    @Operation(summary = "获得策略看板列表(查询房价信息)")
    //@PreAuthorize("@ss.hasPermission('pms:price-strategy:query')")
    public CommonResult<List<PriceStrategyBoardRespVO>> getPriceStrategyBoardList(@Valid PriceStrategyBoardReqDTO reqVO) {
        List<PriceStrategyBoardRespDTO> respDTO=priceStrategyService.getPriceStrategyBoardList(reqVO);
        return success(BeanUtils.toBean(respDTO, PriceStrategyBoardRespVO.class));
    }
}
