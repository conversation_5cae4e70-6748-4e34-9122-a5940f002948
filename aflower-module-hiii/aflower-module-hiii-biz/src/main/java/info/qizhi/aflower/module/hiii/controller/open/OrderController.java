package info.qizhi.aflower.module.hiii.controller.open;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.hiii.service.open.OrderService;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.dto.BookBatchCheckInReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.CheckInReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderAddTogetherSaveReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderContinueInReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "第三方平台 - 订单")
@RestController
@RequestMapping("/open/order")
@Validated
public class OrderController {

    @Resource
    private OrderApi orderApi;
    @Resource
    private OrderService orderService;

    @PostMapping("/check-in")
    @Operation(summary = "入住(直接入住)")
    //@PreAuthorize("@ss.hasPermission('pms:order:create')")
    public CommonResult<String> createOrder(@Valid @RequestBody CheckInReqDTO reqVo) {
        return success(orderService.createOrder(reqVo));
    }

    @PostMapping("/add-together")
    @Operation(summary = "添加同住")
    //@PreAuthorize("@ss.hasPermission('pms:order:create')")
    public CommonResult<Boolean> createOrderTogether(@Valid @RequestBody OrderAddTogetherSaveReqDTO createReqVO) {
        orderService.createOrderTogether(createReqVO);
        return success(true);
    }

    @PutMapping("/continue-in")
    @Operation(summary = "续住")
   // @PreAuthorize("@ss.hasPermission('pms:order:update')")
    public CommonResult<Boolean> continueIn(@Valid @RequestBody OrderContinueInReqDTO updateReqVO) {
        orderService.continueIn(updateReqVO);
        return success(true);
    }

    @PostMapping("/book-check-in")
    @Operation(summary = "预订转入住")
    //@PreAuthorize("@ss.hasPermission('pms:order:create')")
    CommonResult<Boolean> batchCheckIn(@Valid @RequestBody BookBatchCheckInReqDTO reqVO){
        orderService.batchCheckIn(reqVO);
        return success(true);
    }

}
