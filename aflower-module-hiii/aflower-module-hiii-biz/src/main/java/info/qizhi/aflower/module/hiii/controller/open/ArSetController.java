package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.hiii.service.open.ArSetService;
import info.qizhi.aflower.module.pms.api.arset.ArSetApi;
import info.qizhi.aflower.module.pms.api.arset.dto.CalculateArSetDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "第三方平台 - 应付帐")
@RestController
@RequestMapping("/open/ar-set")
@Validated
public class ArSetController {

    @Resource
    private ArSetApi arSetApi;
    @Resource
    private ArSetService arSetService;

    @PostMapping("/calculate-ar-set")
    @Operation(summary = "计算AR账套金额(挂AR账)")
    //@PreAuthorize("@ss.hasPermission('pms:ar-set:create')")
    CommonResult<Boolean> calculateArSet(@Valid @RequestBody CalculateArSetDTO reqVO){
        arSetService.calculateArSet(reqVO);
        return success(true);
    }
}
