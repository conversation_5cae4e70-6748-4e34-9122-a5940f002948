package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.managerdaily.ManagerDailyReportRespVO;
import info.qizhi.aflower.module.hiii.service.open.ReportService;
import info.qizhi.aflower.module.report.api.managerdaily.dto.ManagerDailyReportRespDTO;
import info.qizhi.aflower.module.report.api.managerdaily.dto.ManagerDailyReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 报表
 * @Version: 1.0
 */
@Tag(name = "第三方平台 - 报表")
@RestController("openReportController")
@RequestMapping("/open/report")
@Validated
public class ReportController {


    @Resource
    private ReportService reportService;

    @GetMapping("/get")
    @Operation(summary = "获取经理日报")
    //@PreAuthorize("@ss.hasPermission('open:report:query')")
    public CommonResult<ManagerDailyReportRespVO> getManagerDaily(@Valid ManagerDailyReqDTO reqVO) {
        ManagerDailyReportRespDTO reportDTO=reportService.getDailyReport(reqVO);
        return success(BeanUtils.toBean(reportDTO, ManagerDailyReportRespVO.class));
    }
}
