package info.qizhi.aflower.module.hiii.controller.app;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.room.RoomApi;
import info.qizhi.aflower.module.pms.api.room.dto.RoomRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@Tag(name = "第三方平台 - 房间")
@RestController
@RequestMapping("/open/room")
@Validated
public class RoomController {
    @Resource
    private RoomApi roomApi;

    @GetMapping("/get")
    @Operation(summary = "获得房间信息")
    @Parameters({
            @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024"),
            @Parameter(name = "rNo", description = "房间号", required = true, example = "1024")
    })
    //@PreAuthorize("@ss.hasPermission('open:room:query')")
    CommonResult<RoomRespDTO> getRoomByRNo(@RequestParam("hcode") String hcode, @RequestParam("rNo") String rNo) {
        return roomApi.getRoomByRNo(hcode, rNo);
    }

    @GetMapping("/list")
    @Operation(summary = "获得门店所有房间")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    })
    //@PreAuthorize("@ss.hasPermission('open:room:query')")
    CommonResult<List<RoomRespDTO>> getRoomList(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        return roomApi.getRoomList(gcode, hcode);
    }
}
