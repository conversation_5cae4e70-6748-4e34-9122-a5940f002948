package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.ordertogether.OrderTogetherRespVO;
import info.qizhi.aflower.module.hiii.service.open.OrderTogetherService;
import info.qizhi.aflower.module.pms.api.order.OrderTogetherApi;
import info.qizhi.aflower.module.pms.api.order.dto.OrderTogetherReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderTogetherRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "第三方平台 - 宾客信息")
@RestController
@RequestMapping("/open/together")
@Validated
public class OrderTogetherController {

    @Resource
    private OrderTogetherApi orderTogetherApi;

    @Resource
    private OrderTogetherService orderTogetherService;

    @GetMapping("/list")
    @Operation(summary = "获得登记单信息")
    //@PreAuthorize("@ss.hasPermission('open:together:query')")
    public CommonResult<List<OrderTogetherRespVO>> getOrderTogetherList(@Valid OrderTogetherReqDTO reqDTO){
        List<OrderTogetherRespDTO> list=orderTogetherService.getOrderTogetherList(reqDTO);
        return success(BeanUtils.toBean(list, OrderTogetherRespVO.class));
    }

}
