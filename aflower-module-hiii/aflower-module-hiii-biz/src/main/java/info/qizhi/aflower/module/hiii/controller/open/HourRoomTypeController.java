package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.hiii.service.open.HourRoomTypeService;
import info.qizhi.aflower.module.pms.api.roomtype.dto.HourRoomTypeReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.HourRoomTypeRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 时租房")
@RestController
@RequestMapping("/open/hour-room-type")
@Validated
public class HourRoomTypeController {

    @Resource
    private HourRoomTypeService hourRoomTypeService;

    @GetMapping("/list")
    @Operation(summary = "获得时租房房型列表")
    public CommonResult<List<HourRoomTypeRespDTO>> getHourRoomTypeList(@Valid HourRoomTypeReqDTO reqVO) {
        List<HourRoomTypeRespDTO> list = hourRoomTypeService.getHourRoomTypeList(reqVO);
        return success(list);
    }
}
