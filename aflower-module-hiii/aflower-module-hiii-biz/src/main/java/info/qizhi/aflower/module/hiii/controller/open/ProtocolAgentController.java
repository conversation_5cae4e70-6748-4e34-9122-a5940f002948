package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.protocolagent.ProtocolAgentSimpleRespVO;
import info.qizhi.aflower.module.hiii.service.open.ProtocolAgentService;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentSimpleRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 渠道")
@RestController
@RequestMapping("/open/protocol-agent")
@Validated
public class ProtocolAgentController {

    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private ProtocolAgentService protocolAgentService;

    @GetMapping("/get-simple")
    @Operation(summary = "获得协议单位、中介(只包括代码,名称和渠道代码)")
    public CommonResult<ProtocolAgentSimpleRespVO> getProtocolAgentSimple(@RequestParam("hcode") String hcode, @RequestParam("pdName") String pdName) {
        ProtocolAgentSimpleRespDTO respDTO=protocolAgentService.getProtocolAgentSimple(hcode, pdName);
        return success(BeanUtils.toBean(respDTO,ProtocolAgentSimpleRespVO.class));
    }
}
