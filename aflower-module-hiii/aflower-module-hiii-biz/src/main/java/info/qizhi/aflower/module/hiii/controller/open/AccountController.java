package info.qizhi.aflower.module.hiii.controller.open;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.account.*;
import info.qizhi.aflower.module.hiii.service.open.AccountService;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 账务信息")
@RestController
@RequestMapping("/open/account")
@Validated
public class AccountController {

    @Resource
    private AccountApi accountApi;
    @Resource
    private AccountService accountService;

    @GetMapping("/get-list")
    @Operation(summary = "获得房间账务列表")
   // @PreAuthorize("@ss.hasPermission('open:account:query')")
    public CommonResult<List<AccountRespVO>> getAccountList(@Valid AccountListReqDTO reqDTO){
        List<AccountRespDTO> accountList = accountService.getAccountList(reqDTO);
        return success(BeanUtils.toBean(accountList, AccountRespVO.class));
    }

    @PostMapping("/pay")
    @Operation(summary = "付款、预授权操作(添加账务信息)")
    //@PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<String> createPayAccount(@Valid @RequestBody PayAccountSaveReqDTO createReqVO) {
        return success(accountService.createPayAccount(createReqVO));
    }


    // ------>>> 结账退房、挂账退房
    @GetMapping("/get-checkout-order-list")
    @Operation(summary = "结账退房-结账(挂账)账号列表")
    @Parameters({
            @Parameter(name = "no", description = "单号类型，1：预订单号，2：团队代码，3：客单号", required = true),
            @Parameter(name = "noType", description = "单号类型", required = false)
    })
    public CommonResult<OrderCheckOutGuestRespVO> getCheckOutGuestList(@RequestParam("no") String no, @RequestParam(value = "noType", required = false) String noType) {
        OrderCheckOutGuestRespDTO respDTO = accountService.getCheckOutGuestList(no, noType);
        return success(BeanUtils.toBean(respDTO, OrderCheckOutGuestRespVO.class));
    }

    @PostMapping("/get-confirm-room-fee")
    @Operation(summary = "结账退房-计算房费")
    //@PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<List<ConfirmCheckOutAccountRespVO>> getConfirmRoomFeeList(@Valid @RequestBody ConfirmCheckOutAccountReqDTO r) {
        List<ConfirmCheckOutAccountRespDTO> respDTO = accountService.getConfirmRoomFeeList(r);
        return success(BeanUtils.toBean(respDTO, ConfirmCheckOutAccountRespVO.class));
    }

    @PostMapping("/confirm-room-fee")
    @Operation(summary = "结账退房-确认生成房费")
    //@PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<Boolean> confirmRoomFee(@Valid @RequestBody ConfirmRoomFeeReqDTO reqVO) {
        accountService.confirmRoomFee(reqVO);
        return success(true);
    }

    @GetMapping("/get-confirm-preauth")
    @Operation(summary = "结账退房-预授权列表")
    //@PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<List<ConfirmCheckOutPreAuthRespVO>> getConfirmPreAuthList(@Valid ConfirmPreAuthListReqDTO reqVO) {
        List<ConfirmCheckOutPreAuthRespDTO> respDTO = accountService.getConfirmPreAuthList(reqVO);
        return success(BeanUtils.toBean(respDTO, ConfirmCheckOutPreAuthRespVO.class));
    }

    @PostMapping("/confirm-preauth")
    @Operation(summary = "结账退房-确认预授权（收款金额）")
   // @PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<Boolean> confirmPreAuth(@Valid @RequestBody ConfirmPreAuthReqDTO r) {
        accountService.confirmPreAuth(r);
        return success(true);
    }

    @PutMapping("/cancel-preauth")
    @Operation(summary = "取消预授权")
    //@PreAuthorize("@ss.hasPermission('pms:account:update')")
    public CommonResult<Boolean> cancelPreAuth(@RequestParam("accNo") String accNo) {
        accountService.cancelPreAuth(accNo);
        return success(true);
    }

    @PostMapping("/stat-account")
    @Operation(summary = "统计付款金额(完成结账时需要)")
    //@PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<StatCheckOutAccountRespVO> statCheckOutAccount(@Valid @RequestBody FinishCheckOutAccountReqDTO reqVO) {
        StatCheckOutAccountRespDTO respDTO = accountService.statCheckOutAccount(reqVO);
        return success(BeanUtils.toBean(respDTO, StatCheckOutAccountRespVO.class));
    }

    @PostMapping("/pay-check-out")
    @Operation(summary = "结账退房")
    //@PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<Boolean> payCheckOut(@Valid @RequestBody PayCheckOutAccountRepDTO r) {
        accountService.payCheckOut(r);
        return success(true);
    }

    @PostMapping("/credit-check-out")
    @Operation(summary = "挂账退房")
    //@PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<Boolean> creditCheckOut(@Valid @RequestBody ConfirmRoomFeeReqDTO reqVO) {
        accountService.creditCheckOut(reqVO);
        return success(true);
    }
    // <<<-------结账退房、挂账退房

}
