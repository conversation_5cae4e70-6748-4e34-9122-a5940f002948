package info.qizhi.aflower.module.hiii.controller.app;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.app.vo.OtaBookOpenReqVO;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 预订单
 * @Version: 1.0
 */
@Tag(name = "第三方平台 - 预订单")
@RestController
@RequestMapping("/open/book")
@Validated
public class BookController {

    @Resource
    private BookingApi bookingApi;

    @PostMapping("/ota-new-order")
    @Operation(summary = "ota新预订")
    //@PreAuthorize("@ss.hasPermission('open:book:query')")
    public CommonResult<OtaBookOpenRespDTO> createBook(@Valid @RequestBody OtaBookOpenReqVO reqVO) {
        return bookingApi.createBook(BeanUtils.toBean(reqVO, OtaBookOpenReqDTO.class));
    }

    @PostMapping("/ota-order-modify")
    @Operation(summary = "ota预订修改")
    //@PreAuthorize("@ss.hasPermission('open:book:update')")
    public CommonResult<OtaBookOpenRespDTO> updateGeneralBook(@Valid @RequestBody OtaBookOpenReqDTO reqVO) {
        return bookingApi.updateGeneralBook(reqVO);
    }

    @PostMapping("/ota-order-cancel")
    @Operation(summary = "ota预订取消")
    //@PreAuthorize("@ss.hasPermission('open:book:create')")
    public CommonResult<OtaBookCancelOpenRespDTO> cancelBook(@Valid @RequestBody OtaBookCancelOpenReqDTO reqVO) {
        return bookingApi.cancleBook(reqVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得预订单分页(查询订单)")
   // @PreAuthorize("@ss.hasPermission('open:book:query')")
    public CommonResult<PageResult<BookPageRespDTO>> getBookPage(@Valid BookPageReqDTO pageReqVO) {
        return bookingApi.getBookPage(pageReqVO);
    }


}
