package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.pricerule.PriceHourRuleRespVO;
import info.qizhi.aflower.module.hiii.service.open.PriceHourRuleService;
import info.qizhi.aflower.module.pms.api.pricerule.dto.PriceHourRuleRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 钟点房房价信息")
@RestController
@RequestMapping("/open/price-hour-rule")
@Validated
public class PriceHourRuleController {
    @Resource
    private PriceHourRuleService priceHourRuleService;

    @GetMapping("/get")
    @Operation(summary = "获得时租房计费规则(查询钟点房房价信息)")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:price-hour-rule:query')")
    public CommonResult<PriceHourRuleRespVO> getPriceHourRule(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode){
        PriceHourRuleRespDTO respDTO=priceHourRuleService.getPriceHourRule(gcode,hcode);
        return success(BeanUtils.toBean(respDTO, PriceHourRuleRespVO.class));
    }

}
