package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.room.RoomRespVO;
import info.qizhi.aflower.module.hiii.service.open.RoomService;
import info.qizhi.aflower.module.pms.api.room.RoomApi;
import info.qizhi.aflower.module.pms.api.room.dto.RoomRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


/**
 * <AUTHOR>
 */
@Tag(name = "第三方平台 - 房间")
@RestController("openRoomController")
@RequestMapping("/open/room")
@Validated
public class RoomController {
    @Resource
    private RoomApi roomApi;
    @Resource
    private RoomService roomService;

    @GetMapping("/list")
    @Operation(summary = "获得门店所有房间")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    })
    //@PreAuthorize("@ss.hasPermission('open:room:query')")
    CommonResult<List<RoomRespVO>> getRoomList(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        List<RoomRespDTO> list=roomService.getRoomList(gcode, hcode);
        return success(BeanUtils.toBean(list, RoomRespVO.class));
    }
}
