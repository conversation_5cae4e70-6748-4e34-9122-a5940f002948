package info.qizhi.aflower.module.hiii.controller.open.vo.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 可预订房型房价 Response VO")
@Data
public class BookRoomTypePriceTwoRespVO {
    @Schema(description = "货币单位")
    private String currencyUnit;

    @Schema(description = "可预订房型列表")
    private List<BookRoomTypePriceRespVO> bookRoomTypePriceList;
}
