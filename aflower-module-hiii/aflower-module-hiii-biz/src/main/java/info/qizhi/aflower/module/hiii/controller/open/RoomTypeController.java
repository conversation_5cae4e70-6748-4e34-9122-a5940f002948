package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.roomType.RoomTypeOpenRespVO;
import info.qizhi.aflower.module.hiii.service.open.RoomTypeService;
import info.qizhi.aflower.module.pms.api.roomtype.RoomTypeApi;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeOpenReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeOpenRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 房型
 * @Version: 1.0
 */
@Tag(name = "第三方平台 - 房型")
@RestController("openRoomTypeController")
@RequestMapping("/open/roomtype")
@Validated
public class RoomTypeController {

    @Resource
    private RoomTypeApi roomTypeApi;
    @Resource
    private RoomTypeService roomTypeService;

    @GetMapping("/get")
    @Operation(summary = "查询房型")
    //@PreAuthorize("@ss.hasPermission('open:roomtype:query')")
    public CommonResult<List<RoomTypeOpenRespVO>> getManagerDaily(@Valid RoomTypeOpenReqDTO reqVO) {
        List<RoomTypeOpenRespDTO> list = roomTypeService.getRoomType(reqVO);
        return success(BeanUtils.toBean(list, RoomTypeOpenRespVO.class));
    }
}
