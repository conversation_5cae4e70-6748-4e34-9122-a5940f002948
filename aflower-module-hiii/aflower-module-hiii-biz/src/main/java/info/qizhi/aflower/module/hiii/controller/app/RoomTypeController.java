package info.qizhi.aflower.module.hiii.controller.app;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.roomtype.RoomTypeApi;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeOpenReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeOpenRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 房型
 * @Version: 1.0
 */
@Tag(name = "第三方平台 - 房型")
@RestController
@RequestMapping("/open/roomtype")
@Validated
public class RoomTypeController {

    @Resource
    private RoomTypeApi roomTypeApi;

    @GetMapping("/get")
    @Operation(summary = "查询房型")
    //@PreAuthorize("@ss.hasPermission('open:roomtype:query')")
    public CommonResult<List<RoomTypeOpenRespDTO>> getManagerDaily(@Valid RoomTypeOpenReqDTO reqVO) {
        return roomTypeApi.getRoomType(reqVO);
    }
}
