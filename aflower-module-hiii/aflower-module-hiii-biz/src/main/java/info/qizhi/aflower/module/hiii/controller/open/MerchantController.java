package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.tenant.core.aop.TenantIgnore;
import info.qizhi.aflower.module.hiii.controller.open.vo.merchant.GroupSimpleRespVO;
import info.qizhi.aflower.module.hiii.service.open.MerchantService;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.GroupSimpleRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 门店")
@RestController
@RequestMapping("/open/merchant")
@Validated
public class MerchantController {
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private MerchantService merchantService;

    @GetMapping("/get/group")
    @Operation(summary = "根据门店代码获取集团信息")
    @Parameter(name = "hcode", description = "编号", required = true, example = "1024")
    @TenantIgnore
    CommonResult<GroupSimpleRespVO> getGroup(@RequestParam("hcode") String hcode){
        GroupSimpleRespDTO respDTO=merchantService.getGroup(hcode);
        return success(BeanUtils.toBean(respDTO, GroupSimpleRespVO.class));
    }

}
