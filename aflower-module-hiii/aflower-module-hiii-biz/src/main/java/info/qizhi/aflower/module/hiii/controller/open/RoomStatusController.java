package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.roomstatus.FutureRoomStatusRespVO;
import info.qizhi.aflower.module.hiii.controller.open.vo.roomstatus.RealTimeRoomStatusRespVO;
import info.qizhi.aflower.module.hiii.service.open.RoomStatusService;
import info.qizhi.aflower.module.pms.api.roomstatus.RoomStatusApi;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.FutureRoomStatusReqDTO;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.FutureRoomStatusRespDTO;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.RealTimeRoomStatusReqDTO;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.RealTimeRoomStatusRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Tag(name = "第三方平台 - 房间状态")
@RestController
@RequestMapping("/open/room-status")
@Validated
public class RoomStatusController {

    @Resource
    private RoomStatusApi roomStatusApi;
    @Resource
    private RoomStatusService roomStatusService;

    @GetMapping("/list")
    @Operation(summary = "实时房间")
    //@PreAuthorize("@ss.hasPermission('open:room-status:query')")
    public CommonResult<List<RealTimeRoomStatusRespVO>> getRealTimeRoomTypeList(@Valid  RealTimeRoomStatusReqDTO reqVO) {
        List<RealTimeRoomStatusRespDTO> list=roomStatusService.getRealTimeRoomTypeList(reqVO);
        return CommonResult.success(BeanUtils.toBean(list, RealTimeRoomStatusRespVO.class));
    }

    @GetMapping("/future-room-status")
    @Operation(summary = "远期房态")
    public CommonResult<FutureRoomStatusRespVO> getFutureRoomStatus(@Valid FutureRoomStatusReqDTO reqVO){
        FutureRoomStatusRespDTO respDTO=roomStatusService.getFutureRoomStatus(reqVO);
        return CommonResult.success(BeanUtils.toBean(respDTO, FutureRoomStatusRespVO.class));
    }



}
