package info.qizhi.aflower.module.hiii.controller.app;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.api.managerdaily.ManagerDailyReportApi;
import info.qizhi.aflower.module.report.api.managerdaily.dto.ManagerDailyReportRespDTO;
import info.qizhi.aflower.module.report.api.managerdaily.dto.ManagerDailyReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 报表
 * @Version: 1.0
 */
@Tag(name = "第三方平台 - 报表")
@RestController
@RequestMapping("/open/report")
@Validated
public class ReportController {

    @Resource
    private ManagerDailyReportApi managerDailyReportApi;

    @GetMapping("/get")
    @Operation(summary = "获取经理日报")
    //@PreAuthorize("@ss.hasPermission('open:report:query')")
    public CommonResult<ManagerDailyReportRespDTO> getManagerDaily(@Valid ManagerDailyReqDTO reqVO) {
        return  managerDailyReportApi.getDailyReport(reqVO);
    }
}
