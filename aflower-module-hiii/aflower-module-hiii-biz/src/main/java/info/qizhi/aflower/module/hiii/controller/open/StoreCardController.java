package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.hiii.service.open.StoreCardService;
import info.qizhi.aflower.module.member.api.storecard.StoreCardApi;
import info.qizhi.aflower.module.member.api.storecard.dto.StoreCardConsumeReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "第三方平台 - 会员储值卡")
@RestController
@RequestMapping("/open/store-card")
@Validated
public class StoreCardController {

    @Resource
    private StoreCardApi storeCardApi;
    @Resource
    private StoreCardService storeCardService;

    @PostMapping("/consume")
    @Operation(summary = "储值卡消费")
    //@PreAuthorize("@ss.hasPermission('pms:store-card:create')")
    CommonResult<Boolean> consumeStoreCard(@Valid @RequestBody StoreCardConsumeReqDTO storeCardConsumeReqDTO){
        storeCardService.consumeStoreCard(storeCardConsumeReqDTO);
        return success(true);
    }
}
