package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.enums.UserTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.tenant.core.aop.TenantIgnore;
import info.qizhi.aflower.module.system.api.oauth2.OAuth2TokenApi;
import info.qizhi.aflower.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import info.qizhi.aflower.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 认证")
@RestController
@RequestMapping("/open/auth")
@Validated
@Slf4j
public class AuthController {

    @Resource
   private OAuth2TokenApi oAuth2TokenApi;

    @GetMapping("/get-token")
    @PermitAll
    @Operation(summary = "获取toekn")
    @TenantIgnore
    public CommonResult<OAuth2AccessTokenRespDTO> login(@RequestParam("client_id") String clientId, @RequestParam("client_secret") String clientSecret) {
        CommonResult<OAuth2AccessTokenRespDTO> openAccessTokenTwo = oAuth2TokenApi.createOpenAccessTokenTwo(new OAuth2AccessTokenCreateReqDTO().setUserId(Long.parseLong(clientId))
                .setClientId(clientId).setClientSecret(clientSecret).setUserType(UserTypeEnum.T.getValue()));
        return openAccessTokenTwo;
    }

    @PostMapping("/refresh-token")
    @PermitAll
    @Operation(summary = "刷新令牌")
    @Parameter(name = "refreshToken", description = "刷新令牌", required = true)
    public CommonResult<OAuth2AccessTokenRespDTO> refreshToken(@RequestParam("refreshToken") String refreshToken,
                                                               @RequestParam("client_id") String clientId,
                                                               @RequestParam("client_secret") String clientSecret) {

        return oAuth2TokenApi.refreshAccessTokenTwo(refreshToken, clientId,clientSecret);
    }


}
