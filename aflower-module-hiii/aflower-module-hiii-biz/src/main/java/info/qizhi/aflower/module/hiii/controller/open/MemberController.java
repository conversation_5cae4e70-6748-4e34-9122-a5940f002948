package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.member.MemberAndStoreCardRespVO;
import info.qizhi.aflower.module.hiii.service.open.MemberService;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "第三方平台 - 会员")
@RestController
@RequestMapping("/open/member")
@Validated
public class MemberController {

    @Resource
    private MemberApi memberApi;
    @Resource
    private MemberService memberService;

    @GetMapping("/get/member")
    @Operation(summary = "获得单个会员信息")
    //@PreAuthorize("@ss.hasPermission('open:member:query')")
    public CommonResult<MemberAndStoreCardRespVO> getMemberByIdNo(@Valid MemberReqDTO reqVo) {
        MemberAndStoreCardRespDTO respDTO=memberService.getMember(reqVo);
        return success(BeanUtils.toBean(respDTO, MemberAndStoreCardRespVO.class));
    }
}
