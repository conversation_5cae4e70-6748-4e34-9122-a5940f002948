package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.app.vo.OtaBookOpenReqVO;
import info.qizhi.aflower.module.hiii.controller.open.vo.book.BookPageRespVO;
import info.qizhi.aflower.module.hiii.controller.open.vo.book.BookRoomTypePriceTwoRespVO;
import info.qizhi.aflower.module.hiii.controller.open.vo.book.OtaBookCancelOpenRespVO;
import info.qizhi.aflower.module.hiii.controller.open.vo.book.OtaBookOpenRespVO;
import info.qizhi.aflower.module.hiii.service.open.BookService;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 预订单
 * @Version: 1.0
 */
@Tag(name = "第三方平台 - 预订单")
@RestController("openBookController")
@RequestMapping("/open/book")
@Validated
public class BookController {

    @Resource
    private BookingApi bookingApi;
    @Resource
    private BookService bookService;

    @PostMapping("/ota-new-order")
    @Operation(summary = "新预订")
    //@PreAuthorize("@ss.hasPermission('open:book:query')")
    public CommonResult<OtaBookOpenRespVO> createBook(@Valid @RequestBody OtaBookOpenReqVO reqVO) {
        OtaBookOpenRespDTO respDTO=bookService.createBook(reqVO);
        return success(BeanUtils.toBean(respDTO, OtaBookOpenRespVO.class));
    }

    @PostMapping("/ota-order-modify")
    @Operation(summary = "预订修改")
    //@PreAuthorize("@ss.hasPermission('open:book:update')")
    public CommonResult<OtaBookOpenRespVO> updateGeneralBook(@Valid @RequestBody OtaBookOpenReqVO reqVO) {
        OtaBookOpenRespDTO respDTO=bookService.updateGeneralBook(reqVO);
        return success(BeanUtils.toBean(respDTO, OtaBookOpenRespVO.class));
    }

    @PostMapping("/ota-order-cancel")
    @Operation(summary = "预订取消")
    //@PreAuthorize("@ss.hasPermission('open:book:create')")
    public CommonResult<OtaBookCancelOpenRespVO> cancelBook(@Valid @RequestBody OtaBookCancelOpenReqDTO reqVO) {
        OtaBookCancelOpenRespDTO respDTO=bookService.cancleBook(reqVO);
        return success(BeanUtils.toBean(respDTO, OtaBookCancelOpenRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得预订单分页(查询订单)")
   // @PreAuthorize("@ss.hasPermission('open:book:query')")
    public CommonResult<PageResult<BookPageRespVO>> getBookPage(@Valid BookPageReqDTO pageReqVO) {
        PageResult<BookPageRespDTO> respDTO=bookService.getBookPage(pageReqVO);
        return success(BeanUtils.toBean(respDTO, BookPageRespVO.class));
    }

    @GetMapping("/get/roomtype")
    @Operation(summary = "获得可预订的房型列表(房型、售价、可售数、可超数、预计间数、排房信息、赠早数)")
    CommonResult<BookRoomTypePriceTwoRespVO> getBookRoomTypeList(@Valid BookReqDTO reqVO){
        BookRoomTypePriceTwoRespDTO respDTO=bookService.getBookRoomTypeList(reqVO);
        return success(BeanUtils.toBean(respDTO, BookRoomTypePriceTwoRespVO.class));
    }

}
