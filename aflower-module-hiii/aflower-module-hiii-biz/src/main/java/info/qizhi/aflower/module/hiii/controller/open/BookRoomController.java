package info.qizhi.aflower.module.hiii.controller.open;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.hiii.controller.open.vo.bookRoom.BookRoomArrangeRespVO;
import info.qizhi.aflower.module.hiii.service.open.BookRoomService;
import info.qizhi.aflower.module.pms.api.booking.BookRoomApi;
import info.qizhi.aflower.module.pms.api.booking.dto.BookRoomArrangeRespDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.BookRoomArrangeSaveReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.BookRoomTypeArrangeReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "第三方平台 - 排房")
@RestController
@RequestMapping("/open/book-room")
@Validated
public class BookRoomController {

    @Resource
    private BookRoomApi bookRoomApi;
    @Resource
    private BookRoomService bookRoomService;

    @PutMapping("/arrange")
    @Operation(summary = "排房")
   // @PreAuthorize("@ss.hasPermission('pms:book-room:update')")
    public CommonResult<Boolean> arrangeRoom(@Valid @RequestBody BookRoomArrangeSaveReqDTO reqVO) {
        bookRoomService.arrangeRoom(reqVO);
        return success(true);
    }

    @GetMapping("/can-book-rooms")
    @Operation(summary = "获得可预订的房间列表")
    public CommonResult<List<BookRoomArrangeRespVO>> getBookRoom(@Valid BookRoomTypeArrangeReqDTO reqVO) {
        List<BookRoomArrangeRespDTO> list=bookRoomService.getBookRoom(reqVO);
        return success(BeanUtils.toBean(list, BookRoomArrangeRespVO.class));
    }
}
