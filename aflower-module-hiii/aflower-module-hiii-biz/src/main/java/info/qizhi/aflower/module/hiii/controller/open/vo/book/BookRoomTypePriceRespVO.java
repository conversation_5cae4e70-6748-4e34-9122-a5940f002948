package info.qizhi.aflower.module.hiii.controller.open.vo.book;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeBedDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 可预订房型房价 Response VO")
@Data
public class BookRoomTypePriceRespVO {

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房型名称")
    private String rtName;

    @Schema(description = "房型图")
    private String rtImage;

    @Schema(description = "面积,单位平方米")
    private BigDecimal area;

    @Schema(description = "容纳人数")
    private Integer peopleNum;

    @Schema(description = "是否有窗;是否有窗(0是，1否，2部分有窗)")
    private String isWindow;

    @Schema(description = "是否有卫生间;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isRestRoom;

    @Schema(description = "是否虚拟房;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isVirtual;

    @Schema(description = "房间床型")
    private List<RoomTypeBedDTO> roomTypeBeds;

    @Schema(description = "优惠价(取每日价格列表第一天的vipPrice)")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long vipPrice;

    @Schema(description = "房价(取每日价格列表第一天的price)")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "可售数")
    private Integer canSellNum;

    @Schema(description = "可超数")
    private Integer canOverNum;

    @Schema(description = "早餐数,赠送数")
    private Integer bkNum;

    @Schema(description = "预订数")
    private Integer roomNum;

    @Schema(description = "预抵时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "排房信息")
    private List<BookRoom> bookRooms;

    @Schema(description = "每日价格")
    private List<OrderPrice> dayPrices;

    @Data
    public static class BookRoom {

        @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
        private String orderNo;

        @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "rCode")
        private String rCode;

        @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
        private String state;
    }

    @Data
    public static class OrderPrice {

        @Schema(description = "日期", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
        private LocalDate priceDate;

        @Schema(description = "周几", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer week;

        @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "168")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long price;

        @Schema(description = "优惠价", requiredMode = Schema.RequiredMode.REQUIRED, example = "168")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long vipPrice;

        @Schema(description = "赠送早餐数")
        private Integer bkNum;

        @Schema(description = "房包早餐数")
        private Integer roomBkNum;

        @Schema(description = "价格策略代码")
        private String priceStrategyCode;

    }
}
