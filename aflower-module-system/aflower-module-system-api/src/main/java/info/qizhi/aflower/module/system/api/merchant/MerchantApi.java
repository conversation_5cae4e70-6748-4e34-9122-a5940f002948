package info.qizhi.aflower.module.system.api.merchant;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.module.system.api.merchant.dto.*;
import info.qizhi.aflower.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 门店")
public interface MerchantApi {

    String PREFIX = ApiConstants.PREFIX + "/merchant";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得门店列表")
    @Parameter(name = "gcode", description = "集团编号", example = "abc", required = true)
    CommonResult<List<MerchantRespDTO>> getMerchantList(@RequestParam("gcode") String gcode);

    /**
     * 获取门店的map
     *
     * @param gcode 集团代码
     * @return map
     */
    default Map<String, String> getMerchantMap2(String gcode) {
        List<MerchantRespDTO> merchantList = getMerchantList(gcode).getData();
        return CollectionUtils.convertMap(merchantList, MerchantRespDTO::getHcode, MerchantRespDTO::getHname);
    }

    @GetMapping(PREFIX + "/list-open-all-hotel")
    @Operation(summary = "获得所有营业中、试营业的门店列表")
    CommonResult<List<MerchantSimpleResp2DTO>> getOpenMerchantList();

    @GetMapping(PREFIX + "/list-userid")
    @Operation(summary = "获得用户可访问的门店列表")
    @Parameter(name = "userId", description = "用户id", example = "123", required = true)
    CommonResult<List<MerchantSimpleRespDTO>> getVisitMerchantList(@RequestParam("userId") Long userId);

    @GetMapping(PREFIX + "/map")
    @Operation(summary = "获得门店map")
    @Parameter(name = "gcode", description = "集团编号", example = "abc", required = true)
    CommonResult<Map<String, MerchantRespDTO>> getMerchantMap(@RequestParam("gcode") String gcode);

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得门店")
    @Parameter(name = "hcode", description = "编号", required = true, example = "1024")
    CommonResult<MerchantRespDTO> getMerchant(@RequestParam("hcode") String hcode);

    @GetMapping(PREFIX + "/get/by")
    @Operation(summary = "获得门店通过酒店代码和名称")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编号", required = true, example = "abc"),
            @Parameter(name = "hcode", description = "编号", required = true, example = "1024"),
            @Parameter(name = "hname", description = "名称", required = true, example = "abc")
    })
    CommonResult<MerchantRespDTO> getMerchantByHcodeHname(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode,@RequestParam("hname") String hname);

    @GetMapping(PREFIX+ "/get/group")
    @Operation(summary = "根据门店代码获取集团信息")
    @Parameter(name = "hcode", description = "编号", required = true, example = "1024")
    CommonResult<GroupSimpleRespDTO> getGroup(@RequestParam("hcode") String hcode);

    @PostMapping(PREFIX+ "/update")
    @Operation(summary = "修改门店")
    void update(@RequestBody MerchantShiftReqDTO reqDTO);
}
