package info.qizhi.aflower.module.system.api.oauth2;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.system.api.oauth2.dto.OAuth2ClientDTO;
import info.qizhi.aflower.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - OAuth2.0 令牌")
public interface OAuth2ClientApi {

    String PREFIX = ApiConstants.PREFIX + "/oauth2/client";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "校验访问令牌")
    @Parameter(name = "accessToken", description = "访问令牌", required = true, example = "tudou")
    CommonResult<OAuth2ClientDTO> getOAuth2ClientFromCache(@RequestParam("clientId") String clientId);

}
