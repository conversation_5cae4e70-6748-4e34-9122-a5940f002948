package info.qizhi.aflower.module.system.api.social.dto;

import info.qizhi.aflower.framework.common.enums.UserTypeEnum;
import info.qizhi.aflower.framework.common.validation.InEnum;
import info.qizhi.aflower.module.system.enums.social.SocialTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "RPC 服务 - 取消绑定社交用户 Request DTO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SocialUserBindNoCodeReqDTO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "{id.notnull}")
    private String username;
    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String gcode;
    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(UserTypeEnum.class)
    @NotNull(message = "{userType.notnull}")
    private Integer userType;

    @Schema(description = "社交平台的类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InEnum(SocialTypeEnum.class)
    @NotNull(message = "社交平台的类型不能为空")
    private Integer socialType;
    @Schema(description = "state", requiredMode = Schema.RequiredMode.REQUIRED, example = "qtw")
    @NotEmpty(message = "state 不能为空")
    private String state;

    @Schema(description = "唯一码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String openId;

    @Schema(description = "微信消息", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String inMessage;

}
