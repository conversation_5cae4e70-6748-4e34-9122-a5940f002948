package info.qizhi.aflower.module.system.api.oauth2.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * OAuth2 客户端 DO
 *
 * <AUTHOR>
 */
@Data
public class OAuth2ClientDTO {

    @Schema(description = "客户端编号", required = true, example = "tudou")
    private String clientId;

    @Schema(description = "名称", required = true, example = "tudou")
    private String name;


}
