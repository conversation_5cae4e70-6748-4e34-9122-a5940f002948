package info.qizhi.aflower.module.member.convert.auth;


import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberRespVO;
import info.qizhi.aflower.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import info.qizhi.aflower.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AuthConvert {

    AuthConvert INSTANCE = Mappers.getMapper(AuthConvert.class);

    default AppAuthLoginRespVO convert(OAuth2AccessTokenRespDTO bean, String openid, MemberRespVO member , String mtName){
        if ( bean == null && openid == null ) {
            return null;
        }

        AppAuthLoginRespVO.AppAuthLoginRespVOBuilder appAuthLoginRespVO = AppAuthLoginRespVO.builder();

        if ( bean != null ) {
            appAuthLoginRespVO.userId( bean.getUserId() );
            appAuthLoginRespVO.accessToken( bean.getAccessToken() );
            appAuthLoginRespVO.refreshToken( bean.getRefreshToken() );
            appAuthLoginRespVO.expiresTime( bean.getExpiresTime() );
        }
        appAuthLoginRespVO.openid( openid );
        appAuthLoginRespVO.phone( member.getPhone() );
        appAuthLoginRespVO.mtCode( member.getMtCode() );
        appAuthLoginRespVO.mtName( mtName );
        appAuthLoginRespVO.mcode( member.getMcode() );
        appAuthLoginRespVO.userName( member.getName() );

        return appAuthLoginRespVO.build();
    }

}
