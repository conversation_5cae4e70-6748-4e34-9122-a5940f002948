package info.qizhi.aflower.module.member.dal.dataobject.storecard;

import lombok.*;


import com.baomidou.mybatisplus.annotation.*;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员储值卡 DO
 *
 * <AUTHOR>
 */
@TableName("member_store_card")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StoreCardDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 开卡酒店
     */
    private String hcode;
    /**
     * 会员代码
     */
    private String mcode;
    /**
     * 储值卡号;默认会员的手机号码作为卡号
     */
    private String storeCardNo;
    /**
     * 外部卡号;用于存储物理卡号
     */
    private String outCardNo;
    /**
     * 状态;valid：有效 invalid：无效 freeze：冻结
     */
    private String state;
    /**
     * 是否集团储值卡;0:否 1:是
     */
    private String isG;
    /**
     * 余额
     */
    private Long balance;
    /**
     * 累积积分
     */
    private Long totalPoint;
    /**
     * 集可用积分
     */
    private Long point;
    /**
     * 累积已用积分
     */
    private Long usedPoint;
    /**
     * 总消费
     */
    private Long totalFee;
    /**
     * 总赠送
     */
    private Long totalGive;
    /**
     * 总充值
     */
    private Long totalRecharge;
    /**
     * 办卡方式（recharge:充值赠送/gift:赠送/purchase:付费购买/free:免费注册）
     */
    private String cardRegisterType;

}