package info.qizhi.aflower.module.member.dal.dataobject.exchange;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;

/**
 * 积分兑换项目 DO
 *
 * <AUTHOR>
 */
@TableName("point_exchange_item")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointExchangeItemDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 项目代码
     */
    private String itemCode;
    /**
     * 项目名称
     */
    private String itemName;
    /**
     * 礼品类别代码
     */
    private String gtCode;
    /**
     * 扣减积分
     */
    private Integer reducePoint;
    /**
     * 兑换描述
     */
    private String remark;
    /**
     * 是否快递配送;0:否 1：是
     */
    private String isExpress;
    /**
     * 礼品图片;json对象，存储多个图片路径
     */
    private String pics;

}