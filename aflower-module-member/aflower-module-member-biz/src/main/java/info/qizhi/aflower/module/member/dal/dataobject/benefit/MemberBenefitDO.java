package info.qizhi.aflower.module.member.dal.dataobject.benefit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@TableName("member_benefit")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberBenefitDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 权益代码
     */
    private String benefitCode;
    /**
     * 权益名称
     */
    private String name ;
    /**
     * 权益内容
     */
    private String value ;
    /**
     * 是否系统内置
     */
    private String isSys;
}
