package info.qizhi.aflower.module.member.dal.dataobject.benefit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@TableName("benefit_image")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BenefitImageDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 图标
     */
    private String image;
    /**
     * 权益代码
     */
    private String benefitCode;
    /**
     * 图标类型
     */
    private String imageType;
}
