package info.qizhi.aflower.module.member.dal.dataobject.storecard;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;

/**
 * 会员卡销售提成 DO
 *
 * <AUTHOR>
 */
@TableName("member_card_deduct")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberCardDeductDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 是否入账到集团;0:否 1：是
     */
    private String isIncomeG;
    /**
     * 卡费是否提成;0:否 1：是
     */
    private String isDeductCardFee;
    /**
     * 规则;json对象，参数包括：会员类型  提成方（集团、门店、销售员），提成金额
     */
    private String rule;

}