package info.qizhi.aflower.module.member.convert.member;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import info.qizhi.aflower.framework.common.enums.MemberPathwayEnum;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.member.MemberDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;
import java.util.Objects;

/**
 * @Author: TY
 * @CreateTime: 2024-08-12
 * @Description: 会员转换
 * @Version: 1.0
 */
@Mapper
public interface MemberConvert {

    MemberConvert INSTANCE = Mappers.getMapper(MemberConvert.class);

    default MemberDO convert(MemberSaveReqVO reqVO) {
        return MemberDO.builder()
                .gcode(reqVO.getGcode())
                .hcode(reqVO.getHcode())
                .mcode(IdUtil.getSnowflakeNextIdStr())
                .name(reqVO.getName())
                .sex(reqVO.getSex())
                .phone(reqVO.getPhone().trim())
                .email(reqVO.getEmail())
                .idType(reqVO.getIdType())
                .idNo(reqVO.getIdNo())
                .pwd(StrUtil.subSuf(reqVO.getIdNo().trim(), reqVO.getIdNo().trim().length() - 6))
                .address(reqVO.getAddress()).mtCode(reqVO.getMtCode()).birthday(reqVO.getBirthday())
                .nation(reqVO.getNation())
                .unionId(reqVO.getUnionId())
                .developWay(StrUtil.isNotBlank(reqVO.getUnionId()) ? MemberPathwayEnum.MINI_APP.getCode() : MemberPathwayEnum.LOBBY.getCode())
                .isSendMsg(reqVO.getIsSendMsg())
                .pinyin(PinyinUtil.getFirstLetter(reqVO.getName(), ""))
                .seller(reqVO.getSeller())
                .period(Objects.isNull(reqVO.getPeriod()) ? LocalDate.parse("2099-12-31") : reqVO.getPeriod())
                .remark(reqVO.getRemark())
                .build();
    }
}
