package info.qizhi.aflower.module.member.dal.dataobject.benefit;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

@TableName("member_type_benefit")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MemberTypeBenefitDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 会员类型代码
     */
    private String mtCode;
    /**
     * 权益代码
     */
    private String benefitCode ;
    /**
     * 权益内容
     */
    private String value ;
}
