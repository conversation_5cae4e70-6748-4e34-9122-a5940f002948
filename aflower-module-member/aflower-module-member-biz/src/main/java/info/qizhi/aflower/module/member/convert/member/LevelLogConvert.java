package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberUpgradeReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 会员等级日志 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LevelLogConvert {


    LevelLogConvert INSTANCE = Mappers.getMapper(LevelLogConvert.class);

    default LevelLogSaveReqVO leveLogConvert(MemberUpgradeReqVO reqVO) {
        LevelLogSaveReqVO levelLogSaveReqVO = new LevelLogSaveReqVO();
        levelLogSaveReqVO.setHcode(reqVO.getHcode());
        levelLogSaveReqVO.setGcode(reqVO.getGcode());
        levelLogSaveReqVO.setMcode(reqVO.getMcode());
        levelLogSaveReqVO.setSourceMtcode(reqVO.getSourceMtcode());
        levelLogSaveReqVO.setTargetMtcode(reqVO.getTargetMtcode());
        levelLogSaveReqVO.setPayMode(reqVO.getPayMethod());
        levelLogSaveReqVO.setPayType(reqVO.getPayType());
        levelLogSaveReqVO.setChangeType(NumberEnum.ZERO.getNumber());
        levelLogSaveReqVO.setFee(reqVO.getBuyFee());
        levelLogSaveReqVO.setPoint(reqVO.getPayPoint()!=null? reqVO.getPayPoint():0);
        levelLogSaveReqVO.setStoreCardNo(reqVO.getStoreCardNo());
        levelLogSaveReqVO.setRemark(reqVO.getRemark());
        return levelLogSaveReqVO;
    }

}


