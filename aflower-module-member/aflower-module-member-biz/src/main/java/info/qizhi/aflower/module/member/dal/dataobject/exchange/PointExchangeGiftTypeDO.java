package info.qizhi.aflower.module.member.dal.dataobject.exchange;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;

/**
 * 积分兑换-礼品分类 DO
 *
 * <AUTHOR>
 */
@TableName("point_exchange_gift_type")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PointExchangeGiftTypeDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 礼品类别代码
     */
    private String gtCode;
    /**
     * 礼品类别名称
     */
    private String gtName;

}