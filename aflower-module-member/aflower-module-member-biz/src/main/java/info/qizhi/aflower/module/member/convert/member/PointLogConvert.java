package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.CouponOrderTypeEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.enums.PointSceneEnum;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.ConsumeSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.RechargeSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.member.MemberDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.LocalDate;

/**
 * 会员积分日志 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PointLogConvert {
    PointLogConvert INSTANCE = Mappers.getMapper(PointLogConvert.class);

    default PointLogSaveReqVO pointLogRechargeLogSaveConvert(RechargeSaveReqVO reqVO, MemberDO member,
                                                             MemberTypeDO memberType, String cashBillOrderNo) {

        PointLogSaveReqVO pointLogSaveReqVO = new PointLogSaveReqVO();
        pointLogSaveReqVO.setGcode(reqVO.getGcode());
        pointLogSaveReqVO.setHcode(reqVO.getHcode());
        pointLogSaveReqVO.setMcode(reqVO.getMcode());
        pointLogSaveReqVO.setName(member.getName());
        pointLogSaveReqVO.setPhone(member.getPhone());
        pointLogSaveReqVO.setMtCode(reqVO.getMtCode());
        pointLogSaveReqVO.setMtName(memberType.getMtName());
        pointLogSaveReqVO.setIndate(LocalDate.now().plusYears(100L));
        pointLogSaveReqVO.setState(NumberEnum.ONE.getNumber());
        pointLogSaveReqVO.setPointType(NumberEnum.ZERO.getNumber());
        pointLogSaveReqVO.setScene(PointSceneEnum.RECHARGE.getCode());
        pointLogSaveReqVO.setOrderNo(cashBillOrderNo);
        pointLogSaveReqVO.setOrderType(CouponOrderTypeEnum.CASH.getCode());
        return pointLogSaveReqVO;
    }

    default PointLogSaveReqVO pointLogConsumeLogSaveConvert(ConsumeSaveReqVO consumeSaveReqVO,
                                                            MerchantRespDTO merchant, MemberDO member,
                                                            MemberTypeDO memberType, LocalDate bizDate, String pointType) {

        PointLogSaveReqVO pointLogSaveReqVO = new PointLogSaveReqVO();
        pointLogSaveReqVO.setGcode(consumeSaveReqVO.getGcode());
        pointLogSaveReqVO.setHcode(consumeSaveReqVO.getHcode());
        pointLogSaveReqVO.setHname(merchant.getHname());
        pointLogSaveReqVO.setMcode(consumeSaveReqVO.getMcode());
        pointLogSaveReqVO.setName(member.getName());
        pointLogSaveReqVO.setPhone(member.getPhone());
        pointLogSaveReqVO.setMtCode(memberType.getMtCode());
        pointLogSaveReqVO.setMtName(memberType.getMtName());
        pointLogSaveReqVO.setState(NumberEnum.ONE.getNumber());
        pointLogSaveReqVO.setPointType(pointType);
        if (NumberEnum.FOUR.getNumber().equals(consumeSaveReqVO.getScene())) {
            pointLogSaveReqVO.setScene(PointSceneEnum.MEMBER_UP.getCode());
        } else {
            pointLogSaveReqVO.setScene(PointSceneEnum.CONSUME.getCode());
        }
        pointLogSaveReqVO.setOrderNo(consumeSaveReqVO.getOrderNo());
        pointLogSaveReqVO.setOrderType(CouponOrderTypeEnum.ROOM.getCode());
        pointLogSaveReqVO.setBizDate(bizDate == null ? LocalDate.now() : bizDate);
        return pointLogSaveReqVO;
    }



}
