package info.qizhi.aflower.module.member.dal.dataobject.activity;

import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.framework.common.validation.PositiveNumber;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 充值活动 DO
 *
 * <AUTHOR>
 */
@TableName(value = "member_recharge_activity", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeActivityDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码;集团创建的活动，该字段为0，门店创建的保存门店代码
     */
    private String hcode;
    /**
     * 活动代码
     */
    private String activityCode;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 储值金额
     */
    private Long storeFee;
    /**
     * 赠送金额
     */
    private Long giveFee;
    /**
     * 赠送积分
     */
    private Integer givePoint;
    /**
     * 是否集团活动;0:否 1:是
     */
    private String isG;
    /**
     * 赠送金额-有效期（天）
     */
    private Integer giveFeeIndate;
    /**
     * 赠送积分-有效期（天）
     */
    private Integer givePointIndate;
    /**
     * 赠送优惠券;赠送优惠券
     */
    @TableField(typeHandler = TicketsParameterTypeHandler.class)
    private List<TicketsParameter> tickets;
    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;
    /**
     * 适用渠道;界面上选择全部渠道时，将所有渠道代码保存
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> channels;
    /**
     * 适用会员级别;界面上选择全部级别时，将所有级别代码保存
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> mts;
    /**
     * 参加次数;0表示不限制
     */
    private Integer times;
    /**
     * 状态;0：无效 1：有效
     */
    private String state;

    @Data
    public static class TicketsParameter implements Serializable {

        @Schema(description = "卷名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String templateName;

        @Schema(description = "卷代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String templateCode;

        @Schema(description = "张数", requiredMode = Schema.RequiredMode.REQUIRED)
        @PositiveNumber
        private Integer num;

    }

    public static class TicketsParameterTypeHandler extends AbstractJsonTypeHandler<List<TicketsParameter>> {
        @Override
        protected List<TicketsParameter> parse(String json) {
            return JsonUtils.parseArray(json, TicketsParameter.class);
        }

        @Override
        protected String toJson(List<TicketsParameter> obj) {
            return JsonUtils.toJsonString(obj);
        }
    }

}