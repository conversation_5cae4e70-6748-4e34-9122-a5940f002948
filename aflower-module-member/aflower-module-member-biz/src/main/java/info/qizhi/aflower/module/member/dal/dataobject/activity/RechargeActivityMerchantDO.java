package info.qizhi.aflower.module.member.dal.dataobject.activity;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;

/**
 * 充值活动参与门店 DO
 *
 * <AUTHOR>
 */
@TableName("member_recharge_activity_merchant")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RechargeActivityMerchantDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 参与门店代码
     */
    private String hcode;
    /**
     * 活动代码
     */
    private String activityCode;

}